#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化版按键测试功能
"""

import tkinter as tk
from tkinter import ttk
import subprocess
import threading
import time
import queue

def test_simplified_key_test():
    """测试简化版按键测试"""
    print("=== 简化版按键测试 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("简化版按键测试")
    root.geometry("600x400")
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="简化版按键测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 状态显示
    status_var = tk.StringVar(value="准备就绪")
    status_label = ttk.Label(main_frame, textvariable=status_var, font=("Arial", 12))
    status_label.pack(pady=10)
    
    # 按键状态显示
    expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
    key_vars = {}
    key_labels = {}
    
    keys_frame = ttk.LabelFrame(main_frame, text="按键状态", padding="10")
    keys_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    for i, key_code in enumerate(expected_keys):
        row = i // 4
        col = i % 4
        
        key_frame = ttk.Frame(keys_frame)
        key_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
        
        key_vars[key_code] = tk.StringVar(value="未测试")
        
        ttk.Label(key_frame, text=f"按键{key_code}:", font=("Arial", 10)).pack(side=tk.LEFT)
        
        status_label = ttk.Label(
            key_frame,
            textvariable=key_vars[key_code],
            font=("Arial", 10, "bold"),
            foreground="gray"
        )
        status_label.pack(side=tk.LEFT, padx=(5, 0))
        key_labels[key_code] = status_label
    
    for i in range(4):
        keys_frame.columnconfigure(i, weight=1)
    
    # 日志显示
    log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
    log_frame.pack(fill=tk.X, pady=10)
    
    log_text = tk.Text(log_frame, height=6, wrap=tk.WORD)
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message):
        timestamp = time.strftime("%H:%M:%S")
        log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 测试变量
    process = None
    testing = False
    
    def start_key_test():
        """开始按键测试"""
        nonlocal process, testing
        
        if testing:
            return
        
        testing = True
        
        # 重置状态
        for key_code in expected_keys:
            key_vars[key_code].set("未测试")
            key_labels[key_code].configure(foreground="gray")
        
        log_message("开始按键测试...")
        status_var.set("正在启动...")
        root.update()
        
        try:
            # 启动evtest
            command = "evtest /dev/input/event5"
            log_message(f"执行命令: adb shell {command}")
            
            process = subprocess.Popen(
                ['adb', 'shell', command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            time.sleep(2)  # 等待初始化
            status_var.set("请开始按键测试...")
            log_message("设备监听已启动，请按按键...")
            root.update()
            
            # 测试变量
            detected_keys = set()
            key_press_states = {}
            start_time = time.time()
            timeout = 30  # 30秒超时
            
            # 输出队列
            output_queue = queue.Queue()
            
            def read_output():
                try:
                    while True:
                        line = process.stdout.readline()
                        if not line:
                            break
                        output_queue.put(line.strip())
                except Exception as e:
                    output_queue.put(f"ERROR: {str(e)}")
            
            # 启动读取线程
            threading.Thread(target=read_output, daemon=True).start()
            
            # 主循环 - 直接在主线程中处理
            while len(detected_keys) < 8 and (time.time() - start_time) < timeout and process.poll() is None:
                # 处理事件队列
                events_processed = 0
                while events_processed < 5:  # 每次处理最多5个事件
                    try:
                        line = output_queue.get_nowait()
                        events_processed += 1
                    except queue.Empty:
                        break
                    
                    if not line or line.startswith("ERROR:"):
                        continue
                    
                    # 记录所有事件用于调试
                    if "Event:" in line:
                        log_message(f"事件: {line}")
                    
                    # 解析按键事件
                    if "EV_KEY" in line and "KEY_" in line:
                        try:
                            key_code = None
                            value = None
                            
                            parts = line.split(", ")
                            for part in parts:
                                part = part.strip()
                                if part.startswith("code "):
                                    key_code = int(part.split("code ")[1].split(" ")[0])
                                elif part.startswith("value "):
                                    value = int(part.split("value ")[1])
                            
                            if key_code in expected_keys and value is not None:
                                if value == 1:
                                    # 按键按下
                                    key_press_states[key_code] = True
                                    key_vars[key_code].set("按下")
                                    key_labels[key_code].configure(foreground="orange")
                                    log_message(f"检测到按键{key_code}按下")
                                elif value == 0 and key_press_states.get(key_code, False):
                                    # 按键松开
                                    detected_keys.add(key_code)
                                    key_vars[key_code].set("完成")
                                    key_labels[key_code].configure(foreground="green")
                                    log_message(f"按键{key_code}测试完成")
                                    status_var.set(f"已完成 {len(detected_keys)}/8 个按键测试")
                        
                        except (ValueError, IndexError) as e:
                            log_message(f"解析失败: {str(e)}")
                            continue
                
                # 更新UI
                root.update()
                time.sleep(0.1)
            
            # 停止进程
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()
            
            # 显示最终结果
            if len(detected_keys) >= 8:
                status_var.set("测试完成!")
                log_message(f"按键测试完成 - 检测到{len(detected_keys)}个按键")
                log_message(f"检测到的按键: {sorted(detected_keys)}")
            else:
                status_var.set(f"测试失败 - 只检测到{len(detected_keys)}/8个按键")
                log_message(f"按键测试失败 - 只检测到{len(detected_keys)}个按键")
                missing_keys = set(expected_keys) - detected_keys
                log_message(f"未检测到的按键: {sorted(missing_keys)}")
            
            root.update()
            
        except Exception as e:
            log_message(f"测试出错: {str(e)}")
            status_var.set("测试出错")
        
        finally:
            testing = False
    
    def stop_test():
        """停止测试"""
        nonlocal process, testing
        if process:
            try:
                process.terminate()
                process.wait(timeout=2)
            except:
                process.kill()
            process = None
        testing = False
        status_var.set("测试已停止")
        log_message("测试已停止")
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    start_button = ttk.Button(
        button_frame,
        text="开始测试",
        command=lambda: threading.Thread(target=start_key_test, daemon=True).start()
    )
    start_button.pack(side=tk.LEFT, padx=(0, 10))
    
    stop_button = ttk.Button(
        button_frame,
        text="停止测试",
        command=stop_test
    )
    stop_button.pack(side=tk.LEFT)
    
    log_message("简化版按键测试器已就绪")
    log_message("点击'开始测试'按钮开始")
    
    # 窗口关闭时清理
    def on_closing():
        stop_test()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    print("简化版按键测试")
    print("=" * 50)
    
    # 检查ADB连接
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            print("ADB连接失败，无法进行测试")
            exit(1)
        
        devices = result.stdout.strip().split('\n')[1:]
        if not any('device' in line for line in devices):
            print("没有连接的设备，无法进行测试")
            exit(1)
        
        print("ADB连接正常，设备已连接")
        
    except Exception as e:
        print(f"检查ADB连接失败: {e}")
        exit(1)
    
    # 启动测试
    test_simplified_key_test()
