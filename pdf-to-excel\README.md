# PDF转Excel工具

这是一个简单的PDF转Excel工具，可以将PDF文件中的表格数据提取并转换为Excel格式。

## 功能特点

- 支持多页PDF文件
- 自动识别PDF中的表格
- 将表格数据保存为Excel格式
- 友好的图形用户界面
- 转换进度显示

## 安装要求

- Python 3.7+
- Java Runtime Environment (JRE) - tabula-py需要

## 安装步骤

1. 安装所需的Python包：
```bash
pip install -r requirements.txt
```

2. 确保已安装Java Runtime Environment (JRE)

## 使用方法

1. 运行程序：
```bash
python pdf_to_excel.py
```

2. 在打开的界面中：
   - 点击"选择PDF文件"按钮选择要转换的PDF文件
   - 点击"选择保存位置"按钮选择Excel文件的保存位置
   - 点击"开始转换"按钮开始转换过程

## 注意事项

- 确保PDF文件中的表格格式清晰，便于识别
- 转换大文件时可能需要较长时间
- 如果PDF中的表格格式不规范，可能影响转换效果 