[2025-07-18 12:40:37] === 开始新的测试 ===
[2025-07-18 12:40:37] 开始测试
[2025-07-18 12:40:37] 工序: 4G模组
[2025-07-18 12:40:37] 序列号: 7777777777777777
[2025-07-18 12:40:37] 开始运行 4G模组 工序测试，共 1 个项目
[2025-07-18 12:40:37] 
开始执行: 4G模组版本测试
[2025-07-18 12:40:37] 执行4G模组版本测试...（获取模组型号）
[2025-07-18 12:40:37] 监听线程已启动，等待串口数据...
[2025-07-18 12:40:39] 执行命令: adb shell "echo -e 'ATI\r' > /dev/ttyUSB0"
[2025-07-18 12:40:39] ATI指令已发送，等待串口返回模组版本信息...
[2025-07-18 12:40:39] 串口输出: ATI
[2025-07-18 12:40:39] 串口输出: 
[2025-07-18 12:40:39] 串口输出: 
[2025-07-18 12:40:39] 串口输出: Quectel
[2025-07-18 12:40:39] 串口输出: 
[2025-07-18 12:40:39] 串口输出: EG912U
[2025-07-18 12:40:39] 串口输出: 
[2025-07-18 12:40:39] 串口输出: Revision: EG912UGLAAR03A15M08
[2025-07-18 12:40:39] 检测到模组版本: EG912UGLAAR03A15M08
[2025-07-18 12:40:41] ✅ 4G模组版本测试成功，EG912UGLAAR03A15M08
[2025-07-18 12:40:41] 
测试完成 - 通过率: 1/1
[2025-07-18 12:40:41] ✅ 所有测试通过！
[2025-07-18 12:40:41] 测试记录已保存: records/7777777777777777_20250718_124041.json

