#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面布局修复
验证开始测试按钮是否可见
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

def test_layout_fix():
    """测试界面布局修复"""
    print("=== 测试界面布局修复 ===\n")
    
    print("🔧 修复内容:")
    print("  ✅ 增加窗口尺寸: 1080x900 → 1200x950")
    print("  ✅ 设置最小窗口尺寸: 1000x800")
    print("  ✅ 测试项目列表可扩展: rowconfigure(1, weight=1)")
    print("  ✅ 测试树可扩展: sticky=(W,E,N,S)")
    print("  ✅ 减少日志高度: height=20 → height=12")
    print("  ✅ 按钮区域固定在底部")
    
    print(f"\n📐 窗口布局结构:")
    print(f"  ┌─────────────────────────────────────┐")
    print(f"  │ 菜单栏                              │")
    print(f"  ├─────────────────────────────────────┤")
    print(f"  │ 工序选择 (固定高度)                  │")
    print(f"  ├─────────────────────────────────────┤")
    print(f"  │ 测试项目列表 (可扩展)                │")
    print(f"  │                                     │")
    print(f"  │                                     │")
    print(f"  ├─────────────────────────────────────┤")
    print(f"  │ 测试日志 (固定高度12行)              │")
    print(f"  │                                     │")
    print(f"  ├─────────────────────────────────────┤")
    print(f"  │ [开始测试] [停止测试] [配置管理]     │")
    print(f"  └─────────────────────────────────────┘")
    
    print(f"\n🎯 预期效果:")
    print(f"  • 窗口足够大，按钮始终可见")
    print(f"  • 测试项目列表可以根据内容扩展")
    print(f"  • 日志区域高度适中，不占用过多空间")
    print(f"  • 按钮区域固定在底部，不会被遮挡")
    print(f"  • 支持窗口缩放，最小尺寸1000x800")

def create_layout_demo():
    """创建布局演示"""
    print("\n=== 创建布局演示 ===")
    
    # 创建演示窗口
    root = tk.Tk()
    root.title("界面布局修复演示")
    root.geometry("1200x950")
    root.minsize(1000, 800)
    
    # 创建菜单栏
    menubar = tk.Menu(root)
    root.config(menu=menubar)
    
    file_menu = tk.Menu(menubar, tearoff=0)
    menubar.add_cascade(label="文件", menu=file_menu)
    file_menu.add_command(label="退出", command=root.quit)
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    # 配置网格权重
    root.columnconfigure(0, weight=1)
    root.rowconfigure(0, weight=1)
    main_frame.columnconfigure(0, weight=1)
    main_frame.rowconfigure(0, weight=0)  # 工序选择区域
    main_frame.rowconfigure(1, weight=1)  # 测试项目列表可扩展
    main_frame.rowconfigure(2, weight=0)  # 日志区域固定高度
    main_frame.rowconfigure(3, weight=0)  # 按钮区域不扩展
    
    # 工序选择区域
    process_frame = ttk.LabelFrame(main_frame, text="工序选择", padding="10")
    process_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
    process_frame.columnconfigure(1, weight=1)
    
    ttk.Label(process_frame, text="选择工序:").grid(row=0, column=0, padx=(0, 10), sticky=tk.W)
    
    process_var = tk.StringVar(value="整机半成品功能测试")
    process_combo = ttk.Combobox(
        process_frame, 
        textvariable=process_var,
        values=["整机半成品功能测试", "右手臂功能测试"],
        state="readonly",
        width=20
    )
    process_combo.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
    
    desc_var = tk.StringVar(value="(测试组装半成品各硬件功能)")
    ttk.Label(process_frame, textvariable=desc_var, foreground="gray").grid(row=0, column=2, sticky=tk.W)
    
    # 测试项目列表
    test_frame = ttk.LabelFrame(main_frame, text="测试项目", padding="10")
    test_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 5))
    test_frame.columnconfigure(0, weight=1)
    test_frame.rowconfigure(0, weight=1)
    
    # 创建测试项目树
    test_tree = ttk.Treeview(
        test_frame,
        columns=("data", "result"),
        show="tree headings",
        selectmode="browse"
    )
    
    test_tree.heading("#0", text="测试项目")
    test_tree.heading("data", text="测试数据")
    test_tree.heading("result", text="测试结果")
    test_tree.column("#0", width=250, minwidth=250)
    test_tree.column("data", width=200, minwidth=200)
    test_tree.column("result", width=100, minwidth=100, anchor="center")
    test_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    # 添加示例数据
    test_items = [
        ("设备连接状态检测", "等待测试", ""),
        ("3568版本测试", "等待测试", ""),
        ("USB关键器件检测", "等待测试", ""),
        ("CAN0测试", "等待测试", ""),
        ("GPS测试", "等待测试", ""),
        ("4G模组测试", "等待测试", ""),
        ("按键测试", "等待测试", ""),
        ("按键灯测试", "等待测试", ""),
        ("手电筒测试", "等待测试", ""),
        ("摇杆使能测试", "等待测试", ""),
        ("前摄像头测试", "等待测试", ""),
        ("光感测试", "等待测试", ""),
        ("回充摄像头测试", "等待测试", ""),
        ("喇叭测试", "等待测试", ""),
        ("蓝牙测试", "等待测试", ""),
        ("WiFi测试", "等待测试", ""),
        ("Accel", "等待测试", ""),
        ("Gyro", "等待测试", ""),
        ("Laser", "等待测试", ""),
        ("Laser2", "等待测试", ""),
        ("Odom", "等待测试", "")
    ]
    
    for name, data, result in test_items:
        test_tree.insert("", "end", text=name, values=(data, result))
    
    # 滚动条
    test_scrollbar = ttk.Scrollbar(test_frame, orient="vertical", command=test_tree.yview)
    test_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
    test_tree.configure(yscrollcommand=test_scrollbar.set)
    
    # 日志显示区域
    log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
    log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 5))
    log_frame.columnconfigure(0, weight=1)
    
    # 日志文本区域
    import tkinter.scrolledtext as scrolledtext
    log_text = scrolledtext.ScrolledText(
        log_frame,
        height=12,  # 修复后的高度
        font=("Microsoft YaHei UI", 9)
    )
    log_text.grid(row=0, column=0, sticky=(tk.W, tk.E))
    
    # 添加示例日志
    log_text.insert(tk.END, "CMG整机功能测试软件启动\n")
    log_text.insert(tk.END, "加载配置文件成功\n")
    log_text.insert(tk.END, "已初始化 21 个测试项目\n")
    log_text.insert(tk.END, "等待开始测试...\n")
    
    # 底部按钮区域
    button_frame = ttk.Frame(main_frame, padding=(0, 0))
    button_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))
    button_frame.columnconfigure(1, weight=1)
    
    # 左侧时间显示区域
    time_frame = ttk.Frame(button_frame)
    time_frame.grid(row=0, column=0, sticky=tk.W)
    
    ttk.Label(time_frame, text="开始时间: ").grid(row=0, column=0, padx=(0, 5))
    ttk.Label(time_frame, text="--:--:--").grid(row=0, column=1, padx=(0, 15))
    
    ttk.Label(time_frame, text="结束时间: ").grid(row=0, column=2, padx=(0, 5))
    ttk.Label(time_frame, text="--:--:--").grid(row=0, column=3, padx=(0, 15))
    
    ttk.Label(time_frame, text="总用时: ").grid(row=0, column=4, padx=(0, 5))
    ttk.Label(time_frame, text="--:--:--").grid(row=0, column=5, padx=(0, 15))
    
    # 右侧按钮区域
    control_frame = ttk.Frame(button_frame)
    control_frame.grid(row=0, column=2, sticky=tk.E)
    
    # 开始测试按钮
    start_btn = ttk.Button(
        control_frame,
        text="开始测试",
        width=10,
        style="TButton"
    )
    start_btn.pack(side=tk.LEFT, padx=5)
    
    # 停止测试按钮
    stop_btn = ttk.Button(
        control_frame,
        text="停止测试",
        state="disabled",
        width=10
    )
    stop_btn.pack(side=tk.LEFT, padx=(5, 5))
    
    # 配置管理按钮
    config_btn = ttk.Button(
        control_frame,
        text="配置管理",
        width=10
    )
    config_btn.pack(side=tk.LEFT, padx=(0, 0))
    
    # 状态信息
    status_frame = ttk.Frame(main_frame)
    status_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
    
    status_text = "✅ 界面布局修复完成 - 按钮区域现在始终可见"
    ttk.Label(status_frame, text=status_text, foreground="green", font=("Microsoft YaHei UI", 10, "bold")).pack()
    
    def test_resize():
        """测试窗口缩放"""
        current_size = root.geometry()
        print(f"当前窗口尺寸: {current_size}")
        
        # 测试最小尺寸
        root.geometry("1000x800")
        root.after(1000, lambda: root.geometry("1200x950"))
        print("测试窗口缩放...")
    
    # 添加测试按钮
    test_btn = ttk.Button(control_frame, text="测试缩放", command=test_resize)
    test_btn.pack(side=tk.LEFT, padx=(10, 0))
    
    print("布局演示窗口已创建")
    print("请检查:")
    print("  • 开始测试按钮是否可见")
    print("  • 窗口缩放时按钮是否始终可见")
    print("  • 测试项目列表是否可以正常滚动")
    print("  • 日志区域高度是否合适")
    
    root.mainloop()

if __name__ == "__main__":
    print("界面布局修复测试")
    print("=" * 50)
    
    # 显示修复信息
    test_layout_fix()
    
    # 创建演示
    create_layout_demo()
    
    print("\n测试完成")
