# 梯控测试上位机

这是一个用于测试梯控盒子的上位机程序，通过SSH方式连接到设备并发送指令。

## 功能特点

- 通过SSH连接到梯控设备
- 发送命令并显示返回结果
- 友好的图形用户界面
- 支持自定义连接参数

## 安装要求

- Python 3.8 或更高版本
- Windows 操作系统

## 安装步骤

1. 安装Python依赖包：
```bash
pip install -r requirements.txt
```

2. 运行程序：
```bash
python main.py
```

## 使用说明

1. 程序启动后，默认连接参数已设置为：
   - 主机：************
   - 端口：22
   - 用户名：root
   - 密码：slamware123

2. 点击"连接"按钮连接到设备
3. 在命令输入框中输入要执行的命令
4. 点击"发送"按钮发送命令
5. 命令执行结果将显示在下方的输出区域

## 注意事项

- 确保设备IP地址和网络连接正确
- 确保用户名和密码正确
- 程序关闭时会自动断开SSH连接 