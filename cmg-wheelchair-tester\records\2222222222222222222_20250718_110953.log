[2025-07-18 11:07:41] === 开始新的测试 ===
[2025-07-18 11:07:41] 开始测试
[2025-07-18 11:07:41] 工序: 右手臂功能测试
[2025-07-18 11:07:41] 序列号: 2222222222222222222
[2025-07-18 11:07:41] 开始运行 右手臂功能测试 工序测试，共 17 个项目
[2025-07-18 11:07:41] 
开始执行: 设备连接状态检测
[2025-07-18 11:07:41] 设备连接测试通过，检测到 1 个设备
[2025-07-18 11:07:41] 设备: ?	device
[2025-07-18 11:07:41] 
开始执行: 3568版本测试
[2025-07-18 11:07:41] 执行3568版本测试...
[2025-07-18 11:07:41] 读取ROM版本号...
[2025-07-18 11:07:41] 执行命令: adb shell uname -a
[2025-07-18 11:07:41] uname命令执行成功
[2025-07-18 11:07:41] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 11:07:41] ✅ 3568版本测试成功
[2025-07-18 11:07:42] ROM版本号: Jul 9 12:01:12
[2025-07-18 11:07:42] ✅ 确认为RK3568平台
[2025-07-18 11:07:42] 
开始执行: USB关键器件检测
[2025-07-18 11:07:42] 执行USB设备检测...
[2025-07-18 11:07:42] 执行命令: adb shell lsusb
[2025-07-18 11:07:42] 设备返回数据:
[2025-07-18 11:07:42] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 11:07:42] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 11:07:42] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 11:07:42] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 11:07:42] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 11:07:42] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 11:07:42] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 11:07:42] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 11:07:42] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 11:07:42] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 11:07:42] 总共解析到 9 个设备
[2025-07-18 11:07:42] ✅ 所有预期的设备ID都已找到
[2025-07-18 11:07:42] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 11:07:42] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 11:07:42] ✅ USB设备检测通过
[2025-07-18 11:07:43] 
开始执行: CAN0测试
[2025-07-18 11:07:43] 执行CAN0测试流程...
[2025-07-18 11:07:43] 执行命令: adb shell ip link set can0 down
[2025-07-18 11:07:43] CAN0已关闭
[2025-07-18 11:07:43] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 11:07:43] CAN0已启动
[2025-07-18 11:07:43] CAN监听线程已启动...
[2025-07-18 11:07:44] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 11:07:44] CAN测试数据已发送，等待监听返回...
[2025-07-18 11:07:44] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 11:07:46] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 11:07:47] 
开始执行: GPS测试
[2025-07-18 11:07:47] 执行GPS测试...
[2025-07-18 11:07:47] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 11:07:57] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 11:07:57] 
开始执行: 4G模组测试
[2025-07-18 11:07:57] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 11:07:57] 监听线程已启动，等待串口数据...
[2025-07-18 11:07:59] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 11:07:59] AT+CCID指令已发送，等待串口返回...
[2025-07-18 11:07:59] 串口输出: AT+CCID
[2025-07-18 11:07:59] 串口输出: 
[2025-07-18 11:07:59] 串口输出: 
[2025-07-18 11:07:59] 串口输出: +CCID: 89860114851012535241
[2025-07-18 11:08:01] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-18 11:08:02] 
开始执行: 按键测试
[2025-07-18 11:08:02] 开始按键测试...
[2025-07-18 11:08:03] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 11:08:39] 原始事件: Event: time 1752808119.373205, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-18 11:08:39] 解析结果: key_code=662, value=1
[2025-07-18 11:08:39] ✓ 检测到档位减(662)按下
[2025-07-18 11:08:39] 原始事件: Event: time 1752808119.622973, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-18 11:08:39] 解析结果: key_code=662, value=0
[2025-07-18 11:08:39] ✓ 档位减(662)测试通过
[2025-07-18 11:08:40] 原始事件: Event: time 1752808120.696350, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-18 11:08:40] 解析结果: key_code=658, value=1
[2025-07-18 11:08:40] ✓ 检测到喇叭键(658)按下
[2025-07-18 11:08:40] 原始事件: Event: time 1752808120.999634, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-18 11:08:40] 解析结果: key_code=658, value=0
[2025-07-18 11:08:40] ✓ 喇叭键(658)测试通过
[2025-07-18 11:08:42] 原始事件: Event: time 1752808123.099589, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-18 11:08:42] 解析结果: key_code=659, value=1
[2025-07-18 11:08:42] ✓ 检测到档位加(659)按下
[2025-07-18 11:08:42] 原始事件: Event: time 1752808123.399595, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-18 11:08:42] 解析结果: key_code=659, value=0
[2025-07-18 11:08:42] ✓ 档位加(659)测试通过
[2025-07-18 11:08:43] 原始事件: Event: time 1752808124.272998, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-18 11:08:43] 解析结果: key_code=660, value=1
[2025-07-18 11:08:43] ✓ 检测到智驾键(660)按下
[2025-07-18 11:08:44] 原始事件: Event: time 1752808124.573002, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-18 11:08:44] 解析结果: key_code=660, value=0
[2025-07-18 11:08:44] ✓ 智驾键(660)测试通过
[2025-07-18 11:08:44] 原始事件: Event: time 1752808125.146343, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-18 11:08:44] 解析结果: key_code=656, value=1
[2025-07-18 11:08:44] ✓ 检测到锁定键(656)按下
[2025-07-18 11:08:44] 原始事件: Event: time 1752808125.399596, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-18 11:08:44] 解析结果: key_code=656, value=0
[2025-07-18 11:08:44] ✓ 锁定键(656)测试通过
[2025-07-18 11:08:45] 原始事件: Event: time 1752808126.172966, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-18 11:08:45] 解析结果: key_code=657, value=1
[2025-07-18 11:08:45] ✓ 检测到SOS键(657)按下
[2025-07-18 11:08:46] 原始事件: Event: time 1752808126.446373, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-18 11:08:46] 解析结果: key_code=657, value=0
[2025-07-18 11:08:46] ✓ SOS键(657)测试通过
[2025-07-18 11:08:46] 原始事件: Event: time 1752808126.996303, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 11:08:46] 解析结果: key_code=663, value=1
[2025-07-18 11:08:46] ✓ 检测到语音键(663)按下
[2025-07-18 11:08:46] 原始事件: Event: time 1752808127.246340, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 11:08:46] 解析结果: key_code=663, value=0
[2025-07-18 11:08:46] ✓ 语音键(663)测试通过
[2025-07-18 11:08:47] 原始事件: Event: time 1752808127.873111, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-18 11:08:47] 解析结果: key_code=661, value=1
[2025-07-18 11:08:47] ✓ 检测到静音键(661)按下
[2025-07-18 11:08:47] 原始事件: Event: time 1752808128.123061, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-18 11:08:47] 解析结果: key_code=661, value=0
[2025-07-18 11:08:47] ✓ 静音键(661)测试通过
[2025-07-18 11:08:47] 按键测试完成 - 检测到8个按键
[2025-07-18 11:08:50] 
开始执行: 按键灯测试
[2025-07-18 11:08:50] 开始LED背光灯测试...
[2025-07-18 11:08:50] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:08:50] LED控制命令执行成功
[2025-07-18 11:08:50] 🔧 显示确认对话框: LED测试确认
[2025-07-18 11:08:50] 🔧 对话框窗口已创建
[2025-07-18 11:08:50] 🔧 '是'按钮已创建
[2025-07-18 11:08:50] 🔧 '否'按钮已创建
[2025-07-18 11:08:50] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 11:08:52] 👤 用户选择: 是 (测试通过)
[2025-07-18 11:08:52] 🔧 对话框关闭，用户响应: yes
[2025-07-18 11:08:52] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:08:52] LED灯已关闭
[2025-07-18 11:08:52] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-18 11:08:52] 
开始执行: 手电筒测试
[2025-07-18 11:08:52] 开始手电筒LED测试...
[2025-07-18 11:08:52] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 11:08:52] 手电筒控制命令执行成功
[2025-07-18 11:08:52] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-18 11:08:52] 🔧 对话框窗口已创建
[2025-07-18 11:08:52] 🔧 '是'按钮已创建
[2025-07-18 11:08:52] 🔧 '否'按钮已创建
[2025-07-18 11:08:52] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 11:08:53] 👤 用户选择: 是 (测试通过)
[2025-07-18 11:08:53] 🔧 对话框关闭，用户响应: yes
[2025-07-18 11:08:53] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-18 11:08:53] 手电筒已关闭
[2025-07-18 11:08:53] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-18 11:08:54] 
开始执行: 摇杆使能测试
[2025-07-18 11:08:54] 执行摇杆测试...
[2025-07-18 11:08:54] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 11:08:54] 命令执行成功
[2025-07-18 11:08:54] 返回数据: 255
[2025-07-18 11:08:54] 摇杆测试通过，值: 255
[2025-07-18 11:08:54] 
开始执行: 前摄像头测试
[2025-07-18 11:08:54] 开始执行前摄像头测试...
[2025-07-18 11:08:55] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 11:08:55] 命令执行成功，返回: 无输出
[2025-07-18 11:08:55] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-18 11:08:56] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.319652199
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-18 11:08:56] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-18 11:08:56] 拉取命令执行成功
[2025-07-18 11:08:56] 返回数据: [ 38%] /data/camera/cam0_3840x2160.jpg
[ 77%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 26.0 MB/s (168853 bytes in 0.006s)
[2025-07-18 11:08:56] 图片已保存: cam0_3840x2160.jpg
[2025-07-18 11:08:57] 用户确认结果: 通过
[2025-07-18 11:08:58] 
开始执行: 光感测试
[2025-07-18 11:08:58] 执行光感测试...
[2025-07-18 11:08:58] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 11:09:03] 光感测试完成 - 检测到数值变化
[2025-07-18 11:09:03] 数值从 5 变化到 0
[2025-07-18 11:09:04] 
开始执行: 回充摄像头测试
[2025-07-18 11:09:04] 开始执行回充摄像头测试...
[2025-07-18 11:09:04] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 11:09:04] 命令执行成功，返回: 无输出
[2025-07-18 11:09:04] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-18 11:09:05] 命令执行成功，返回: 无输出
[2025-07-18 11:09:05] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-18 11:09:05] 拉取命令执行成功
[2025-07-18 11:09:05] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 15.5 MB/s (42040 bytes in 0.003s)
[2025-07-18 11:09:05] 图片已保存: output.jpg
[2025-07-18 11:09:06] 用户确认结果: 通过
[2025-07-18 11:09:07] 
开始执行: 喇叭测试
[2025-07-18 11:09:07] 执行喇叭测试...
[2025-07-18 11:09:07] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 11:09:21] 命令执行成功
[2025-07-18 11:09:21] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-18 11:09:21] 音频播放完成
[2025-07-18 11:09:21] 
开始执行: 蓝牙测试
[2025-07-18 11:09:21] 执行蓝牙测试...
[2025-07-18 11:09:21] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 11:09:21] 执行命令: adb shell bluetoothctl show
[2025-07-18 11:09:21] 命令执行成功
[2025-07-18 11:09:21] 返回数据:
[2025-07-18 11:09:21]   Controller 24:21:5E:C0:30:4A (public)
[2025-07-18 11:09:21]   Name: CMG-1
[2025-07-18 11:09:21]   Alias: CMG-1
[2025-07-18 11:09:21]   Class: 0x006c0000 (7077888)
[2025-07-18 11:09:21]   Powered: yes
[2025-07-18 11:09:21]   PowerState: on
[2025-07-18 11:09:21]   Discoverable: no
[2025-07-18 11:09:21]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-18 11:09:21]   Pairable: yes
[2025-07-18 11:09:21]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:09:21]   Modalias: usb:v1D6Bp0246d0544
[2025-07-18 11:09:21]   Discovering: no
[2025-07-18 11:09:21]   Roles: central
[2025-07-18 11:09:21]   Roles: peripheral
[2025-07-18 11:09:21]   Advertising Features:
[2025-07-18 11:09:21]   ActiveInstances: 0x00 (0)
[2025-07-18 11:09:21]   SupportedInstances: 0x10 (16)
[2025-07-18 11:09:21]   SupportedIncludes: tx-power
[2025-07-18 11:09:21]   SupportedIncludes: appearance
[2025-07-18 11:09:21]   SupportedIncludes: local-name
[2025-07-18 11:09:21]   SupportedSecondaryChannels: 1M
[2025-07-18 11:09:21]   SupportedSecondaryChannels: 2M
[2025-07-18 11:09:21]   SupportedSecondaryChannels: Coded
[2025-07-18 11:09:21]   SupportedCapabilities Key: MaxAdvLen
[2025-07-18 11:09:21]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 11:09:21]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-18 11:09:21]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 11:09:21]   SupportedFeatures: CanSetTxPower
[2025-07-18 11:09:21]   SupportedFeatures: HardwareOffload
[2025-07-18 11:09:21]   Advertisement Monitor Features:
[2025-07-18 11:09:21]   SupportedMonitorTypes: or_patterns
[2025-07-18 11:09:21] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-18 11:09:21] 蓝牙控制器MAC地址: 24:21:5E:C0:30:4A
[2025-07-18 11:09:22] 
开始执行: 4G网络测试
[2025-07-18 11:09:22] 执行4G网络测试...
[2025-07-18 11:09:22] 第一步：关闭WiFi网络...
[2025-07-18 11:09:22] WiFi已关闭，等待4G网络连接...
[2025-07-18 11:09:25] 第二步：使用4G网络进行ping测试...
[2025-07-18 11:09:25] ping目标: www.baidu.com
[2025-07-18 11:09:25] 测试时长: 10秒
[2025-07-18 11:09:34] ping测试输出:
[2025-07-18 11:09:34]   PING www.baidu.com(2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606)) 56 data bytes
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=1 ttl=52 time=91.3 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=2 ttl=52 time=186 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=3 ttl=52 time=43.6 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=4 ttl=52 time=234 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=5 ttl=52 time=65.3 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=6 ttl=52 time=511 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=7 ttl=52 time=41.4 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=8 ttl=52 time=48.0 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=9 ttl=52 time=191 ms
[2025-07-18 11:09:34]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=10 ttl=52 time=74.3 ms
[2025-07-18 11:09:34]   --- www.baidu.com ping statistics ---
[2025-07-18 11:09:34]   10 packets transmitted, 10 received, 0% packet loss, time 9007ms
[2025-07-18 11:09:34]   rtt min/avg/max/mdev = 41.368/148.596/511.023/137.922 ms
[2025-07-18 11:09:34] 4G网络延时统计:
[2025-07-18 11:09:34]   成功ping包: 10 个
[2025-07-18 11:09:34]   平均延时: 148.6 ms
[2025-07-18 11:09:34]   最小延时: 41.4 ms
[2025-07-18 11:09:34]   最大延时: 511.0 ms
[2025-07-18 11:09:34] 4G网络测试通过
[2025-07-18 11:09:34] 第三步：重新打开WiFi网络...
[2025-07-18 11:09:34] WiFi已重新打开
[2025-07-18 11:09:37] 
开始执行: WiFi测试
[2025-07-18 11:09:37] 执行WiFi测试...
[2025-07-18 11:09:37] 第一步：连接WiFi网络...
[2025-07-18 11:09:37] 停止现有的wpa_supplicant进程...
[2025-07-18 11:09:37] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 11:09:37] 清理wpa_supplicant socket文件...
[2025-07-18 11:09:37] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 11:09:37] 关闭wlan0接口...
[2025-07-18 11:09:37] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 11:09:37] 连接WiFi网络...
[2025-07-18 11:09:37] 执行WiFi连接命令...
[2025-07-18 11:09:37] SSID: Orion_SZ_5G
[2025-07-18 11:09:41] WiFi连接命令执行完成，返回数据:
[2025-07-18 11:09:41]   Successfully initialized wpa_supplicant
[2025-07-18 11:09:41]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-18 11:09:41]   1
[2025-07-18 11:09:41]   OK
[2025-07-18 11:09:41]   OK
[2025-07-18 11:09:41]   OK
[2025-07-18 11:09:41]   deleting routers
[2025-07-18 11:09:41]   adding dns ************
[2025-07-18 11:09:41]   adding dns ***********
[2025-07-18 11:09:41]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-18 11:09:41]   SSID: Orion_SZ_5G
[2025-07-18 11:09:41]   freq: 5300
[2025-07-18 11:09:41]   RX: 1484 bytes (4 packets)
[2025-07-18 11:09:41]   TX: 1272 bytes (9 packets)
[2025-07-18 11:09:41]   signal: -59 dBm
[2025-07-18 11:09:41]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-18 11:09:41]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-18 11:09:41]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-18 11:09:41]   link/ether 24:21:5e:c0:3c:0b brd ff:ff:ff:ff:ff:ff
[2025-07-18 11:09:41]   inet 192.168.20.167/24 brd 192.168.20.255 scope global wlan0
[2025-07-18 11:09:41]   valid_lft forever preferred_lft forever
[2025-07-18 11:09:41]   inet6 fe80::ae0a:4422:f549:2225/64 scope link
[2025-07-18 11:09:41]   valid_lft forever preferred_lft forever
[2025-07-18 11:09:41] ✅ WiFi连接成功
[2025-07-18 11:09:41] 等待网络稳定...
[2025-07-18 11:09:44] 第二步：开始网络发包延时测试...
[2025-07-18 11:09:44] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-18 11:09:44] 正在进行10秒钟的网络延时测试...
[2025-07-18 11:09:53] ping命令执行成功
[2025-07-18 11:09:53] 返回数据:
[2025-07-18 11:09:53]   PING www.baidu.com(2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207)) 56 data bytes
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=1 ttl=52 time=144 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=2 ttl=52 time=154 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=3 ttl=52 time=61.1 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=4 ttl=52 time=72.8 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=5 ttl=52 time=76.2 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=6 ttl=52 time=83.7 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=7 ttl=52 time=75.3 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=8 ttl=52 time=67.6 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=9 ttl=52 time=51.2 ms
[2025-07-18 11:09:53]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=10 ttl=52 time=63.0 ms
[2025-07-18 11:09:53]   --- www.baidu.com ping statistics ---
[2025-07-18 11:09:53]   10 packets transmitted, 10 received, 0% packet loss, time 9007ms
[2025-07-18 11:09:53]   rtt min/avg/max/mdev = 51.154/84.905/153.983/33.310 ms
[2025-07-18 11:09:53] 检测到延时: 144.0 ms
[2025-07-18 11:09:53] 检测到延时: 154.0 ms
[2025-07-18 11:09:53] 检测到延时: 61.1 ms
[2025-07-18 11:09:53] 检测到延时: 72.8 ms
[2025-07-18 11:09:53] 检测到延时: 76.2 ms
[2025-07-18 11:09:53] 检测到延时: 83.7 ms
[2025-07-18 11:09:53] 检测到延时: 75.3 ms
[2025-07-18 11:09:53] 检测到延时: 67.6 ms
[2025-07-18 11:09:53] 检测到延时: 51.2 ms
[2025-07-18 11:09:53] 检测到延时: 63.0 ms
[2025-07-18 11:09:53] ✅ WiFi延时测试成功
[2025-07-18 11:09:53] 发包数量: 10 个
[2025-07-18 11:09:53] 平均延时: 84.89 ms
[2025-07-18 11:09:53] 最小延时: 51.20 ms
[2025-07-18 11:09:53] 最大延时: 154.00 ms
[2025-07-18 11:09:53] 
测试完成 - 通过率: 16/17
[2025-07-18 11:09:53] ❌ 存在测试失败项！
[2025-07-18 11:09:53] 测试记录已保存: records/2222222222222222222_20250718_110953.json

