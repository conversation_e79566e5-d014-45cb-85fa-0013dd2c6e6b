# 轮椅测试上位机软件

这是一个运行在Windows平台的图形界面上位机软件，主要用于通过adb shell指令获取测试结果，进行轮椅设备的自动化测试。

## 功能特性

### 测试项目列表
1. **设备连接状态** - 检测ADB连接状态
2. **3568版本测试** - 读取ROM版本号
3. **USB关键器件测试** - 检测系统USB设备数量和ID
4. **CAN0测试** - CAN总线通信测试
5. **GPS测试** - GPS信号检测
6. **4G模组测试** - 4G模组AT指令测试
7. **按键测试** - 手柄按键功能测试
8. **按键灯测试** - 按键LED灯测试
9. **手电筒测试** - 手电筒LED灯测试
10. **摇杆使能测试** - 摇杆功能使能测试
11. **前摄像头测试** - 前摄像头拍照测试
12. **光感测试** - 光线传感器测试
13. **回充摄像头测试** - 回充摄像头拍照测试
14. **喇叭测试** - 音频播放测试
15. **蓝牙测试** - 蓝牙控制器信息获取测试
16. **WiFi测试** - 连接WiFi并进行网络发包延时测试

### 界面功能
- **连接状态检测** - 实时显示ADB连接状态
- **测试项目列表** - 显示所有测试项目及状态
- **批量测试** - 一键执行全部测试项目
- **单独测试** - 支持单个测试项目执行
- **实时日志** - 显示详细的测试过程和结果
- **结果导出** - 支持测试结果和日志导出
- **进度显示** - 显示测试进度和状态
- **图片预览** - 摄像头测试后自动显示图片预览

## 系统要求

- Windows 10/11
- Python 3.7+
- ADB工具（需要添加到系统PATH中）

## 安装和运行

1. 确保已安装Python 3.7或更高版本
2. 确保ADB工具已安装并添加到系统PATH中
3. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
4. 运行程序：
   ```bash
   python main.py
   ```
   或双击 `run.bat` 文件

## 使用说明

### 1. 连接设备
1. 将测试设备通过USB连接到电脑
2. 在设备上启用USB调试模式
3. 点击"检测连接"按钮检查连接状态

### 2. 执行测试
- **全部测试**: 点击"开始全部测试"按钮执行所有测试项目
- **单独测试**: 双击测试项目或右键选择"单独测试"
- **停止测试**: 点击"停止测试"按钮中断当前测试

### 3. 查看结果
- **实时日志**: 在日志区域查看详细的测试过程
- **测试状态**: 在测试项目列表中查看每个项目的状态
- **结果详情**: 右键测试项目选择"查看详情"

### 4. 导出数据
- **保存日志**: 点击"保存日志"按钮保存测试日志
- **导出结果**: 点击"导出结果"按钮导出测试结果

## 测试项目详细说明

### 设备连接状态
- **命令**: `adb devices`
- **检测内容**: ADB连接状态
- **判断标准**: 设备列表中包含"device"状态

### 3568版本测试
- **命令**: `adb shell uname -a`
- **测试流程**:
  1. 执行uname -a命令获取系统信息
  2. 解析输出中的日期时间信息
  3. 验证是否为RK3568平台
- **输出示例**: `Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux`
- **提取信息**: `Jul 9 12:01:12` (编译日期时间)
- **数据解析**: 使用正则表达式匹配 `(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})` 格式
- **结果显示**: `RK3568 - Jul 9 12:01:12`
- **判断标准**: 成功解析到日期时间信息且包含rk3568标识

### USB关键器件测试
- **命令**: `adb shell lsusb`
- **预期设备**: 9个标准USB设备
- **判断标准**: 设备数量和ID完全匹配

### CAN0测试
- **命令**: 
  - `adb shell ip link set can0 down`
  - `adb shell ip link set can0 up type can bitrate 500000 loopback on`
  - `adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff`
- **预期结果**: CAN数据发送成功

### GPS测试
- **命令**: 
  - `adb shell "cat /dev/ttyUSB4 |grep GPGSV"`
  - `adb shell "cat /dev/ttyUSB4 |grep GNGSV"`
  - `adb shell "cat /dev/ttyUSB4 |grep GBGSV"`
- **预期结果**: 检测到GPS信号

### 4G模组测试
- **命令**: `adb shell "echo -e \"ATI\" > /dev/ttyUSB0"`
- **预期结果**: 返回模组版本信息

### 按键测试
- **命令**: `adb shell evtest /dev/input/event5`
- **设备**: gpio-keys设备
- **测试按键**: 8个功能按键
  - 656: 锁定键
  - 657: SOS键
  - 658: 喇叭键
  - 659: 档位加
  - 660: 智驾键
  - 661: 静音键
  - 662: 档位减
  - 663: 语音键
- **测试流程**:
  1. 自动执行evtest命令监听gpio-keys设备
  2. 进入按键监听模式
  3. 用户依次按下8个功能按键
  4. 系统检测每个按键的按下(value 1)和松开(value 0)状态变化
- **判断标准**: 检测到所有8个按键的完整按下->松开循环
- **测试界面**:
  - 实时显示每个按键的测试状态（未测试→按下→PASS）
  - 网格布局显示8个按键状态，使用功能名称标识
  - 自动统计已完成的按键数量
  - 颜色指示：灰色(未测试)→橙色(按下)→绿色(PASS)
- **超时设置**: 60秒内完成所有按键测试

### 按键灯测试
- **命令**: 
  - `adb shell "echo 255 > /sys/class/leds/lock_led/brightness"`
  - `adb shell "echo 0 > /sys/class/leds/lock_led/brightness"`
- **判断标准**: 手动观察LED状态

### 手电筒测试
- **命令**: 
  - `adb shell "echo 255 > /sys/class/leds/torch/brightness"`
  - `adb shell "echo 0 > /sys/class/leds/torch/brightness"`
- **判断标准**: 手动观察手电筒LED亮度和状态

### 摇杆使能测试
- **命令**: `adb shell cat /sys/class/leds/joystick/brightness`
- **预期值**: 255
- **判断标准**: 返回值匹配

### 前摄像头测试
- **命令**: 
  - `adb shell mkdir -p /data/camera/`
  - `adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg`
- **输出文件**: cam0_3840x2160.jpg
- **判断标准**: 手动检查图片质量
- **预览功能**: 测试完成后自动显示图片预览窗口

### 光感测试
- **命令**: `adb shell evtest /dev/input/event1`
- **预期模式**: ABS_MISC
- **测试流程**:
  1. 启动测试后会打开专门的测试窗口
  2. 窗口显示实时光感数值
  3. 按提示用手遮挡光感传感器，观察数值变化
  4. 系统自动判断测试结果
- **判断标准**: 
  - 检测到数值变化即判定为正常
  - 30秒内完成测试
- **自动化判定**:
  - 检测到数值变化：测试通过
  - 未检测到变化：测试失败
  - 超过30秒：测试超时
- **日志记录**:
  - 记录数值变化情况
  - 记录测试结果和具体原因

### 回充摄像头测试
- **命令**: 
  - `adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG`
  - `adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1`
- **输出文件**: output.jpg
- **判断标准**: 手动检查图片质量
- **预览功能**: 测试完成后自动显示图片预览窗口

### 喇叭测试
- **命令**: `adb shell tinyplay /usr/data/test.wav`
- **判断标准**: 手动确认声音质量

### 蓝牙测试
- **命令**: `adb shell bluetoothctl show`
- **测试流程**:
  1. 执行bluetoothctl show命令
  2. 获取蓝牙控制器信息
  3. 解析Controller行信息
  4. 提取MAC地址
- **预期结果**: 获取到蓝牙控制器MAC地址
- **输出格式**: `Controller 24:21:5E:C0:30:F3 (public)`
- **显示内容**: 只显示MAC地址部分 (如: 24:21:5E:C0:30:F3)
- **判断标准**: 输出包含"Controller"且能提取到有效MAC地址

### WiFi测试
- **测试流程**:
  1. **WiFi连接阶段**:
     - 停止现有wpa_supplicant进程
     - 清理socket文件和关闭wlan0接口
     - 连接指定WiFi网络 (默认: Orion_SZ_5G)
     - 获取IP地址并验证连接状态
  2. **网络延时测试阶段**:
     - 执行ping命令，发送10个数据包
     - 解析每个数据包的延时信息
     - 计算平均延时、最小延时、最大延时
     - 根据延时评估网络质量
- **WiFi连接命令序列**:
  ```bash
  adb shell "killall wpa_supplicant 2>/dev/null"
  adb shell "rm -f /var/run/wpa_supplicant/wlan0"
  adb shell "ip link set wlan0 down"
  adb shell "wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && ..."
  ```
- **延时测试命令**: `adb shell ping -c 10 www.baidu.com`
- **数据解析**: 提取 `time=10.9 ms` 中的延时值
- **输出格式**: `64 bytes from **************: icmp_seq=1 ttl=54 time=10.9 ms`
- **结果显示**: `平均延时: 10.9ms (优秀)`
- **网络质量评级**:
  - ≤50ms: 优秀
  - ≤100ms: 良好
  - ≤200ms: 一般
  - >200ms: 较差
- **WiFi配置**: 可在config.json中修改wifi_ssid和wifi_password
- **判断标准**: WiFi连接成功且能解析到延时数据

## 配置文件说明

### config.json
```json
{
    "adb_settings": {
        "timeout_seconds": 30,
        "enable_detailed_logging": true,
        "auto_save_results": true
    },
    "wifi_settings": {
        "ssid": "Orion_SZ_5G",
        "password": "Orion@2025"
    },
    "test_projects": [
        {
            "id": "test_id",
            "name": "测试名称",
            "description": "测试描述",
            "type": "测试类型",
            "command": "adb命令",
            "expected_result": "预期结果"
        }
    ]
}
```

## 项目结构

```
cmg-wheelchair-tester/
├── main.py                    # 主程序文件
├── config.json               # 配置文件
├── requirements.txt          # 依赖包列表
├── run.bat                  # 运行脚本
├── test_image_preview.py    # 图片预览功能测试脚本
└── README.md                # 项目说明文档
```

## 注意事项

- 确保测试设备已正确连接并启用USB调试模式
- ADB工具需要正确安装并配置到系统PATH中
- 测试过程中请勿断开设备连接
- 部分测试需要手动确认结果（如摄像头、音频等）
- 如遇到连接问题，请检查USB驱动是否正确安装

## 技术支持

如遇到问题，请检查：
1. 系统日志
2. 程序日志
3. ADB命令输出
4. 设备连接状态

常见问题解决方案：
- **ADB未连接**: 检查设备USB调试是否启用
- **命令执行失败**: 检查设备是否支持相应命令
- **测试超时**: 增加config.json中的timeout_seconds值
- **权限问题**: 以管理员身份运行程序 