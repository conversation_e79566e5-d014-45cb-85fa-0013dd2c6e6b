[2025-07-05 21:26:36] 开始测试设备: 1111
[2025-07-05 21:26:36] 测试数据已清除
[2025-07-05 21:26:36] 开始执行全部测试...
[2025-07-05 21:26:36] 开始测试: 设备连接状态
[2025-07-05 21:26:36] 设备连接测试失败：未检测到设备
[2025-07-05 21:26:37] 开始测试: USB关键器件测试
[2025-07-05 21:26:37] 未知的测试类型：device_match
[2025-07-05 21:26:38] 开始测试: CAN0测试
[2025-07-05 21:26:38] 执行CAN0测试流程...
[2025-07-05 21:26:38] 执行命令: adb shell ip link set can0 down
[2025-07-05 21:26:38] CAN0 down失败: error: no devices/emulators found

[2025-07-05 21:26:39] 开始测试: GPS测试
[2025-07-05 21:26:39] 执行GPS测试...
[2025-07-05 21:26:39] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-05 21:26:40] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:40] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-05 21:26:40] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:40] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-05 21:26:40] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:40] GPS信号检测失败
[2025-07-05 21:26:41] 开始测试: 4G模组测试
[2025-07-05 21:26:41] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-05 21:26:41] 监听线程已启动，等待串口数据...
[2025-07-05 21:26:43] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-05 21:26:43] AT指令发送失败: error: no devices/emulators found

[2025-07-05 21:26:44] 开始测试: 按键测试
[2025-07-05 21:26:44] 执行按键测试...
[2025-07-05 21:26:44] 请按手柄按键进行测试...
[2025-07-05 21:26:45] 开始测试: 按键灯测试
[2025-07-05 21:26:45] 开始LED背光灯测试...
[2025-07-05 21:26:45] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-05 21:26:45] LED控制命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:46] 开始测试: 手电筒测试
[2025-07-05 21:26:46] 开始手电筒LED测试...
[2025-07-05 21:26:46] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-05 21:26:46] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:47] 开始测试: 摇杆使能测试
[2025-07-05 21:26:47] 执行摇杆测试...
[2025-07-05 21:26:48] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-05 21:26:48] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:49] 开始测试: 前摄像头测试
[2025-07-05 21:26:49] 开始执行前摄像头测试...
[2025-07-05 21:26:49] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-05 21:26:49] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:50] 开始测试: 光感测试
[2025-07-05 21:26:50] 执行光感测试...
[2025-07-05 21:26:50] 执行命令: adb shell evtest /dev/input/event1
[2025-07-05 21:26:50] 光感测试失败 - 未检测到数值变化
[2025-07-05 21:26:51] 开始测试: 回充摄像头测试
[2025-07-05 21:26:51] 开始执行回充摄像头测试...
[2025-07-05 21:26:51] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-05 21:26:51] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:52] 开始测试: 喇叭测试
[2025-07-05 21:26:53] 执行喇叭测试...
[2025-07-05 21:26:53] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-05 21:26:53] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:54] 开始测试: 蓝牙测试
[2025-07-05 21:26:54] 执行蓝牙测试...
[2025-07-05 21:26:54] 启动蓝牙服务...
[2025-07-05 21:26:54] 启动蓝牙服务失败: error: no devices/emulators found

[2025-07-05 21:26:54] 开启蓝牙...
[2025-07-05 21:26:54] 开启蓝牙失败: error: no devices/emulators found

[2025-07-05 21:26:54] 执行命令: adb shell bluetoothctl devices
[2025-07-05 21:26:54] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:55] 开始测试: WiFi测试
[2025-07-05 21:26:55] 执行WiFi测试...
[2025-07-05 21:26:55] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-05 21:26:55] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:26:56] 全部测试完成
[2025-07-05 21:26:56] 测试记录已保存: records/1111_20250705_212656.json

