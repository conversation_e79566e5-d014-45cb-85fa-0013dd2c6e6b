# CMG测试配置管理工具使用指南

## 概述

配置管理工具 (`config_manager.py`) 是一个图形化界面工具，用于维护和管理CMG轮椅测试系统的工序和测试项目配置。

## 启动工具

```bash
cd cmg-wheelchair-tester
python config_manager.py
```

## 主要功能

### 1. 测试项目管理

#### 1.1 查看测试项目
- 在"测试项目管理"标签页中查看所有测试项目
- 项目列表显示：项目名称、项目ID、类型、描述
- 点击项目可在右侧查看详细信息

#### 1.2 新建测试项目
1. 点击"新建项目"按钮
2. 填写项目信息：
   - **项目名称**：显示在界面上的名称
   - **项目ID**：唯一标识符，用于程序内部引用
   - **项目类型**：从下拉列表选择测试类型
   - **项目描述**：详细描述测试内容
   - **命令**：执行的ADB命令
   - **期望模式**：预期的输出模式
   - **手动检查**：是否需要人工确认
3. 点击"保存项目"

#### 1.3 编辑测试项目
1. 在项目列表中选择要编辑的项目
2. 在右侧表单中修改信息
3. 点击"保存项目"

#### 1.4 删除测试项目
1. 在项目列表中选择要删除的项目
2. 点击"删除项目"
3. 确认删除操作

### 2. 工序管理

#### 2.1 查看工序
- 在"工序管理"标签页中查看所有工序
- 工序列表显示：工序名称、描述、包含的项目数量

#### 2.2 新建工序
1. 点击"新建工序"按钮
2. 填写工序信息：
   - **工序名称**：工序的显示名称
   - **工序描述**：工序的详细说明
3. 在项目选择区域：
   - 左侧显示所有可用的测试项目
   - 使用中间的按钮将项目添加到工序中：
     - `>>`: 添加选中的项目
     - `<<`: 移除选中的项目
     - `全选`: 添加所有项目
     - `清空`: 清空已选项目
   - 右侧显示已选择的项目
4. 点击"保存工序"

#### 2.3 编辑工序
1. 在工序列表中选择要编辑的工序
2. 修改工序信息和项目选择
3. 点击"保存工序"

#### 2.4 删除工序
1. 在工序列表中选择要删除的工序
2. 点击"删除工序"
3. 确认删除操作

### 3. 配置预览

#### 3.1 查看配置
- 在"配置预览"标签页中查看完整的JSON配置
- 可以检查配置的格式和内容

#### 3.2 刷新预览
- 点击"刷新预览"按钮更新显示

### 4. 配置文件操作

#### 4.1 保存配置
- 点击底部的"保存配置"按钮
- 自动创建备份文件
- 保存到 `config.json`

#### 4.2 重新加载
- 点击"重新加载"按钮
- 从文件重新加载配置（丢失未保存的更改）

#### 4.3 导出配置
- 点击"导出配置"按钮
- 选择保存位置和文件名
- 导出当前配置到指定文件

#### 4.4 导入配置
- 点击"导入配置"按钮
- 选择要导入的配置文件
- 覆盖当前配置

#### 4.5 恢复备份
- 点击"恢复备份"按钮
- 从备份列表中选择要恢复的备份
- 恢复到指定的备份状态

## 测试项目类型说明

| 类型 | 说明 |
|------|------|
| `connection_test` | 连接测试 |
| `rom_version_test` | ROM版本测试 |
| `usb_test` | USB设备测试 |
| `can_test` | CAN总线测试 |
| `gps_test` | GPS测试 |
| `4g_test` | 4G模组测试 |
| `key_test` | 按键测试 |
| `led_test` | LED灯测试 |
| `torch_test` | 手电筒测试 |
| `joystick_test` | 摇杆测试 |
| `camera_test` | 摄像头测试 |
| `light_sensor_test` | 光感测试 |
| `speaker_test` | 喇叭测试 |
| `bluetooth_test` | 蓝牙测试 |
| `wifi_test` | WiFi测试 |
| `sensor_fps_test` | 传感器FPS测试 |

## 工序配置示例

### 基础连接测试工序
```json
{
    "description": "设备连接和基础信息检测",
    "test_ids": ["device_connection", "rom_version_test", "usb_test"]
}
```

### 传感器测试工序
```json
{
    "description": "各类传感器FPS数据测试",
    "test_ids": ["accel_test", "gyro_test", "laser_test", "laser2_test", "odom_test"]
}
```

## 备份机制

- 每次保存配置时自动创建备份
- 备份文件存储在 `config_backups/` 目录
- 备份文件命名格式：`config_backup_YYYYMMDD_HHMMSS.json`
- 可以通过"恢复备份"功能恢复到任意备份点

## 注意事项

1. **项目ID唯一性**：确保每个测试项目的ID是唯一的
2. **工序名称唯一性**：确保每个工序的名称是唯一的
3. **定期备份**：建议定期导出配置文件作为额外备份
4. **测试验证**：修改配置后建议运行测试验证配置正确性
5. **权限要求**：确保对配置文件和备份目录有读写权限

## 故障排除

### 配置文件损坏
1. 使用"恢复备份"功能恢复到之前的状态
2. 或者使用"导入配置"导入备份的配置文件

### 界面显示异常
1. 点击"重新加载"刷新界面
2. 重启配置管理工具

### 保存失败
1. 检查文件权限
2. 确保磁盘空间充足
3. 检查配置内容是否有效

## 最佳实践

1. **规范命名**：使用清晰、一致的命名规则
2. **分类管理**：按功能模块组织测试项目和工序
3. **定期维护**：定期检查和更新配置
4. **版本控制**：重要更改前先导出备份
5. **文档记录**：记录重要的配置更改和原因

## 配置文件结构

```json
{
    "adb_settings": {
        "timeout_seconds": 30,
        "enable_detailed_logging": true,
        "auto_save_results": true
    },
    "wifi_settings": {
        "ssid": "Orion_SZ_5G",
        "password": "Orion@2025"
    },
    "work_processes": {
        "工序名称": {
            "description": "工序描述",
            "test_ids": ["test_id1", "test_id2"]
        }
    },
    "test_projects": [
        {
            "id": "test_id",
            "name": "测试名称",
            "description": "测试描述",
            "type": "测试类型",
            "command": "执行命令",
            "expected_pattern": "期望模式",
            "manual_check": false
        }
    ]
}
```
