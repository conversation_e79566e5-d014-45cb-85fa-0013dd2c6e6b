#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试右手臂功能测试工序的单项测试功能
验证：
1. 右手臂工序的测试项目是否正确加载
2. 序列号输入取消后，单项测试功能是否正常
3. 工序切换功能是否正常工作
"""

import subprocess
import time
import sys
import os
import json

def check_right_arm_process_config():
    """检查右手臂工序配置"""
    print("=== 检查右手臂工序配置 ===")
    
    if not os.path.exists('config.json'):
        print("❌ config.json 文件不存在")
        return False
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        work_processes = config.get("work_processes", {})
        test_projects = config.get("test_projects", [])
        
        # 检查右手臂工序
        if "右手臂功能测试" not in work_processes:
            print("❌ 未找到'右手臂功能测试'工序")
            return False
        
        right_arm_process = work_processes["右手臂功能测试"]
        print("✅ 找到右手臂功能测试工序:")
        print(f"   描述: {right_arm_process['description']}")
        print(f"   测试项目数量: {len(right_arm_process['test_ids'])}")
        
        # 检查测试项目
        test_ids = right_arm_process["test_ids"]
        print(f"\n📋 右手臂工序包含的测试项目:")
        
        valid_projects = []
        for i, test_id in enumerate(test_ids, 1):
            # 查找对应的测试项目
            project_found = False
            for project in test_projects:
                if project["id"] == test_id:
                    print(f"   {i:2d}. {project['name']} ({test_id})")
                    valid_projects.append(project)
                    project_found = True
                    break
            
            if not project_found:
                print(f"   {i:2d}. ❌ 未找到测试项目: {test_id}")
        
        print(f"\n✅ 有效测试项目: {len(valid_projects)}/{len(test_ids)}")
        
        if len(valid_projects) == len(test_ids):
            print("✅ 所有测试项目配置正确")
            return True
        else:
            print("⚠️ 部分测试项目配置有问题")
            return False
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {str(e)}")
        return False

def test_right_arm_single_test():
    """测试右手臂工序的单项测试功能"""
    print("\n=== 测试右手臂工序单项测试功能 ===")
    
    print("📋 测试场景:")
    print("1. 程序启动，选择'右手臂功能测试'工序")
    print("2. 点击'开始测试'，弹出序列号输入对话框")
    print("3. 点击'取消'按钮，返回主界面")
    print("4. 双击测试项目列表中的任意项目")
    print("5. 验证单项测试是否正常执行")
    
    print("\n🔍 可能的问题原因:")
    print("1. 工序特定的初始化问题")
    print("2. 测试项目结果对象未正确创建")
    print("3. 焦点问题（已修复）")
    print("4. 工序切换后状态不一致")
    
    print("\n🔧 调试步骤:")
    print("1. 检查日志输出是否有'🔧 run_single_test 方法被调用'")
    print("2. 检查是否有'已初始化 X 个测试项目'日志")
    print("3. 检查test_results字典是否包含对应的测试项目")
    print("4. 检查双击事件是否正确触发")
    
    return True

def provide_debugging_guide():
    """提供调试指导"""
    print("\n=== 调试指导 ===")
    
    print("🔧 手动调试步骤:")
    
    print("\n步骤1: 验证工序配置")
    print("  a) 检查config.json中'右手臂功能测试'的配置")
    print("  b) 确认test_ids列表包含正确的测试项目ID")
    print("  c) 确认所有test_id都有对应的test_projects配置")
    
    print("\n步骤2: 验证程序启动")
    print("  a) 运行 python main.py")
    print("  b) 选择'右手臂功能测试'工序")
    print("  c) 观察日志是否显示'已初始化 X 个测试项目'")
    print("  d) 确认测试项目列表显示正确的项目")
    
    print("\n步骤3: 验证序列号取消后的状态")
    print("  a) 点击'开始测试'按钮")
    print("  b) 在序列号输入对话框中点击'取消'")
    print("  c) 观察日志是否显示取消相关信息")
    print("  d) 确认返回主界面后界面状态正常")
    
    print("\n步骤4: 验证单项测试功能")
    print("  a) 双击测试项目列表中的任意项目")
    print("  b) 观察日志是否输出'🔧 run_single_test 方法被调用'")
    print("  c) 观察是否显示'🔧 选中的项目: (项目ID)'")
    print("  d) 观察是否正常执行单项测试")
    
    print("\n步骤5: 验证工序切换功能")
    print("  a) 通过菜单'配置'->'选择工序'重新选择工序")
    print("  b) 选择其他工序（如'整机半成品功能测试'）")
    print("  c) 观察测试项目列表是否更新")
    print("  d) 测试双击功能是否正常")
    print("  e) 再次切换回'右手臂功能测试'")
    print("  f) 验证功能是否恢复正常")
    
    return True

def analyze_potential_issues():
    """分析潜在问题"""
    print("\n=== 潜在问题分析 ===")
    
    print("🔍 可能的问题类型:")
    
    print("\n1. 工序特定问题:")
    print("   - 右手臂工序的测试项目配置有误")
    print("   - 某些测试项目ID不存在或重复")
    print("   - 工序描述或配置格式问题")
    
    print("\n2. 初始化时序问题:")
    print("   - init_test_projects()调用时机不对")
    print("   - test_results字典未正确初始化")
    print("   - 工序切换后状态未正确更新")
    
    print("\n3. 焦点管理问题:")
    print("   - 序列号对话框取消后焦点未恢复（已修复）")
    print("   - 主窗口事件响应异常（已修复）")
    
    print("\n4. 界面状态问题:")
    print("   - 测试项目列表显示不正确")
    print("   - 双击事件绑定失效")
    print("   - 右键菜单无法显示")
    
    print("\n🔧 排查优先级:")
    print("1. 首先检查配置文件是否正确")
    print("2. 然后检查程序启动日志")
    print("3. 再检查双击事件是否触发")
    print("4. 最后检查测试执行逻辑")
    
    return True

def provide_solutions():
    """提供解决方案"""
    print("\n=== 解决方案 ===")
    
    print("🔧 针对不同问题的解决方案:")
    
    print("\n方案1: 配置文件问题")
    print("  - 检查config.json中右手臂工序的test_ids")
    print("  - 确保所有test_id都有对应的test_projects配置")
    print("  - 验证JSON格式是否正确")
    
    print("\n方案2: 初始化问题")
    print("  - 在工序选择后强制调用init_test_projects()")
    print("  - 在序列号取消后检查test_results状态")
    print("  - 添加更多调试日志输出")
    
    print("\n方案3: 事件绑定问题")
    print("  - 检查双击事件绑定是否正确")
    print("  - 验证事件处理函数是否被调用")
    print("  - 确认焦点状态是否正常")
    
    print("\n方案4: 状态同步问题")
    print("  - 工序切换后重新初始化所有相关状态")
    print("  - 确保界面显示与内部状态一致")
    print("  - 添加状态验证机制")
    
    print("\n🎯 推荐的修复步骤:")
    print("1. 添加菜单'配置'->'选择工序'选项（已完成）")
    print("2. 实现reselect_work_process方法（已完成）")
    print("3. 在工序切换后强制更新界面状态")
    print("4. 添加更详细的调试日志")
    print("5. 验证所有工序的配置正确性")
    
    return True

def main():
    """主测试函数"""
    print("右手臂功能测试工序单项测试验证")
    print("=" * 50)
    
    # 检查配置
    config_ok = check_right_arm_process_config()
    
    # 测试功能
    test_ok = test_right_arm_single_test()
    
    # 调试指导
    debug_ok = provide_debugging_guide()
    
    # 问题分析
    analysis_ok = analyze_potential_issues()
    
    # 解决方案
    solution_ok = provide_solutions()
    
    print("\n📊 验证结果:")
    print(f"配置检查: {'✅' if config_ok else '❌'}")
    print(f"功能测试: {'✅' if test_ok else '❌'}")
    print(f"调试指导: {'✅' if debug_ok else '❌'}")
    print(f"问题分析: {'✅' if analysis_ok else '❌'}")
    print(f"解决方案: {'✅' if solution_ok else '❌'}")
    
    if config_ok:
        print("\n🎉 右手臂工序配置检查通过！")
        print("- 工序配置正确")
        print("- 测试项目完整")
        print("- 可以进行功能测试")
    else:
        print("\n⚠️ 右手臂工序配置有问题")
        print("- 请检查config.json文件")
        print("- 确认测试项目配置完整")
    
    print("\n📝 下一步操作:")
    print("1. 运行 python main.py 进行实际测试")
    print("2. 选择'右手臂功能测试'工序")
    print("3. 按照调试步骤逐一验证")
    print("4. 如果问题仍然存在，请提供详细的日志输出")

if __name__ == "__main__":
    main()
