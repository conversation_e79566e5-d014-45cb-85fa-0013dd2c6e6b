#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试单项测试功能
检查可能导致单项测试无法工作的问题
"""

import json
import sys
import os

def debug_single_test():
    """调试单项测试功能"""
    print("=== 调试单项测试功能 ===")
    
    # 1. 检查配置文件
    print("\n1. 检查配置文件...")
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        test_projects = config.get("test_projects", [])
        work_processes = config.get("work_processes", {})
        
        print(f"✅ 配置文件加载成功")
        print(f"  测试项目数量: {len(test_projects)}")
        print(f"  工序数量: {len(work_processes)}")
        
        if not test_projects:
            print("❌ 没有测试项目")
            return False
        
        if not work_processes:
            print("❌ 没有工序配置")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False
    
    # 2. 检查工序和测试项目的关联
    print("\n2. 检查工序和测试项目的关联...")
    for process_name, process_config in work_processes.items():
        test_ids = process_config.get("test_ids", [])
        print(f"工序 '{process_name}': {len(test_ids)} 个测试项目")
        
        # 检查测试项目是否存在
        missing_projects = []
        for test_id in test_ids:
            found = False
            for project in test_projects:
                if project["id"] == test_id:
                    found = True
                    break
            if not found:
                missing_projects.append(test_id)
        
        if missing_projects:
            print(f"  ❌ 缺少测试项目: {missing_projects}")
        else:
            print(f"  ✅ 所有测试项目都存在")
    
    # 3. 检查测试项目的基本信息
    print("\n3. 检查测试项目的基本信息...")
    required_fields = ["id", "name", "type"]
    
    for i, project in enumerate(test_projects[:5]):  # 只检查前5个
        print(f"项目 {i+1}: {project.get('name', 'Unknown')}")
        
        missing_fields = []
        for field in required_fields:
            if field not in project:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"  ❌ 缺少字段: {missing_fields}")
        else:
            print(f"  ✅ 基本字段完整")
            print(f"    ID: {project['id']}")
            print(f"    类型: {project['type']}")
    
    # 4. 检查主程序文件
    print("\n4. 检查主程序文件...")
    try:
        with open('main.py', 'r', encoding='utf-8') as f:
            main_content = f.read()
        
        # 检查关键方法是否存在
        key_methods = [
            "def run_single_test",
            "def show_test_menu",
            "bind(\"<Double-Button-1>\"",
            "bind(\"<Button-3>\""
        ]
        
        for method in key_methods:
            if method in main_content:
                print(f"  ✅ 找到: {method}")
            else:
                print(f"  ❌ 缺少: {method}")
        
    except Exception as e:
        print(f"❌ 主程序文件检查失败: {e}")
        return False
    
    # 5. 检查可能的问题
    print("\n5. 检查可能的问题...")
    
    # 检查是否有重复的测试项目ID
    test_ids = [project["id"] for project in test_projects]
    duplicate_ids = []
    for test_id in test_ids:
        if test_ids.count(test_id) > 1 and test_id not in duplicate_ids:
            duplicate_ids.append(test_id)
    
    if duplicate_ids:
        print(f"  ❌ 发现重复的测试项目ID: {duplicate_ids}")
    else:
        print(f"  ✅ 没有重复的测试项目ID")
    
    # 检查是否有重复的测试项目名称
    test_names = [project["name"] for project in test_projects]
    duplicate_names = []
    for test_name in test_names:
        if test_names.count(test_name) > 1 and test_name not in duplicate_names:
            duplicate_names.append(test_name)
    
    if duplicate_names:
        print(f"  ❌ 发现重复的测试项目名称: {duplicate_names}")
    else:
        print(f"  ✅ 没有重复的测试项目名称")
    
    # 6. 模拟单项测试流程
    print("\n6. 模拟单项测试流程...")
    
    # 选择第一个工序
    if work_processes:
        process_name = list(work_processes.keys())[0]
        test_ids = work_processes[process_name]["test_ids"]
        
        print(f"选择工序: {process_name}")
        print(f"测试项目数量: {len(test_ids)}")
        
        # 模拟获取过滤后的测试项目
        filtered_projects = []
        for project in test_projects:
            if project["id"] in test_ids:
                filtered_projects.append(project)
        
        print(f"过滤后的项目数量: {len(filtered_projects)}")
        
        if filtered_projects:
            # 模拟选择第一个项目进行单项测试
            test_project = filtered_projects[0]
            print(f"模拟测试项目: {test_project['name']}")
            print(f"  ID: {test_project['id']}")
            print(f"  类型: {test_project['type']}")
            
            # 检查测试类型是否支持
            supported_types = [
                "connection_test", "rom_version_test", "usb_test", "can_test",
                "gps_test", "4g_test", "key_test", "led_test", "joystick_test",
                "camera_test", "light_sensor_test", "speaker_test", "bluetooth_test",
                "4g_network_test", "wifi_test", "sensor_fps_test", "torch_test"
            ]
            
            if test_project['type'] in supported_types:
                print(f"  ✅ 测试类型受支持")
            else:
                print(f"  ❌ 测试类型不受支持: {test_project['type']}")
        
    print("\n=== 调试完成 ===")
    return True

def check_main_program():
    """检查主程序是否可以正常启动"""
    print("\n=== 检查主程序启动 ===")
    
    try:
        # 尝试导入主程序的关键部分
        import tkinter as tk
        from tkinter import ttk
        import subprocess
        import threading
        import json
        import datetime
        import re
        import os
        import time
        
        print("✅ 所有必需的模块都可以导入")
        
        # 检查ADB是否可用
        try:
            result = subprocess.run(['adb', 'version'], capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                print("✅ ADB可用")
            else:
                print("❌ ADB不可用")
        except FileNotFoundError:
            print("❌ ADB未安装或不在PATH中")
        except Exception as e:
            print(f"❌ ADB检查失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def provide_solutions():
    """提供可能的解决方案"""
    print("\n=== 可能的解决方案 ===")
    
    solutions = [
        "1. 重启主程序",
        "2. 检查配置文件是否正确",
        "3. 确保选择了正确的工序",
        "4. 检查测试项目列表是否正常显示",
        "5. 尝试右键菜单而不是双击",
        "6. 检查是否有错误日志",
        "7. 重新加载配置文件",
        "8. 检查ADB连接状态"
    ]
    
    for solution in solutions:
        print(solution)
    
    print("\n🔧 调试步骤:")
    print("1. 运行主程序: python main.py")
    print("2. 选择工序")
    print("3. 尝试双击测试项目")
    print("4. 如果不工作，尝试右键菜单")
    print("5. 查看日志输出是否有错误信息")

if __name__ == "__main__":
    print("单项测试功能调试工具")
    print("=" * 50)
    
    # 检查当前目录
    if not os.path.exists('config.json'):
        print("❌ 当前目录没有config.json文件")
        print("请在cmg-wheelchair-tester目录下运行此脚本")
        sys.exit(1)
    
    if not os.path.exists('main.py'):
        print("❌ 当前目录没有main.py文件")
        print("请在cmg-wheelchair-tester目录下运行此脚本")
        sys.exit(1)
    
    # 执行调试
    success = debug_single_test()
    
    if success:
        check_main_program()
    
    provide_solutions()
    
    print("\n调试完成")
