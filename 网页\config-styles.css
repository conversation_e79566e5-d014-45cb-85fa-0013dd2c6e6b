/* 选配页面布局 */
.config-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-top: 2rem;
}

/* 左侧选配区域 */
.config-sidebar {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
}

.config-section {
    margin-bottom: 2rem;
}

.config-section h2 {
    color: #1a1a1a;
    font-size: 1.4rem;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
    font-weight: 600;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.option-item {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 0.8rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.option-item:hover {
    background: rgba(255, 255, 255, 1);
    border-color: rgba(0, 0, 0, 0.2);
}

.option-item input[type="radio"],
.option-item input[type="checkbox"] {
    width: 1.2rem;
    height: 1.2rem;
    cursor: pointer;
}

.option-item span {
    color: #333333;
    font-size: 1rem;
    font-weight: 500;
}

/* 右侧汇总区域 */
.config-summary {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 4px 30px rgba(0, 0, 0, 0.2);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.summary-header h2 {
    color: #1a1a1a;
    font-size: 1.4rem;
    font-weight: 600;
}

.btn-save {
    padding: 0.8rem 1.5rem;
    background: #4CAF50;
    color: #ffffff;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.btn-save:hover {
    background: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.summary-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.summary-section {
    background: rgba(255, 255, 255, 0.9);
    padding: 1.5rem;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

.summary-section h3 {
    color: #1a1a1a;
    font-size: 1.2rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.summary-section p,
.summary-section li {
    color: #333333;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.summary-section ul {
    list-style: none;
    padding: 0;
}

.summary-section li {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.summary-section li::before {
    content: "•";
    color: #4CAF50;
    font-weight: bold;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .config-layout {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .config-sidebar,
    .config-summary {
        padding: 1.5rem;
    }

    .summary-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .config-section h2,
    .summary-header h2 {
        font-size: 1.2rem;
    }

    .option-item {
        padding: 0.6rem;
    }

    .summary-section {
        padding: 1rem;
    }
} 