[2025-07-05 14:12:29] 开始测试设备: 1111
[2025-07-05 14:12:29] 测试数据已清除
[2025-07-05 14:12:29] 开始执行全部测试...
[2025-07-05 14:12:29] 开始测试: USB关键器件测试
[2025-07-05 14:12:29] 执行USB设备检测...
[2025-07-05 14:12:29] 执行命令: adb shell lsusb
[2025-07-05 14:12:29] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:30] 开始测试: CAN0测试
[2025-07-05 14:12:30] 执行CAN0测试流程...
[2025-07-05 14:12:30] 执行命令: adb shell ip link set can0 down
[2025-07-05 14:12:30] CAN0 down失败: error: no devices/emulators found

[2025-07-05 14:12:31] 开始测试: GPS测试
[2025-07-05 14:12:31] 执行GPS测试...
[2025-07-05 14:12:31] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-05 14:12:31] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:31] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-05 14:12:31] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:31] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-05 14:12:32] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:32] GPS信号检测失败
[2025-07-05 14:12:33] 开始测试: 4G模组测试
[2025-07-05 14:12:33] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-05 14:12:33] 监听线程已启动，等待串口数据...
[2025-07-05 14:12:35] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-05 14:12:35] AT指令发送失败: error: no devices/emulators found

[2025-07-05 14:12:36] 开始测试: 按键测试
[2025-07-05 14:12:36] 执行按键测试...
[2025-07-05 14:12:36] 请按手柄按键进行测试...
[2025-07-05 14:12:37] 开始测试: 按键灯测试
[2025-07-05 14:12:37] 开始LED背光灯测试...
[2025-07-05 14:12:37] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-05 14:12:37] LED控制命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:38] 开始测试: 手电筒测试
[2025-07-05 14:12:38] 开始手电筒LED测试...
[2025-07-05 14:12:38] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-05 14:12:38] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:39] 开始测试: 摇杆使能测试
[2025-07-05 14:12:39] 执行摇杆测试...
[2025-07-05 14:12:39] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-05 14:12:39] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:40] 开始测试: 前摄像头测试
[2025-07-05 14:12:40] 开始执行前摄像头测试...
[2025-07-05 14:12:41] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-05 14:12:41] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:42] 开始测试: 光感测试
[2025-07-05 14:12:42] 执行光感测试...
[2025-07-05 14:12:42] 执行命令: adb shell evtest /dev/input/event1
[2025-07-05 14:12:42] 光感测试失败 - 未检测到数值变化
[2025-07-05 14:12:43] 开始测试: 回充摄像头测试
[2025-07-05 14:12:43] 开始执行回充摄像头测试...
[2025-07-05 14:12:43] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-05 14:12:43] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:44] 开始测试: 喇叭测试
[2025-07-05 14:12:44] 执行喇叭测试...
[2025-07-05 14:12:44] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-05 14:12:44] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:45] 开始测试: 蓝牙测试
[2025-07-05 14:12:45] 执行蓝牙测试...
[2025-07-05 14:12:45] 启动蓝牙服务...
[2025-07-05 14:12:45] 启动蓝牙服务失败: error: no devices/emulators found

[2025-07-05 14:12:45] 开启蓝牙...
[2025-07-05 14:12:45] 开启蓝牙失败: error: no devices/emulators found

[2025-07-05 14:12:45] 执行命令: adb shell bluetoothctl devices
[2025-07-05 14:12:45] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:47] 开始测试: WiFi测试
[2025-07-05 14:12:47] 执行WiFi测试...
[2025-07-05 14:12:47] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-05 14:12:47] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:12:48] 全部测试完成
[2025-07-05 14:12:48] 测试记录已保存: records/1111_20250705_141248.json

