#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工序切换功能
验证测试项目清单是否根据选择的工序正确变化
"""

import tkinter as tk
from tkinter import ttk
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"test_projects": [], "work_processes": {}}

def test_process_filtering():
    """测试工序过滤逻辑"""
    print("=== 测试工序过滤逻辑 ===")
    
    config = load_config()
    test_projects = config.get("test_projects", [])
    work_processes = config.get("work_processes", {})
    
    print(f"总测试项目数: {len(test_projects)}")
    print(f"工序数量: {len(work_processes)}")
    
    # 测试每个工序的过滤结果
    for process_name, process_config in work_processes.items():
        test_ids = process_config.get("test_ids", [])
        
        # 过滤测试项目
        filtered_projects = []
        for project in test_projects:
            if project["id"] in test_ids:
                filtered_projects.append(project)
        
        print(f"\n工序: {process_name}")
        print(f"  描述: {process_config['description']}")
        print(f"  配置的测试ID数: {len(test_ids)}")
        print(f"  实际匹配的项目数: {len(filtered_projects)}")
        
        # 检查是否有无效的测试ID
        valid_project_ids = [p["id"] for p in test_projects]
        invalid_ids = [tid for tid in test_ids if tid not in valid_project_ids]
        
        if invalid_ids:
            print(f"  ❌ 无效的测试ID: {invalid_ids}")
        else:
            print(f"  ✅ 所有测试ID有效")
        
        # 显示匹配的项目
        if filtered_projects:
            print(f"  包含的测试项目:")
            for project in filtered_projects:
                print(f"    - {project['name']} ({project['id']})")

def test_process_switching_ui():
    """测试工序切换UI"""
    print("\n=== 测试工序切换UI ===")
    
    config = load_config()
    test_projects = config.get("test_projects", [])
    work_processes = config.get("work_processes", {})
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("工序切换功能测试")
    root.geometry("900x700")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="工序切换功能测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 工序选择区域
    process_frame = ttk.LabelFrame(main_frame, text="工序选择", padding="10")
    process_frame.pack(fill=tk.X, pady=(0, 10))
    process_frame.columnconfigure(1, weight=1)
    
    # 工序选择标签
    ttk.Label(process_frame, text="选择工序:").grid(row=0, column=0, padx=(0, 10), sticky=tk.W)
    
    # 工序选择下拉框
    selected_process = tk.StringVar(value="完整测试")
    process_combo = ttk.Combobox(
        process_frame, 
        textvariable=selected_process,
        values=list(work_processes.keys()),
        state="readonly",
        width=20
    )
    process_combo.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
    
    # 工序描述标签
    process_desc_var = tk.StringVar()
    desc_label = ttk.Label(process_frame, textvariable=process_desc_var, foreground="gray")
    desc_label.grid(row=0, column=2, sticky=tk.W)
    
    # 统计信息
    stats_var = tk.StringVar()
    stats_label = ttk.Label(process_frame, textvariable=stats_var, font=("Arial", 10, "bold"))
    stats_label.grid(row=1, column=0, columnspan=3, pady=(10, 0), sticky=tk.W)
    
    # 测试项目显示区域
    project_frame = ttk.LabelFrame(main_frame, text="测试项目列表", padding="10")
    project_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    # 创建测试项目列表
    project_tree = ttk.Treeview(
        project_frame,
        columns=("id", "type", "description"),
        show="tree headings",
        height=15
    )
    
    project_tree.heading("#0", text="项目名称")
    project_tree.heading("id", text="项目ID")
    project_tree.heading("type", text="类型")
    project_tree.heading("description", text="描述")
    
    project_tree.column("#0", width=150)
    project_tree.column("id", width=150)
    project_tree.column("type", width=120)
    project_tree.column("description", width=250)
    
    # 添加滚动条
    scrollbar = ttk.Scrollbar(project_frame, orient=tk.VERTICAL, command=project_tree.yview)
    project_tree.configure(yscrollcommand=scrollbar.set)
    
    project_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 对比显示区域
    compare_frame = ttk.LabelFrame(main_frame, text="工序对比", padding="10")
    compare_frame.pack(fill=tk.X, pady=(0, 10))
    
    compare_text = tk.Text(compare_frame, height=8, font=("Consolas", 9))
    compare_scrollbar = ttk.Scrollbar(compare_frame, orient=tk.VERTICAL, command=compare_text.yview)
    compare_text.configure(yscrollcommand=compare_scrollbar.set)
    
    compare_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    compare_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def get_filtered_projects(process_name):
        """获取过滤后的项目"""
        if process_name not in work_processes:
            return test_projects
        
        selected_test_ids = work_processes[process_name]["test_ids"]
        filtered_projects = []
        
        for project in test_projects:
            if project["id"] in selected_test_ids:
                filtered_projects.append(project)
        
        return filtered_projects
    
    def update_process_description():
        """更新工序描述"""
        process_name = selected_process.get()
        if process_name in work_processes:
            desc = work_processes[process_name]["description"]
            process_desc_var.set(f"({desc})")
        else:
            process_desc_var.set("")
    
    def update_project_list():
        """更新测试项目列表"""
        # 清空现有项目
        for item in project_tree.get_children():
            project_tree.delete(item)
        
        process_name = selected_process.get()
        filtered_projects = get_filtered_projects(process_name)
        
        # 添加到树中
        for project in filtered_projects:
            project_tree.insert("", "end", 
                              text=project["name"],
                              values=(project["id"], project["type"], project["description"]))
        
        # 更新统计信息
        total_projects = len(test_projects)
        filtered_count = len(filtered_projects)
        stats_var.set(f"显示 {filtered_count}/{total_projects} 个测试项目")
    
    def update_comparison():
        """更新工序对比"""
        compare_text.delete(1.0, tk.END)
        
        comparison_info = "工序项目数量对比:\n\n"
        
        for process_name, process_config in work_processes.items():
            filtered_projects = get_filtered_projects(process_name)
            current_mark = " ← 当前选择" if process_name == selected_process.get() else ""
            comparison_info += f"{process_name}: {len(filtered_projects)} 个项目{current_mark}\n"
        
        comparison_info += f"\n总项目数: {len(test_projects)} 个\n"
        
        # 显示当前工序的详细信息
        current_process = selected_process.get()
        if current_process in work_processes:
            comparison_info += f"\n当前工序 '{current_process}' 包含的项目:\n"
            filtered_projects = get_filtered_projects(current_process)
            for i, project in enumerate(filtered_projects, 1):
                comparison_info += f"{i:2d}. {project['name']} ({project['id']})\n"
        
        compare_text.insert(tk.END, comparison_info)
    
    def on_process_changed(event=None):
        """工序选择改变事件"""
        update_process_description()
        update_project_list()
        update_comparison()
        print(f"切换到工序: {selected_process.get()}")
    
    # 绑定事件
    process_combo.bind("<<ComboboxSelected>>", on_process_changed)
    
    # 初始化显示
    update_process_description()
    update_project_list()
    update_comparison()
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    def test_all_processes():
        """测试所有工序"""
        print("\n=== 测试所有工序切换 ===")
        for process_name in work_processes.keys():
            selected_process.set(process_name)
            on_process_changed()
            filtered_projects = get_filtered_projects(process_name)
            print(f"工序 '{process_name}': {len(filtered_projects)} 个项目")
        
        # 回到完整测试
        selected_process.set("完整测试")
        on_process_changed()
        print("测试完成，已回到完整测试")
    
    def show_filtering_logic():
        """显示过滤逻辑"""
        logic_window = tk.Toplevel(root)
        logic_window.title("过滤逻辑说明")
        logic_window.geometry("600x400")
        
        logic_text = """工序过滤逻辑说明：

1. 工序配置结构：
   work_processes: {
       "工序名称": {
           "description": "工序描述",
           "test_ids": ["test_id1", "test_id2", ...]
       }
   }

2. 过滤算法：
   def get_filtered_test_projects(selected_process):
       if selected_process not in work_processes:
           return all_test_projects  # 返回所有项目
       
       selected_test_ids = work_processes[selected_process]["test_ids"]
       filtered_projects = []
       
       for project in all_test_projects:
           if project["id"] in selected_test_ids:
               filtered_projects.append(project)
       
       return filtered_projects

3. 切换流程：
   a) 用户选择工序
   b) 触发 on_process_changed 事件
   c) 调用 get_filtered_test_projects 获取过滤后的项目
   d) 调用 init_test_projects 重新初始化测试结果
   e) 调用 init_test_tree 更新界面显示

4. 关键点：
   • 测试结果存储只包含当前工序的项目
   • 界面显示只显示当前工序的项目
   • 运行测试时只运行当前工序的项目"""
        
        text_widget = tk.Text(logic_window, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(logic_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10, padx=(0, 10))
        
        text_widget.insert(tk.END, logic_text)
        text_widget.config(state=tk.DISABLED)
    
    ttk.Button(button_frame, text="测试所有工序", command=test_all_processes).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="过滤逻辑说明", command=show_filtering_logic).pack(side=tk.LEFT, padx=5)
    
    print("工序切换测试UI已创建")
    root.mainloop()

if __name__ == "__main__":
    print("工序切换功能测试")
    print("=" * 50)
    
    # 测试过滤逻辑
    test_process_filtering()
    
    # 测试UI
    test_process_switching_ui()
    
    print("\n测试完成")
