import paramiko
import time
import os
from pathlib import Path

class ElevatorUpgrade:
    def __init__(self, host, port, username, password):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.ssh = None
        self.sftp = None

    def connect(self):
        """建立SSH连接"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(self.host, self.port, self.username, self.password)
            self.sftp = self.ssh.open_sftp()
            print("SSH连接成功")
            return True
        except Exception as e:
            print(f"SSH连接失败: {str(e)}")
            return False

    def upload_file(self, local_path, remote_path):
        """上传文件到远程设备"""
        try:
            self.sftp.put(local_path, remote_path)
            print(f"文件上传成功: {local_path} -> {remote_path}")
            return True
        except Exception as e:
            print(f"文件上传失败: {str(e)}")
            return False

    def execute_command(self, command):
        """执行远程命令"""
        try:
            stdin, stdout, stderr = self.ssh.exec_command(command)
            output = stdout.read().decode()
            error = stderr.read().decode()
            if error:
                print(f"命令执行错误: {error}")
                return False, error
            return True, output
        except Exception as e:
            print(f"命令执行失败: {str(e)}")
            return False, str(e)

    def check_version(self):
        """检查版本信息"""
        success, output = self.execute_command("cat /etc/slamware_release")
        if success:
            print("当前版本信息:")
            print(output)
            return output
        return None

    def upgrade(self, ota_file_path):
        """执行升级流程"""
        if not os.path.exists(ota_file_path):
            print(f"OTA文件不存在: {ota_file_path}")
            return False

        # 上传OTA文件
        remote_path = "/mnt/sdcard/ota.bin"
        if not self.upload_file(ota_file_path, remote_path):
            return False

        # 执行升级命令
        print("开始执行升级...")
        success, output = self.execute_command("sysupgrade.sh /mnt/sdcard/ota.bin")
        if not success:
            return False

        # 等待升级完成
        print("升级进行中，请等待约5分钟...")
        time.sleep(300)  # 等待5分钟

        # 重新连接并检查版本
        self.ssh.close()
        time.sleep(10)  # 等待设备重启
        if not self.connect():
            print("升级后重新连接失败")
            return False

        version = self.check_version()
        if version:
            print("升级完成")
            return True
        return False

    def close(self):
        """关闭连接"""
        if self.sftp:
            self.sftp.close()
        if self.ssh:
            self.ssh.close()

def main():
    # 配置信息
    host = "************"
    port = 22
    username = "root"
    password = "slamware123"

    # 创建升级实例
    upgrade = ElevatorUpgrade(host, port, username, password)

    # 连接设备
    if not upgrade.connect():
        return

    try:
        # 检查当前版本
        print("检查当前版本...")
        upgrade.check_version()

        # 获取OTA文件路径
        ota_file = input("请输入OTA文件路径: ").strip()
        if not ota_file:
            print("未提供OTA文件路径")
            return

        # 执行升级
        if upgrade.upgrade(ota_file):
            print("升级流程完成")
        else:
            print("升级失败")
    finally:
        upgrade.close()

if __name__ == "__main__":
    main() 