/* 全局样式 */
:root {
    --primary-color: #2196F3;
    --secondary-color: #1976D2;
    --background-color: #f5f5f5;
    --text-color: #333;
    --card-background: #ffffff;
    --shadow: 0 2px 4px rgba(0,0,0,0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--background-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
.header {
    background-color: var(--card-background);
    box-shadow: var(--shadow);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin-bottom: 1rem;
}

.nav ul {
    list-style: none;
    display: flex;
    gap: 2rem;
}

.nav a {
    text-decoration: none;
    color: var(--text-color);
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav a:hover,
.nav a.active {
    color: var(--primary-color);
}

/* 主要内容区域 */
.main {
    padding: 2rem 0;
}

.products-grid {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    padding: 1rem 0;
}

/* 分类标题样式 */
.category-title {
    color: var(--primary-color);
    font-size: 1.8rem;
    margin: 0 0 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-color);
}

.category-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 1rem;
}

/* 产品卡片样式 */
.product-card {
    background: var(--card-background);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.product-image {
    width: 100%;
    height: 220px;
    overflow: hidden;
    background-color: #f8f9fa;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.product-info h3 {
    margin-bottom: 0.8rem;
    color: var(--text-color);
    font-size: 1.2rem;
}

.product-info p {
    color: #666;
    margin-bottom: 1.2rem;
    line-height: 1.5;
}

.btn-select {
    display: inline-block;
    padding: 0.6rem 1.2rem;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    text-align: center;
    font-weight: 500;
}

.btn-select:hover {
    background-color: var(--secondary-color);
}

/* 页脚样式 */
.footer {
    background-color: var(--card-background);
    padding: 2rem 0;
    margin-top: 2rem;
    text-align: center;
    box-shadow: var(--shadow);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header h1 {
        font-size: 1.5rem;
    }

    .nav ul {
        gap: 1rem;
    }

    .category-title {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .category-container {
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1.5rem;
    }

    .product-image {
        height: 200px;
    }
}

@media (max-width: 480px) {
    .nav ul {
        flex-direction: column;
        gap: 0.5rem;
    }

    .category-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .category-title {
        font-size: 1.3rem;
    }

    .product-image {
        height: 180px;
    }
} 