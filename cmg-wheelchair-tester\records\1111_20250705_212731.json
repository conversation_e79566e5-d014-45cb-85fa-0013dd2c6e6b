{"sn": "1111", "timestamp": "2025-07-05T21:27:31.372434", "results": {"device_connection": {"test_name": "设备连接状态", "status": "失败", "message": "未连接设备", "timestamp": "2025-07-05T21:27:26.939640"}, "usb_test": {"test_name": "USB关键器件测试", "status": "失败", "message": "未知的测试类型", "timestamp": "2025-07-05T21:27:27.984961"}, "can_test": {"test_name": "CAN0测试", "status": "失败", "message": "CAN0 down失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T21:27:29.099693"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "无GPS信号", "timestamp": "2025-07-05T21:27:30.331825"}, "4g_test": {"test_name": "4G模组测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "key_test": {"test_name": "按键测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "led_test": {"test_name": "按键灯测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "torch_test": {"test_name": "手电筒测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "light_sensor_test": {"test_name": "光感测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "speaker_test": {"test_name": "喇叭测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}, "wifi_test": {"test_name": "WiFi测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:26.836883"}}}