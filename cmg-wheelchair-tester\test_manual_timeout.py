#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试保留人工判断逻辑的超时功能
"""

import tkinter as tk
from tkinter import ttk
import time

def test_manual_judgment_with_timeout():
    """测试带超时的人工判断功能"""
    print("=== 测试带超时的人工判断功能 ===")
    
    root = tk.Tk()
    root.title("人工判断超时测试")
    root.geometry("700x500")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="人工判断超时测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 测试结果显示
    result_var = tk.StringVar(value="等待测试...")
    result_label = ttk.Label(main_frame, textvariable=result_var, font=("Arial", 12))
    result_label.pack(pady=10)
    
    def show_timeout_confirmation(title, message):
        """显示带超时的确认对话框 - 保留完整人工判断逻辑"""
        # 创建自定义对话框
        dialog = tk.Toplevel(root)
        dialog.title(title)
        dialog.geometry("450x250")
        dialog.resizable(False, False)
        dialog.transient(root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        result = {"answer": None, "timeout": False}
        
        # 主框架
        dialog_frame = ttk.Frame(dialog, padding="20")
        dialog_frame.pack(fill=tk.BOTH, expand=True)
        
        # 消息文本
        message_label = ttk.Label(dialog_frame, text=message, justify=tk.CENTER, wraplength=400)
        message_label.pack(pady=(0, 15))
        
        # 倒计时显示
        countdown_var = tk.StringVar(value="剩余时间: 60秒")
        countdown_label = ttk.Label(dialog_frame, textvariable=countdown_var, foreground="red", font=("Arial", 10, "bold"))
        countdown_label.pack(pady=(0, 15))
        
        # 按钮框架
        button_frame = ttk.Frame(dialog_frame)
        button_frame.pack()
        
        def on_yes():
            """用户选择'是' - 人工判断为通过"""
            result["answer"] = "yes"
            result["timeout"] = False
            print("👤 用户手动选择: 是 (测试通过)")
            dialog.destroy()
        
        def on_no():
            """用户选择'否' - 人工判断为失败"""
            result["answer"] = "no"
            result["timeout"] = False
            print("👤 用户手动选择: 否 (测试失败)")
            dialog.destroy()
        
        def on_timeout():
            """超时处理 - 自动判定为失败，但保留超时标记"""
            result["timeout"] = True
            result["answer"] = "timeout"  # 超时状态，区别于用户主动选择
            print("⏰ 确认对话框超时 - 1分钟内无人工确认，自动判定为失败")
            dialog.destroy()
        
        # 按钮
        yes_button = ttk.Button(button_frame, text="是 (通过)", command=on_yes, width=12)
        yes_button.pack(side=tk.LEFT, padx=(0, 15))
        
        no_button = ttk.Button(button_frame, text="否 (失败)", command=on_no, width=12)
        no_button.pack(side=tk.LEFT)
        
        # 倒计时更新
        remaining_time = [60]  # 使用列表以便在嵌套函数中修改
        
        def update_countdown():
            if remaining_time[0] > 0:
                countdown_var.set(f"剩余时间: {remaining_time[0]}秒")
                remaining_time[0] -= 1
                dialog.after(1000, update_countdown)
            else:
                on_timeout()
        
        # 开始倒计时
        update_countdown()
        
        # 绑定键盘事件
        dialog.bind("<Return>", lambda e: on_yes())
        dialog.bind("<Escape>", lambda e: on_no())
        dialog.bind("<y>", lambda e: on_yes())
        dialog.bind("<n>", lambda e: on_no())
        
        # 等待窗口关闭
        dialog.wait_window()
        
        return result["answer"], result["timeout"]
    
    def test_led_scenario():
        """模拟LED测试场景"""
        result_var.set("正在进行LED测试...")
        root.update()
        
        # 模拟LED点亮命令
        print("🔧 执行LED点亮命令...")
        time.sleep(1)
        
        # 显示确认对话框
        confirm, is_timeout = show_timeout_confirmation(
            "LED测试确认",
            "请确认：\n1. 背光灯是否已点亮？\n2. 背光灯颜色是否为蓝色？\n\n注意：1分钟内无响应将自动判定为失败"
        )
        
        # 模拟LED关闭命令
        print("🔧 执行LED关闭命令...")
        
        # 处理不同的响应结果，保留人工判断逻辑
        if confirm == "yes":
            result_var.set("✅ LED测试通过 - 用户确认背光灯正常")
            print("✅ LED测试通过 - 用户确认背光灯正常")
            return True
        elif confirm == "no":
            result_var.set("❌ LED测试失败 - 用户确认背光灯异常")
            print("❌ LED测试失败 - 用户确认背光灯异常")
            return False
        elif confirm == "timeout":
            result_var.set("⏰ LED测试超时失败 - 1分钟内无人工确认")
            print("⏰ LED测试超时失败 - 1分钟内无人工确认")
            return False
        else:
            result_var.set("❌ LED测试失败 - 未知响应")
            print("❌ LED测试失败 - 未知响应")
            return False
    
    def test_flashlight_scenario():
        """模拟手电筒测试场景"""
        result_var.set("正在进行手电筒测试...")
        root.update()
        
        # 模拟手电筒点亮命令
        print("🔦 执行手电筒点亮命令...")
        time.sleep(1)
        
        # 显示确认对话框
        confirm, is_timeout = show_timeout_confirmation(
            "手电筒测试确认",
            "请确认：\n1. 手电筒是否已点亮？\n2. 亮度是否正常？\n\n注意：1分钟内无响应将自动判定为失败"
        )
        
        # 模拟手电筒关闭命令
        print("🔦 执行手电筒关闭命令...")
        
        # 处理不同的响应结果，保留人工判断逻辑
        if confirm == "yes":
            result_var.set("✅ 手电筒测试通过 - 用户确认手电筒正常")
            print("✅ 手电筒测试通过 - 用户确认手电筒正常")
            return True
        elif confirm == "no":
            result_var.set("❌ 手电筒测试失败 - 用户确认手电筒异常")
            print("❌ 手电筒测试失败 - 用户确认手电筒异常")
            return False
        elif confirm == "timeout":
            result_var.set("⏰ 手电筒测试超时失败 - 1分钟内无人工确认")
            print("⏰ 手电筒测试超时失败 - 1分钟内无人工确认")
            return False
        else:
            result_var.set("❌ 手电筒测试失败 - 未知响应")
            print("❌ 手电筒测试失败 - 未知响应")
            return False
    
    def test_quick_timeout():
        """快速超时演示（5秒）"""
        result_var.set("快速超时演示...")
        root.update()
        
        # 创建快速超时的对话框
        dialog = tk.Toplevel(root)
        dialog.title("快速超时演示")
        dialog.geometry("350x180")
        dialog.resizable(False, False)
        dialog.transient(root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        result = {"timeout": False, "answer": None}
        
        # 主框架
        dialog_frame = ttk.Frame(dialog, padding="20")
        dialog_frame.pack(fill=tk.BOTH, expand=True)
        
        # 消息文本
        message_label = ttk.Label(dialog_frame, text="这个对话框将在5秒后自动超时\n演示超时自动判断失败的功能", justify=tk.CENTER)
        message_label.pack(pady=(0, 15))
        
        # 倒计时显示
        countdown_var = tk.StringVar(value="剩余时间: 5秒")
        countdown_label = ttk.Label(dialog_frame, textvariable=countdown_var, foreground="red", font=("Arial", 12, "bold"))
        countdown_label.pack(pady=(0, 15))
        
        # 按钮框架
        button_frame = ttk.Frame(dialog_frame)
        button_frame.pack()
        
        def on_manual_yes():
            result["answer"] = "yes"
            result["timeout"] = False
            print("👤 用户在超时前选择: 是")
            dialog.destroy()
        
        def on_manual_no():
            result["answer"] = "no"
            result["timeout"] = False
            print("👤 用户在超时前选择: 否")
            dialog.destroy()
        
        def on_timeout():
            result["timeout"] = True
            result["answer"] = "timeout"
            print("⏰ 快速超时演示 - 5秒内无响应，自动判定为失败")
            dialog.destroy()
        
        # 按钮
        yes_button = ttk.Button(button_frame, text="是", command=on_manual_yes)
        yes_button.pack(side=tk.LEFT, padx=(0, 10))
        
        no_button = ttk.Button(button_frame, text="否", command=on_manual_no)
        no_button.pack(side=tk.LEFT)
        
        # 倒计时更新
        remaining_time = [5]
        
        def update_countdown():
            if remaining_time[0] > 0:
                countdown_var.set(f"剩余时间: {remaining_time[0]}秒")
                remaining_time[0] -= 1
                dialog.after(1000, update_countdown)
            else:
                on_timeout()
        
        # 开始倒计时
        update_countdown()
        
        # 等待窗口关闭
        dialog.wait_window()
        
        if result["timeout"]:
            result_var.set("快速超时演示: 成功演示超时自动判断失败 ✅")
        elif result["answer"] == "yes":
            result_var.set("快速超时演示: 用户在超时前选择了'是' ✅")
        elif result["answer"] == "no":
            result_var.set("快速超时演示: 用户在超时前选择了'否' ✅")
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=20)
    
    led_button = ttk.Button(
        button_frame,
        text="测试LED场景",
        command=test_led_scenario
    )
    led_button.pack(side=tk.LEFT, padx=(0, 10))
    
    flashlight_button = ttk.Button(
        button_frame,
        text="测试手电筒场景",
        command=test_flashlight_scenario
    )
    flashlight_button.pack(side=tk.LEFT, padx=(0, 10))
    
    timeout_button = ttk.Button(
        button_frame,
        text="快速超时演示",
        command=test_quick_timeout
    )
    timeout_button.pack(side=tk.LEFT)
    
    # 说明文本
    info_frame = ttk.LabelFrame(main_frame, text="人工判断 + 超时功能说明", padding="10")
    info_frame.pack(fill=tk.X, pady=20)
    
    info_text = """功能特点：
• 保留完整的人工判断逻辑：用户可以选择"是"或"否"
• 增加超时保护：1分钟内无响应自动判定为失败
• 区分不同的失败原因：
  - 用户主动选择"否" → 人工判断失败
  - 超时无响应 → 超时失败
• 提供可视化倒计时，用户可以看到剩余时间
• 支持键盘快捷键：Enter确认，Escape取消，Y/N快速选择

测试流程：
1. 执行硬件操作命令（如点亮LED、手电筒）
2. 显示确认对话框，等待用户人工判断
3. 如果1分钟内无响应，自动判定为失败
4. 执行清理命令（如关闭LED、手电筒）
5. 记录详细的测试结果和失败原因"""
    
    info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
    info_label.pack(fill=tk.X)
    
    print("人工判断超时测试窗口已创建")
    root.mainloop()

if __name__ == "__main__":
    print("人工判断 + 超时功能测试")
    print("=" * 50)
    
    test_manual_judgment_with_timeout()
