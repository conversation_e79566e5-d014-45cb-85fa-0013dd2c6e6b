#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的按键测试 - 不使用GUI
"""

import subprocess
import threading
import time
import queue

def test_key_detection():
    """测试按键检测功能"""
    print("=== 按键检测测试 ===")
    print("请准备按按键，测试将在3秒后开始...")
    time.sleep(3)
    
    try:
        # 启动evtest进程
        command = "evtest /dev/input/event5"
        print(f"执行命令: adb shell {command}")
        
        process = subprocess.Popen(
            ['adb', 'shell', command],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待初始化
        time.sleep(2)
        print("gpio-keys设备监听已启动，请开始按键测试...")
        print("请在30秒内按下8个按键...")
        
        # 监听按键事件
        expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
        detected_keys = set()
        key_press_states = {}
        start_time = time.time()
        timeout = 30  # 30秒超时
        
        # 创建输出队列
        output_queue = queue.Queue()
        
        def read_output():
            """读取进程输出"""
            try:
                while True:
                    line = process.stdout.readline()
                    if not line:
                        break
                    output_queue.put(line.strip())
            except Exception as e:
                output_queue.put(f"ERROR: {str(e)}")
        
        # 启动输出读取线程
        output_thread = threading.Thread(target=read_output, daemon=True)
        output_thread.start()
        
        while len(detected_keys) < 8 and (time.time() - start_time) < timeout:
            if process.poll() is not None:
                print("evtest进程已结束")
                break
            
            try:
                # 从队列中获取输出行
                try:
                    line = output_queue.get_nowait()
                except queue.Empty:
                    time.sleep(0.1)
                    continue
                
                if not line or line.startswith("ERROR:"):
                    if line.startswith("ERROR:"):
                        print(f"读取输出时出错: {line[6:]}")
                    continue
                
                # 记录所有事件输出用于调试
                if "Event:" in line:
                    print(f"事件: {line}")
                
                # 解析按键事件
                if "EV_KEY" in line and "KEY_" in line:
                    try:
                        key_code = None
                        value = None
                        
                        parts = line.split(", ")
                        for part in parts:
                            part = part.strip()
                            if part.startswith("code "):
                                code_str = part.split("code ")[1].split(" ")[0]
                                key_code = int(code_str)
                            elif part.startswith("value "):
                                value = int(part.split("value ")[1])
                        
                        # 检查是否是我们关心的按键
                        if key_code in expected_keys and value is not None:
                            if value == 1:
                                # 按键按下
                                key_press_states[key_code] = True
                                print(f"  -> 检测到按键{key_code}按下")
                            elif value == 0 and key_press_states.get(key_code, False):
                                # 按键松开
                                detected_keys.add(key_code)
                                print(f"  -> 按键{key_code}测试完成 (按下->松开)")
                                print(f"  -> 进度: {len(detected_keys)}/8 个按键已完成")
                        
                    except (ValueError, IndexError) as e:
                        print(f"解析按键事件失败: {str(e)}, 原始行: {line}")
                        continue
                
            except Exception as e:
                print(f"处理按键事件时出错: {str(e)}")
                break
        
        # 停止进程
        try:
            process.terminate()
            process.wait(timeout=5)
        except:
            process.kill()
        
        # 显示结果
        print("\n=== 测试结果 ===")
        if len(detected_keys) >= 8:
            print(f"✓ 按键测试成功 - 检测到{len(detected_keys)}个按键")
            print(f"检测到的按键: {sorted(detected_keys)}")
            return True
        else:
            missing_keys = set(expected_keys) - detected_keys
            print(f"✗ 按键测试失败 - 只检测到{len(detected_keys)}个按键")
            print(f"检测到的按键: {sorted(detected_keys)}")
            print(f"未检测到的按键: {sorted(missing_keys)}")
            return False
        
    except Exception as e:
        print(f"按键测试出错: {str(e)}")
        return False

def check_evtest_output():
    """检查evtest的基本输出"""
    print("\n=== 检查evtest基本输出 ===")
    try:
        print("执行: adb shell evtest /dev/input/event5")
        print("将在5秒后自动停止...")
        
        process = subprocess.Popen(
            ['adb', 'shell', 'timeout', '5', 'evtest', '/dev/input/event5'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        stdout, stderr = process.communicate()
        
        print("标准输出:")
        print(stdout)
        
        if stderr:
            print("错误输出:")
            print(stderr)
        
        print(f"返回码: {process.returncode}")
        
    except Exception as e:
        print(f"检查evtest输出失败: {e}")

if __name__ == "__main__":
    print("简单按键测试")
    print("=" * 50)
    
    # 检查ADB连接
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            print("ADB连接失败，无法进行测试")
            exit(1)
        
        devices = result.stdout.strip().split('\n')[1:]
        if not any('device' in line for line in devices):
            print("没有连接的设备，无法进行测试")
            exit(1)
        
        print("ADB连接正常，设备已连接")
        
    except Exception as e:
        print(f"检查ADB连接失败: {e}")
        exit(1)
    
    # 检查evtest基本输出
    check_evtest_output()
    
    # 进行按键检测测试
    success = test_key_detection()
    
    if success:
        print("\n🎉 按键测试功能正常！")
    else:
        print("\n❌ 按键测试需要调试")
    
    print("\n测试完成")
