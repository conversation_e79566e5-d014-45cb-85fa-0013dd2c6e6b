#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试焦点修复后的单项测试功能
验证序列号输入窗口取消后，单项测试功能是否正常工作
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time
import random

def test_focus_fix():
    """测试焦点修复"""
    print("=== 测试焦点修复后的单项测试功能 ===")
    
    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        test_projects = config.get("test_projects", [])
        work_processes = config.get("work_processes", {})
    except Exception as e:
        print(f"配置加载失败: {e}")
        return
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("焦点修复测试")
    root.geometry("1000x700")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 1000) // 2
    y = (screen_height - 700) // 2
    root.geometry(f"1000x700+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="焦点修复测试 - 单项测试功能验证", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 问题说明
    problem_frame = ttk.LabelFrame(main_frame, text="问题描述", padding="15")
    problem_frame.pack(fill=tk.X, pady=(0, 20))
    
    problem_text = """🔍 问题现象:
1. 在扫描序列号窗口点击了"取消"按钮
2. 返回主界面后，双击测试项目没有任何反应
3. 右键点击测试项目也没有菜单提示

🔧 修复内容:
1. 修复了对话框关闭时的焦点释放问题
2. 确保主窗口重新获得焦点
3. 添加了详细的调试信息

✅ 预期结果:
- 取消序列号输入后，单项测试功能正常工作
- 双击和右键菜单都能正常响应"""
    
    ttk.Label(problem_frame, text=problem_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 创建左右分栏
    content_frame = ttk.Frame(main_frame)
    content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 左侧：测试项目列表
    left_frame = ttk.LabelFrame(content_frame, text="测试项目列表 (模拟主程序)", padding="10")
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
    
    # 创建测试项目树
    columns = ("data", "result")
    test_tree = ttk.Treeview(left_frame, columns=columns, show="tree headings", height=12)
    
    test_tree.heading("#0", text="测试项目", anchor=tk.W)
    test_tree.heading("data", text="测试数据", anchor=tk.W)
    test_tree.heading("result", text="测试结果", anchor=tk.CENTER)
    
    test_tree.column("#0", width=200, minwidth=150)
    test_tree.column("data", width=150, minwidth=100)
    test_tree.column("result", width=80, minwidth=60)
    
    # 设置样式
    test_tree.tag_configure("pass_result", foreground="#28a745", font=("Microsoft YaHei UI", 10, "bold"))
    test_tree.tag_configure("fail_result", foreground="#dc3545", font=("Microsoft YaHei UI", 10, "bold"))
    test_tree.tag_configure("testing_result", foreground="#ffc107", font=("Microsoft YaHei UI", 10, "bold"))
    
    test_tree.pack(fill=tk.BOTH, expand=True)
    
    # 右侧：控制和日志
    right_frame = ttk.Frame(content_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    
    # 状态显示
    status_var = tk.StringVar(value="准备测试...")
    status_label = ttk.Label(right_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack(pady=(0, 15))
    
    # 日志显示
    log_frame = ttk.LabelFrame(right_frame, text="事件日志", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    log_text = tk.Text(log_frame, height=10, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message, color=None):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if color:
            start_pos = log_text.index(tk.END + "-1c")
            log_text.insert(tk.END, log_entry)
            end_pos = log_text.index(tk.END + "-1c")
            log_text.tag_add(color, start_pos, end_pos)
            log_text.tag_config(color, foreground=color)
        else:
            log_text.insert(tk.END, log_entry)
        
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 测试状态
    test_state = {
        "running": False,
        "test_results": {}
    }
    
    # 初始化测试项目
    def init_test_projects():
        """初始化测试项目"""
        # 清空现有项目
        for item in test_tree.get_children():
            test_tree.delete(item)
        
        # 获取测试项目
        if work_processes and "整机半成品功能测试" in work_processes:
            test_ids = work_processes["整机半成品功能测试"]["test_ids"][:8]  # 只显示前8个
            
            for test_id in test_ids:
                project_name = test_id
                for project in test_projects:
                    if project["id"] == test_id:
                        project_name = project["name"]
                        break
                
                item = test_tree.insert("", "end", text=project_name)
                test_tree.set(item, "data", "")
                test_tree.set(item, "result", "")
                
                test_state["test_results"][test_id] = {
                    "name": project_name,
                    "status": "未测试",
                    "item": item
                }
    
    def update_test_item(test_id, status, message=""):
        """更新测试项目状态"""
        if test_id in test_state["test_results"]:
            result = test_state["test_results"][test_id]
            item = result["item"]
            
            result["status"] = status
            test_tree.set(item, "data", message)
            
            if status == "通过":
                test_tree.set(item, "result", "PASS")
                test_tree.item(item, tags=("pass_result",))
            elif status == "失败":
                test_tree.set(item, "result", "FAIL")
                test_tree.item(item, tags=("fail_result",))
            elif status == "测试中":
                test_tree.set(item, "result", "测试中")
                test_tree.item(item, tags=("testing_result",))
            else:
                test_tree.set(item, "result", "")
                test_tree.item(item, tags=())
    
    def run_single_test_simulation(item_id):
        """运行单项测试模拟"""
        if test_state["running"]:
            log_message("已有测试在运行中", "orange")
            return
        
        test_state["running"] = True
        
        # 获取测试项目信息
        test_name = test_tree.item(item_id, "text")
        test_id = None
        
        for tid, result in test_state["test_results"].items():
            if result["item"] == item_id:
                test_id = tid
                break
        
        if not test_id:
            log_message("未找到测试项目信息", "red")
            test_state["running"] = False
            return
        
        log_message(f"🔧 run_single_test 方法被调用", "blue")
        log_message(f"🔧 选中的项目: {test_name}")
        log_message(f"开始单项测试: {test_name}", "blue")
        status_var.set(f"正在测试: {test_name}")
        
        # 更新状态为测试中
        update_test_item(test_id, "测试中", "正在测试...")
        
        def simulate_test():
            try:
                time.sleep(2)  # 模拟测试时间
                
                # 随机生成测试结果
                is_pass = random.random() > 0.3  # 70%通过率
                
                if is_pass:
                    message = "测试通过"
                    update_test_item(test_id, "通过", message)
                    log_message(f"{test_name} - 单项测试通过 ✅", "green")
                else:
                    message = "测试失败"
                    update_test_item(test_id, "失败", message)
                    log_message(f"{test_name} - 单项测试失败 ❌", "red")
                
                status_var.set("测试完成")
                
            except Exception as e:
                log_message(f"测试出错: {str(e)}", "red")
                update_test_item(test_id, "失败", f"错误: {str(e)}")
            finally:
                test_state["running"] = False
        
        threading.Thread(target=simulate_test, daemon=True).start()
    
    def on_double_click(event):
        """双击事件处理"""
        log_message("🖱️🖱️ 双击事件触发！", "blue")
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"双击测试项目: {test_name}")
            run_single_test_simulation(item[0])
        else:
            log_message("❌ 没有选中的项目", "red")
    
    def on_single_click(event):
        """单击事件处理"""
        log_message("🖱️ 单击事件触发")
    
    def show_test_menu(event):
        """显示右键菜单"""
        log_message("🖱️➡️ 右键菜单事件触发", "blue")
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"右键测试项目: {test_name}")
            test_menu.post(event.x_root, event.y_root)
        else:
            log_message("❌ 没有选中的项目", "red")
    
    def run_menu_test():
        """从菜单运行测试"""
        log_message("📋 菜单测试触发", "blue")
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"菜单测试项目: {test_name}")
            run_single_test_simulation(item[0])
    
    def show_test_details():
        """显示测试详情"""
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            messagebox.showinfo("测试详情", f"测试项目: {test_name}")
    
    # 创建右键菜单
    test_menu = tk.Menu(root, tearoff=0)
    test_menu.add_command(label="运行测试", command=run_menu_test)
    test_menu.add_command(label="查看详情", command=show_test_details)
    
    # 绑定事件（与主程序相同的方式）
    test_tree.bind("<Double-Button-1>", on_double_click)
    test_tree.bind("<Button-3>", show_test_menu)
    test_tree.bind("<Button-1>", on_single_click, add="+")
    
    # 模拟序列号输入对话框
    def show_serial_dialog():
        """显示序列号输入对话框（模拟主程序）"""
        log_message("显示序列号输入对话框", "blue")
        
        dialog = tk.Toplevel(root)
        dialog.title("输入序列号")
        dialog.geometry("400x200")
        dialog.transient(root)
        dialog.grab_set()  # 模拟主程序的焦点抓取
        dialog.resizable(False, False)
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(main_frame, text="请输入设备序列号:", 
                 font=("Microsoft YaHei UI", 12)).pack(pady=(0, 20))
        
        serial_var = tk.StringVar()
        serial_entry = ttk.Entry(main_frame, textvariable=serial_var, width=30)
        serial_entry.pack(pady=(0, 20))
        serial_entry.focus()
        
        button_frame = ttk.Frame(main_frame)
        button_frame.pack()
        
        def confirm():
            serial = serial_var.get().strip()
            if serial:
                log_message(f"确认序列号: {serial}", "green")
                # 修复后的关闭方式
                dialog.grab_release()
                dialog.destroy()
                root.focus_force()
                root.lift()
                log_message("对话框已关闭，主窗口重新获得焦点", "green")
            else:
                messagebox.showwarning("警告", "请输入序列号")
        
        def cancel():
            log_message("用户点击取消按钮", "orange")
            # 修复后的关闭方式
            dialog.grab_release()
            dialog.destroy()
            root.focus_force()
            root.lift()
            log_message("对话框已取消，主窗口重新获得焦点", "green")
            log_message("现在可以测试单项测试功能是否正常", "blue")
        
        ttk.Button(button_frame, text="确认", command=confirm).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=cancel).pack(side=tk.LEFT, padx=5)
    
    # 控制按钮
    control_frame = ttk.Frame(right_frame)
    control_frame.pack(fill=tk.X)
    
    def reset_tests():
        """重置测试"""
        for test_id in test_state["test_results"]:
            update_test_item(test_id, "未测试", "")
        log_message("测试已重置")
        status_var.set("准备测试...")
    
    def clear_log():
        """清空日志"""
        log_text.delete(1.0, tk.END)
        log_message("日志已清空")
    
    ttk.Button(control_frame, text="显示序列号对话框", 
              command=show_serial_dialog, width=18).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="重置测试", 
              command=reset_tests, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="清空日志", 
              command=clear_log, width=12).pack(side=tk.RIGHT, padx=5)
    
    # 测试说明
    instruction_frame = ttk.LabelFrame(main_frame, text="测试步骤", padding="10")
    instruction_frame.pack(fill=tk.X)
    
    instruction_text = """📋 测试步骤:

1. 点击"显示序列号对话框"按钮
2. 在弹出的对话框中点击"取消"按钮
3. 观察日志输出，确认主窗口重新获得焦点
4. 尝试双击测试项目列表中的任意项目
5. 观察是否有双击事件触发和测试执行
6. 尝试右键点击测试项目
7. 观察是否显示右键菜单

✅ 预期结果:
- 取消对话框后，双击和右键功能正常工作
- 日志中显示相应的事件触发信息"""
    
    ttk.Label(instruction_frame, text=instruction_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 9)).pack(fill=tk.X)
    
    # 初始化
    init_test_projects()
    log_message("焦点修复测试已准备就绪")
    log_message("请按照测试步骤进行验证")
    
    print("焦点修复测试界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("焦点修复测试")
    print("=" * 50)
    
    test_focus_fix()
    
    print("\n测试完成")
