#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OrionStar ROM 刷机工具
用于Windows平台的Android设备刷机工具
"""

import sys
import os
import subprocess
import json
import time
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
                             QWidget, QLabel, QPushButton, QTextEdit, QComboBox, 
                             QFileDialog, QProgressBar, QMessageBox, QGroupBox)
from PyQt5.QtCore import QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QIcon

class DeviceDetector(QThread):
    """设备检测线程"""
    device_detected = pyqtSignal(str, str)  # 设备ID, 设备型号
    device_disconnected = pyqtSignal()
    
    def __init__(self):
        super().__init__()
        self.running = True
        
    def run(self):
        """运行设备检测"""
        while self.running:
            try:
                # 检测ADB设备
                result = subprocess.run(['adb', 'devices'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                    connected_devices = [line for line in lines if line.strip() and 'device' in line]
                    
                    if connected_devices:
                        device_id = connected_devices[0].split('\t')[0]
                        # 获取设备型号
                        model_result = subprocess.run(['adb', '-s', device_id, 'shell', 'getprop', 'ro.product.model'],
                                                    capture_output=True, text=True, timeout=5)
                        if model_result.returncode == 0:
                            model = model_result.stdout.strip()
                            self.device_detected.emit(device_id, model)
                        else:
                            self.device_detected.emit(device_id, "未知型号")
                    else:
                        self.device_disconnected.emit()
                else:
                    self.device_disconnected.emit()
            except:
                self.device_disconnected.emit()
            
            time.sleep(2)  # 每2秒检测一次
    
    def stop(self):
        """停止检测"""
        self.running = False

class RomFlasher(QThread):
    """ROM刷机线程"""
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    flash_completed = pyqtSignal(bool, str)
    
    def __init__(self, device_id, rom_path):
        super().__init__()
        self.device_id = device_id
        self.rom_path = rom_path
        
    def run(self):
        """执行刷机"""
        try:
            self.status_updated.emit("正在推送ROM文件...")
            self.progress_updated.emit(10)
            
            # 推送ROM文件到设备
            push_cmd = ['adb', '-s', self.device_id, 'push', self.rom_path, '/sdcard/']
            result = subprocess.run(push_cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode != 0:
                self.flash_completed.emit(False, f"推送ROM文件失败: {result.stderr}")
                return
            
            self.progress_updated.emit(50)
            self.status_updated.emit("正在安装ROM...")
            
            # 安装ROM
            install_cmd = ['adb', '-s', self.device_id, 'shell', 'pm', 'install', '-r', '/sdcard/' + os.path.basename(self.rom_path)]
            result = subprocess.run(install_cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode != 0:
                self.flash_completed.emit(False, f"安装ROM失败: {result.stderr}")
                return
            
            self.progress_updated.emit(100)
            self.status_updated.emit("刷机完成")
            self.flash_completed.emit(True, "ROM刷机成功完成")
            
        except subprocess.TimeoutExpired:
            self.flash_completed.emit(False, "刷机操作超时")
        except Exception as e:
            self.flash_completed.emit(False, f"刷机过程中发生错误: {str(e)}")

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.device_id = None
        self.device_model = None
        self.rom_path = None
        self.device_detector = None
        self.rom_flasher = None
        
        self.init_ui()
        self.start_device_detection()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("OrionStar ROM 刷机工具")
        self.setGeometry(100, 100, 800, 600)
        
        # 设置字体
        font = QFont("Microsoft YaHei", 9)
        self.setFont(font)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        layout = QVBoxLayout(central_widget)
        
        # 设备信息组
        device_group = QGroupBox("设备信息")
        device_layout = QVBoxLayout(device_group)
        
        self.device_label = QLabel("未检测到设备")
        self.device_label.setStyleSheet("color: red; font-weight: bold;")
        device_layout.addWidget(self.device_label)
        
        layout.addWidget(device_group)
        
        # ROM选择组
        rom_group = QGroupBox("ROM选择")
        rom_layout = QVBoxLayout(rom_group)
        
        # ROM类型选择
        rom_type_layout = QHBoxLayout()
        rom_type_layout.addWidget(QLabel("ROM类型:"))
        self.rom_type_combo = QComboBox()
        self.rom_type_combo.addItems(["工厂版本", "出货版本"])
        self.rom_type_combo.currentTextChanged.connect(self.update_rom_list)
        rom_type_layout.addWidget(self.rom_type_combo)
        rom_layout.addLayout(rom_type_layout)
        
        # ROM区域选择
        rom_region_layout = QHBoxLayout()
        rom_region_layout.addWidget(QLabel("ROM区域:"))
        self.rom_region_combo = QComboBox()
        self.rom_region_combo.addItems(["国内版本", "海外版本"])
        self.rom_region_combo.currentTextChanged.connect(self.update_rom_list)
        rom_layout.addLayout(rom_region_layout)
        
        # ROM文件选择
        rom_file_layout = QHBoxLayout()
        rom_file_layout.addWidget(QLabel("ROM文件:"))
        self.rom_file_combo = QComboBox()
        rom_file_layout.addWidget(self.rom_file_combo)
        self.select_rom_btn = QPushButton("选择文件")
        self.select_rom_btn.clicked.connect(self.select_rom_file)
        rom_file_layout.addWidget(self.select_rom_btn)
        rom_layout.addLayout(rom_file_layout)
        
        layout.addWidget(rom_group)
        
        # 操作按钮组
        button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新设备")
        self.refresh_btn.clicked.connect(self.refresh_device)
        button_layout.addWidget(self.refresh_btn)
        
        self.flash_btn = QPushButton("开始刷机")
        self.flash_btn.clicked.connect(self.start_flash)
        self.flash_btn.setEnabled(False)
        button_layout.addWidget(self.flash_btn)
        
        layout.addLayout(button_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 状态显示
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        # 日志显示
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(200)
        log_layout.addWidget(self.log_text)
        
        layout.addWidget(log_group)
        
        # 初始化ROM列表
        self.update_rom_list()
        
    def start_device_detection(self):
        """开始设备检测"""
        self.device_detector = DeviceDetector()
        self.device_detector.device_detected.connect(self.on_device_detected)
        self.device_detector.device_disconnected.connect(self.on_device_disconnected)
        self.device_detector.start()
        
    def on_device_detected(self, device_id, model):
        """设备检测到"""
        self.device_id = device_id
        self.device_model = model
        self.device_label.setText(f"设备ID: {device_id}\n型号: {model}")
        self.device_label.setStyleSheet("color: green; font-weight: bold;")
        self.log_message(f"检测到设备: {model} ({device_id})")
        self.update_flash_button()
        
    def on_device_disconnected(self):
        """设备断开"""
        self.device_id = None
        self.device_model = None
        self.device_label.setText("未检测到设备")
        self.device_label.setStyleSheet("color: red; font-weight: bold;")
        self.log_message("设备已断开连接")
        self.update_flash_button()
        
    def refresh_device(self):
        """刷新设备"""
        self.log_message("正在刷新设备连接...")
        # 设备检测线程会自动处理
        
    def update_rom_list(self):
        """更新ROM列表"""
        rom_type = self.rom_type_combo.currentText()
        rom_region = self.rom_region_combo.currentText()
        
        # 构建ROM路径
        if rom_type == "工厂版本":
            rom_base_path = os.path.join("roms", rom_type)
        else:
            rom_base_path = os.path.join("roms", rom_type, rom_region)
        
        # 清空当前列表
        self.rom_file_combo.clear()
        
        # 扫描ROM文件
        if os.path.exists(rom_base_path):
            for root, dirs, files in os.walk(rom_base_path):
                for file in files:
                    if file.endswith(('.zip', '.img')):
                        rel_path = os.path.relpath(os.path.join(root, file), rom_base_path)
                        self.rom_file_combo.addItem(rel_path, os.path.join(root, file))
        
        self.update_flash_button()
        
    def select_rom_file(self):
        """选择ROM文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择ROM文件", "", "ROM文件 (*.zip *.img);;所有文件 (*.*)"
        )
        if file_path:
            self.rom_path = file_path
            self.log_message(f"已选择ROM文件: {os.path.basename(file_path)}")
            self.update_flash_button()
            
    def update_flash_button(self):
        """更新刷机按钮状态"""
        can_flash = (self.device_id is not None and 
                    (self.rom_path is not None or self.rom_file_combo.currentData() is not None))
        self.flash_btn.setEnabled(can_flash)
        
    def start_flash(self):
        """开始刷机"""
        if not self.device_id:
            QMessageBox.warning(self, "警告", "请先连接设备")
            return
            
        # 获取ROM路径
        if self.rom_path:
            rom_path = self.rom_path
        elif self.rom_file_combo.currentData():
            rom_path = self.rom_file_combo.currentData()
        else:
            QMessageBox.warning(self, "警告", "请选择ROM文件")
            return
            
        # 确认刷机
        reply = QMessageBox.question(
            self, "确认刷机", 
            f"确定要刷入ROM文件吗？\n设备: {self.device_model}\nROM: {os.path.basename(rom_path)}",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.log_message("开始刷机...")
            self.flash_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setValue(0)
            
            # 启动刷机线程
            self.rom_flasher = RomFlasher(self.device_id, rom_path)
            self.rom_flasher.progress_updated.connect(self.progress_bar.setValue)
            self.rom_flasher.status_updated.connect(self.status_label.setText)
            self.rom_flasher.flash_completed.connect(self.on_flash_completed)
            self.rom_flasher.start()
            
    def on_flash_completed(self, success, message):
        """刷机完成"""
        self.flash_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.log_message("刷机成功完成")
        else:
            QMessageBox.critical(self, "失败", message)
            self.log_message(f"刷机失败: {message}")
            
    def log_message(self, message):
        """记录日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        
    def closeEvent(self, event):
        """关闭事件"""
        if self.device_detector:
            self.device_detector.stop()
            self.device_detector.wait()
        event.accept()

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("OrionStar ROM 刷机工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = MainWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 