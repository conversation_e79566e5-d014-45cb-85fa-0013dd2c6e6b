#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开始测试功能修复
验证：
1. 清空日志不再弹出确认对话框
2. 不再重复弹出序列号输入窗口
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}}

def test_start_test_workflow():
    """测试开始测试工作流程"""
    print("=== 测试开始测试工作流程修复 ===")
    
    config = load_config()
    work_processes = config.get("work_processes", {})
    
    if not work_processes:
        print("❌ 没有找到工序配置")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title("开始测试功能修复测试")
    root.geometry("800x600")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 800) // 2
    y = (screen_height - 600) // 2
    root.geometry(f"800x600+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="开始测试功能修复测试", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 修复说明
    fix_frame = ttk.LabelFrame(main_frame, text="修复内容", padding="15")
    fix_frame.pack(fill=tk.X, pady=(0, 20))
    
    fix_text = """🔧 开始测试功能修复:

问题1: 清空日志弹出确认对话框
  • 修复前: 开始测试时弹出"确定要清空所有日志吗？"确认对话框
  • 修复后: 后台静默清空日志，无需用户确认

问题2: 重复弹出序列号输入窗口
  • 修复前: 序列号输入后，开始测试时又弹出序列号输入窗口
  • 修复后: 使用已输入的序列号，不再重复弹窗

🎯 修复方法:
  1. 新增 _clear_log_silent() 方法，静默清空日志
  2. 修改 start_all_tests() 方法，使用静默清空
  3. 移除重复的序列号输入逻辑
  4. 使用已保存的 current_serial_number"""
    
    ttk.Label(fix_frame, text=fix_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 模拟测试区域
    test_frame = ttk.LabelFrame(main_frame, text="模拟测试", padding="15")
    test_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 状态显示
    status_var = tk.StringVar(value="准备测试...")
    status_label = ttk.Label(test_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack(pady=(0, 15))
    
    # 日志显示
    log_frame = ttk.Frame(test_frame)
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
    
    log_text = tk.Text(log_frame, height=12, font=("Consolas", 10))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        log_text.insert(tk.END, log_entry)
        log_text.see(tk.END)
        root.update_idletasks()
    
    def clear_log_silent():
        """静默清空日志（无确认弹窗）"""
        log_text.delete(1.0, tk.END)
        log_message("=== 开始新的测试 ===")
    
    # 模拟变量
    test_state = {
        "selected_process": "整机半成品功能测试",
        "current_serial_number": "CMG2024120001",
        "test_running": False
    }
    
    def simulate_old_behavior():
        """模拟修复前的行为"""
        status_var.set("模拟修复前的行为...")
        log_message("开始测试（修复前）...")
        
        # 模拟弹出确认对话框
        if messagebox.askyesno("确认", "确定要清空所有日志吗？"):
            log_text.delete(1.0, tk.END)
            log_message("日志已清空")
        
        # 模拟重复弹出序列号输入
        def show_sn_dialog():
            sn_dialog = tk.Toplevel(root)
            sn_dialog.title("扫描序列号")
            sn_dialog.geometry("400x200")
            sn_dialog.transient(root)
            sn_dialog.grab_set()
            
            # 居中显示
            sn_dialog.update_idletasks()
            x = (sn_dialog.winfo_screenwidth() // 2) - (sn_dialog.winfo_width() // 2)
            y = (sn_dialog.winfo_screenheight() // 2) - (sn_dialog.winfo_height() // 2)
            sn_dialog.geometry(f"+{x}+{y}")
            
            frame = ttk.Frame(sn_dialog, padding="20")
            frame.pack(fill=tk.BOTH, expand=True)
            
            ttk.Label(frame, text="重复弹出的序列号输入", 
                     font=("Microsoft YaHei UI", 12, "bold")).pack(pady=10)
            ttk.Label(frame, text="这是修复前会重复弹出的窗口", 
                     foreground="red").pack(pady=5)
            
            sn_var = tk.StringVar(value=test_state["current_serial_number"])
            ttk.Entry(frame, textvariable=sn_var, width=20).pack(pady=10)
            
            def close_dialog():
                sn_dialog.destroy()
                log_message(f"重复输入序列号: {sn_var.get()}")
                status_var.set("修复前: 需要重复输入序列号")
            
            ttk.Button(frame, text="确认", command=close_dialog).pack()
        
        root.after(1000, show_sn_dialog)
    
    def simulate_new_behavior():
        """模拟修复后的行为"""
        status_var.set("模拟修复后的行为...")
        log_message("开始测试（修复后）...")
        
        # 静默清空日志
        clear_log_silent()
        log_message(f"工序: {test_state['selected_process']}")
        log_message(f"序列号: {test_state['current_serial_number']}")
        log_message("✅ 无需确认对话框，直接开始测试")
        log_message("✅ 使用已输入的序列号，无重复弹窗")
        
        # 模拟测试进度
        def simulate_test_progress():
            test_items = [
                "设备连接检测",
                "版本信息测试", 
                "USB接口测试",
                "CAN通信测试",
                "GPS定位测试"
            ]
            
            for i, item in enumerate(test_items, 1):
                time.sleep(0.5)
                log_message(f"[{i}/{len(test_items)}] {item} - 通过 ✅")
                root.update_idletasks()
            
            log_message("🎉 所有测试完成！")
            status_var.set("修复后: 流程顺畅，无重复操作")
        
        threading.Thread(target=simulate_test_progress, daemon=True).start()
    
    def test_actual_program():
        """测试实际程序"""
        import subprocess
        try:
            subprocess.Popen(["python", "main.py"], cwd=".")
            log_message("✅ 已启动实际程序，请测试开始测试功能")
            status_var.set("实际程序已启动，请测试")
        except Exception as e:
            log_message(f"❌ 启动实际程序失败: {e}")
    
    # 测试按钮
    button_frame = ttk.Frame(test_frame)
    button_frame.pack(fill=tk.X)
    
    ttk.Button(button_frame, text="模拟修复前行为", 
              command=simulate_old_behavior, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="模拟修复后行为", 
              command=simulate_new_behavior, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试实际程序", 
              command=test_actual_program, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="清空日志", 
              command=lambda: log_text.delete(1.0, tk.END), width=10).pack(side=tk.RIGHT, padx=5)
    
    # 对比说明
    compare_frame = ttk.LabelFrame(main_frame, text="修复前后对比", padding="10")
    compare_frame.pack(fill=tk.X)
    
    compare_text = """📊 修复前后对比:

修复前的问题:
  ❌ 开始测试时弹出"确定要清空所有日志吗？"确认对话框
  ❌ 清空日志后又弹出序列号输入窗口
  ❌ 用户需要重复输入已经输入过的序列号
  ❌ 操作流程冗余，用户体验差

修复后的改进:
  ✅ 开始测试时静默清空日志，无需确认
  ✅ 使用已保存的序列号，不再重复弹窗
  ✅ 流程简化，直接开始测试
  ✅ 用户体验显著提升

技术实现:
  • 新增 _clear_log_silent() 方法
  • 修改 start_all_tests() 使用静默清空
  • 移除重复的序列号输入逻辑
  • 使用 current_serial_number 变量"""
    
    ttk.Label(compare_frame, text=compare_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 9)).pack(fill=tk.X)
    
    # 初始化日志
    log_message("开始测试功能修复测试已准备就绪")
    log_message(f"当前工序: {test_state['selected_process']}")
    log_message(f"当前序列号: {test_state['current_serial_number']}")
    
    print("开始测试功能修复测试界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("开始测试功能修复测试")
    print("=" * 50)
    
    print("🔧 修复内容:")
    print("  1. 清空日志不再弹出确认对话框")
    print("  2. 不再重复弹出序列号输入窗口")
    print("  3. 使用已输入的序列号直接开始测试")
    
    # 创建测试界面
    test_start_test_workflow()
    
    print("\n测试完成")
