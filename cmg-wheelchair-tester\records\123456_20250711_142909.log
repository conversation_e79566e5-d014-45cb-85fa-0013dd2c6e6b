[2025-07-11 14:26:35] 开始测试 - SN: 123456
[2025-07-11 14:26:36] 
开始执行: 设备连接状态检测
[2025-07-11 14:26:36] 设备连接测试通过，检测到 1 个设备
[2025-07-11 14:26:36] 设备: ?	device
[2025-07-11 14:26:36] 
开始执行: USB关键器件检测
[2025-07-11 14:26:36] 执行USB设备检测...
[2025-07-11 14:26:36] 执行命令: adb shell lsusb
[2025-07-11 14:26:36] 设备返回数据:
[2025-07-11 14:26:36] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-11 14:26:36] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-11 14:26:36] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-11 14:26:36] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-11 14:26:36] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-11 14:26:36] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-11 14:26:36] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-11 14:26:36] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-11 14:26:36] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-11 14:26:36] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-11 14:26:36] 总共解析到 9 个设备
[2025-07-11 14:26:36] ✅ 所有预期的设备ID都已找到
[2025-07-11 14:26:37] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 14:26:37] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 14:26:37] ✅ USB设备检测通过
[2025-07-11 14:26:37] 
开始执行: CAN0测试
[2025-07-11 14:26:37] 执行CAN0测试流程...
[2025-07-11 14:26:37] 执行命令: adb shell ip link set can0 down
[2025-07-11 14:26:37] CAN0已关闭
[2025-07-11 14:26:37] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-11 14:26:37] CAN0已启动
[2025-07-11 14:26:37] CAN监听线程已启动...
[2025-07-11 14:26:38] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-11 14:26:39] CAN测试数据已发送，等待监听返回...
[2025-07-11 14:26:39] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-11 14:26:41] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-11 14:26:41] 
开始执行: GPS测试
[2025-07-11 14:26:41] 执行GPS测试...
[2025-07-11 14:26:41] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-11 14:26:51] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-11 14:26:52] 
开始执行: 4G模组测试
[2025-07-11 14:26:52] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-11 14:26:52] 监听线程已启动，等待串口数据...
[2025-07-11 14:26:54] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-11 14:26:54] AT+CCID指令已发送，等待串口返回...
[2025-07-11 14:26:54] 串口输出: AT+CCID
[2025-07-11 14:26:54] 串口输出: 
[2025-07-11 14:26:54] 串口输出: 
[2025-07-11 14:26:54] 串口输出: +CME ERROR: 13
[2025-07-11 14:26:54] 串口输出: 
[2025-07-11 14:26:54] 串口输出: AT+C
[2025-07-11 14:26:54] 串口输出: 
[2025-07-11 14:26:54] 串口输出: 
[2025-07-11 14:26:54] 串口输出: +CME ERROR: 58
[2025-07-11 14:26:54] 串口输出: 
[2025-07-11 14:27:06] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-11 14:27:06] 
开始执行: 按键测试
[2025-07-11 14:27:06] 开始按键测试...
[2025-07-11 14:27:07] 执行命令: adb shell evtest /dev/input/event5
[2025-07-11 14:27:23] 原始事件: Event: time 1751299292.730053, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 14:27:23] 解析结果: key_code=657, value=1
[2025-07-11 14:27:23] ✓ 检测到SOS键(657)按下
[2025-07-11 14:27:23] 原始事件: Event: time 1751299293.006620, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 14:27:23] 解析结果: key_code=657, value=0
[2025-07-11 14:27:23] ✓ SOS键(657)测试通过
[2025-07-11 14:27:26] 原始事件: Event: time 1751299295.280014, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:27:26] 解析结果: key_code=663, value=1
[2025-07-11 14:27:26] ✓ 检测到语音键(663)按下
[2025-07-11 14:27:26] 原始事件: Event: time 1751299295.560000, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:27:26] 解析结果: key_code=663, value=0
[2025-07-11 14:27:26] ✓ 语音键(663)测试通过
[2025-07-11 14:27:31] 原始事件: Event: time 1751299300.680195, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:27:31] 解析结果: key_code=662, value=1
[2025-07-11 14:27:31] ✓ 检测到档位减(662)按下
[2025-07-11 14:27:31] 原始事件: Event: time 1751299300.980149, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:27:31] 解析结果: key_code=662, value=0
[2025-07-11 14:27:31] ✓ 档位减(662)测试通过
[2025-07-11 14:27:34] 原始事件: Event: time 1751299303.129997, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:27:34] 解析结果: key_code=663, value=1
[2025-07-11 14:27:34] ✓ 检测到语音键(663)按下
[2025-07-11 14:27:34] 原始事件: Event: time 1751299303.330069, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:27:34] 解析结果: key_code=663, value=0
[2025-07-11 14:27:34] ✓ 语音键(663)测试通过
[2025-07-11 14:27:34] 原始事件: Event: time 1751299303.830134, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 14:27:34] 解析结果: key_code=657, value=1
[2025-07-11 14:27:34] ✓ 检测到SOS键(657)按下
[2025-07-11 14:27:35] 原始事件: Event: time 1751299304.080168, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 14:27:35] 解析结果: key_code=657, value=0
[2025-07-11 14:27:35] ✓ SOS键(657)测试通过
[2025-07-11 14:27:36] 原始事件: Event: time 1751299305.209999, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:27:36] 解析结果: key_code=656, value=1
[2025-07-11 14:27:36] ✓ 检测到锁定键(656)按下
[2025-07-11 14:27:36] 原始事件: Event: time 1751299305.483367, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:27:36] 解析结果: key_code=656, value=0
[2025-07-11 14:27:36] ✓ 锁定键(656)测试通过
[2025-07-11 14:27:43] 原始事件: Event: time 1751299312.710026, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:27:43] 解析结果: key_code=663, value=1
[2025-07-11 14:27:43] ✓ 检测到语音键(663)按下
[2025-07-11 14:27:43] 原始事件: Event: time 1751299312.930035, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:27:43] 解析结果: key_code=663, value=0
[2025-07-11 14:27:43] ✓ 语音键(663)测试通过
[2025-07-11 14:27:45] 原始事件: Event: time 1751299314.230057, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:27:45] 解析结果: key_code=662, value=1
[2025-07-11 14:27:45] ✓ 检测到档位减(662)按下
[2025-07-11 14:27:45] 原始事件: Event: time 1751299314.456742, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:27:45] 解析结果: key_code=662, value=0
[2025-07-11 14:27:45] ✓ 档位减(662)测试通过
[2025-07-11 14:27:50] 原始事件: Event: time 1751299319.606678, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:27:50] 解析结果: key_code=656, value=1
[2025-07-11 14:27:50] ✓ 检测到锁定键(656)按下
[2025-07-11 14:27:50] 原始事件: Event: time 1751299319.830018, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:27:50] 解析结果: key_code=656, value=0
[2025-07-11 14:27:50] ✓ 锁定键(656)测试通过
[2025-07-11 14:27:51] 原始事件: Event: time 1751299320.980071, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:27:51] 解析结果: key_code=656, value=1
[2025-07-11 14:27:51] ✓ 检测到锁定键(656)按下
[2025-07-11 14:27:51] 原始事件: Event: time 1751299321.033436, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:27:51] 解析结果: key_code=660, value=1
[2025-07-11 14:27:52] ✓ 检测到智驾键(660)按下
[2025-07-11 14:27:52] 原始事件: Event: time 1751299321.256682, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:27:52] 解析结果: key_code=660, value=0
[2025-07-11 14:27:52] ✓ 智驾键(660)测试通过
[2025-07-11 14:27:52] 原始事件: Event: time 1751299321.280019, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:27:52] 解析结果: key_code=656, value=0
[2025-07-11 14:27:52] ✓ 锁定键(656)测试通过
[2025-07-11 14:27:52] 原始事件: Event: time 1751299321.856672, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:27:52] 解析结果: key_code=660, value=1
[2025-07-11 14:27:52] ✓ 检测到智驾键(660)按下
[2025-07-11 14:27:52] 原始事件: Event: time 1751299322.056801, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:27:52] 解析结果: key_code=660, value=0
[2025-07-11 14:27:52] ✓ 智驾键(660)测试通过
[2025-07-11 14:27:55] 原始事件: Event: time 1751299324.879995, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:27:55] 解析结果: key_code=663, value=1
[2025-07-11 14:27:55] ✓ 检测到语音键(663)按下
[2025-07-11 14:27:56] 原始事件: Event: time 1751299325.156667, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:27:56] 解析结果: key_code=663, value=0
[2025-07-11 14:27:56] ✓ 语音键(663)测试通过
[2025-07-11 14:27:57] 原始事件: Event: time 1751299326.060003, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 14:27:57] 解析结果: key_code=657, value=1
[2025-07-11 14:27:57] ✓ 检测到SOS键(657)按下
[2025-07-11 14:27:57] 原始事件: Event: time 1751299326.080016, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:27:57] 解析结果: key_code=663, value=1
[2025-07-11 14:27:57] ✓ 检测到语音键(663)按下
[2025-07-11 14:27:57] 原始事件: Event: time 1751299326.179997, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 14:27:57] 解析结果: key_code=657, value=0
[2025-07-11 14:27:57] ✓ SOS键(657)测试通过
[2025-07-11 14:27:57] 原始事件: Event: time 1751299326.280104, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:27:57] 解析结果: key_code=663, value=0
[2025-07-11 14:27:57] ✓ 语音键(663)测试通过
[2025-07-11 14:27:58] 原始事件: Event: time 1751299327.230051, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:27:58] 解析结果: key_code=662, value=1
[2025-07-11 14:27:58] ✓ 检测到档位减(662)按下
[2025-07-11 14:27:58] 原始事件: Event: time 1751299327.506794, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:27:58] 解析结果: key_code=662, value=0
[2025-07-11 14:27:58] ✓ 档位减(662)测试通过
[2025-07-11 14:28:03] 原始事件: Event: time 1751299332.110052, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:28:03] 解析结果: key_code=660, value=1
[2025-07-11 14:28:03] ✓ 检测到智驾键(660)按下
[2025-07-11 14:28:03] 原始事件: Event: time 1751299332.356678, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:28:03] 解析结果: key_code=660, value=0
[2025-07-11 14:28:03] ✓ 智驾键(660)测试通过
[2025-07-11 14:28:03] 原始事件: Event: time 1751299332.606686, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:28:03] 解析结果: key_code=660, value=1
[2025-07-11 14:28:03] ✓ 检测到智驾键(660)按下
[2025-07-11 14:28:03] 原始事件: Event: time 1751299332.806679, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:28:03] 解析结果: key_code=660, value=0
[2025-07-11 14:28:03] ✓ 智驾键(660)测试通过
[2025-07-11 14:28:04] 原始事件: Event: time 1751299333.030025, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:28:04] 解析结果: key_code=660, value=1
[2025-07-11 14:28:04] ✓ 检测到智驾键(660)按下
[2025-07-11 14:28:04] 原始事件: Event: time 1751299333.233326, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:28:04] 解析结果: key_code=660, value=0
[2025-07-11 14:28:04] ✓ 智驾键(660)测试通过
[2025-07-11 14:28:04] 原始事件: Event: time 1751299333.756675, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:28:04] 解析结果: key_code=656, value=1
[2025-07-11 14:28:04] ✓ 检测到锁定键(656)按下
[2025-07-11 14:28:04] 原始事件: Event: time 1751299333.983347, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:28:04] 解析结果: key_code=656, value=0
[2025-07-11 14:28:04] ✓ 锁定键(656)测试通过
[2025-07-11 14:28:05] 原始事件: Event: time 1751299334.256676, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:28:05] 解析结果: key_code=656, value=1
[2025-07-11 14:28:05] ✓ 检测到锁定键(656)按下
[2025-07-11 14:28:05] 原始事件: Event: time 1751299334.456701, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:28:05] 解析结果: key_code=656, value=0
[2025-07-11 14:28:05] ✓ 锁定键(656)测试通过
[2025-07-11 14:28:05] 原始事件: Event: time 1751299334.707686, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:28:05] 解析结果: key_code=656, value=1
[2025-07-11 14:28:05] ✓ 检测到锁定键(656)按下
[2025-07-11 14:28:05] 原始事件: Event: time 1751299334.880000, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:28:05] 解析结果: key_code=656, value=0
[2025-07-11 14:28:05] ✓ 锁定键(656)测试通过
[2025-07-11 14:28:06] 原始事件: Event: time 1751299335.456712, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:28:06] 解析结果: key_code=660, value=1
[2025-07-11 14:28:06] ✓ 检测到智驾键(660)按下
[2025-07-11 14:28:06] 原始事件: Event: time 1751299335.633347, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:28:06] 解析结果: key_code=660, value=0
[2025-07-11 14:28:06] ✓ 智驾键(660)测试通过
[2025-07-11 14:28:08] 原始事件: Event: time 1751299338.056716, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:28:08] 解析结果: key_code=662, value=1
[2025-07-11 14:28:08] ✓ 检测到档位减(662)按下
[2025-07-11 14:28:09] 原始事件: Event: time 1751299338.330067, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:28:09] 解析结果: key_code=662, value=0
[2025-07-11 14:28:09] ✓ 档位减(662)测试通过
[2025-07-11 14:28:09] 按键测试失败 - 只检测到5个按键
[2025-07-11 14:28:11] 
开始执行: 按键灯测试
[2025-07-11 14:28:12] 开始LED背光灯测试...
[2025-07-11 14:28:12] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-11 14:28:12] LED控制命令执行成功
[2025-07-11 14:28:36] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-11 14:28:36] LED灯已关闭
[2025-07-11 14:28:36] LED测试通过 - 背光灯正常点亮
[2025-07-11 14:28:37] 
开始执行: 手电筒测试
[2025-07-11 14:28:37] 开始手电筒LED测试...
[2025-07-11 14:28:37] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-11 14:28:37] 手电筒控制命令执行成功
[2025-07-11 14:28:38] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-11 14:28:38] 手电筒已关闭
[2025-07-11 14:28:38] 手电筒测试通过 - 手电筒正常点亮
[2025-07-11 14:28:39] 
开始执行: 摇杆使能测试
[2025-07-11 14:28:39] 执行摇杆测试...
[2025-07-11 14:28:39] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-11 14:28:39] 命令执行成功
[2025-07-11 14:28:39] 返回数据: 255
[2025-07-11 14:28:39] 摇杆测试通过，值: 255
[2025-07-11 14:28:39] 
开始执行: 前摄像头测试
[2025-07-11 14:28:39] 开始执行前摄像头测试...
[2025-07-11 14:28:39] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-11 14:28:39] 命令执行成功，返回: 无输出
[2025-07-11 14:28:39] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-11 14:28:40] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.331608200
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-11 14:28:40] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-11 14:28:40] 拉取命令执行成功
[2025-07-11 14:28:40] 返回数据: [ 39%] /data/camera/cam0_3840x2160.jpg
[ 79%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 23.1 MB/s (164776 bytes in 0.007s)
[2025-07-11 14:28:40] 图片已保存: cam0_3840x2160.jpg
[2025-07-11 14:28:42] 用户确认结果: 通过
[2025-07-11 14:28:42] 
开始执行: 光感测试
[2025-07-11 14:28:42] 执行光感测试...
[2025-07-11 14:28:42] 执行命令: adb shell evtest /dev/input/event1
[2025-07-11 14:28:49] 光感测试完成 - 检测到数值变化
[2025-07-11 14:28:49] 数值从 1 变化到 0
[2025-07-11 14:28:50] 
开始执行: 回充摄像头测试
[2025-07-11 14:28:50] 开始执行回充摄像头测试...
[2025-07-11 14:28:50] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-11 14:28:50] 命令执行成功，返回: 无输出
[2025-07-11 14:28:50] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-11 14:28:51] 命令执行成功，返回: 无输出
[2025-07-11 14:28:51] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-11 14:28:51] 拉取命令执行成功
[2025-07-11 14:28:51] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 8.8 MB/s (51752 bytes in 0.006s)
[2025-07-11 14:28:51] 图片已保存: output.jpg
[2025-07-11 14:28:53] 用户确认结果: 通过
[2025-07-11 14:28:53] 
开始执行: 喇叭测试
[2025-07-11 14:28:53] 执行喇叭测试...
[2025-07-11 14:28:53] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-11 14:29:07] 命令执行成功
[2025-07-11 14:29:07] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-11 14:29:07] 音频播放完成
[2025-07-11 14:29:08] 
开始执行: 蓝牙测试
[2025-07-11 14:29:08] 执行蓝牙测试...
[2025-07-11 14:29:08] 启动蓝牙服务...
[2025-07-11 14:29:08] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-11 14:29:08] 开启蓝牙...
[2025-07-11 14:29:08] 开启蓝牙失败: 
[2025-07-11 14:29:08] 执行命令: adb shell bluetoothctl devices
[2025-07-11 14:29:08] 命令执行成功
[2025-07-11 14:29:08] 返回数据:
[2025-07-11 14:29:08]   No default controller available
[2025-07-11 14:29:08] ❌ 蓝牙测试失败，未发现蓝牙设备
[2025-07-11 14:29:08] 
开始执行: WiFi测试
[2025-07-11 14:29:08] 执行WiFi测试...
[2025-07-11 14:29:08] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-11 14:29:09] 命令执行失败: 
[2025-07-11 14:29:09] 
测试完成 - 通过率: 10/15
[2025-07-11 14:29:09] ❌ 存在测试失败项！
[2025-07-11 14:29:09] 测试记录已保存: records/123456_20250711_142909.json

