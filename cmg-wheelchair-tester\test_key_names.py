#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的按键名称显示
"""

import tkinter as tk
from tkinter import ttk
import time

def test_key_names_display():
    """测试按键名称显示"""
    print("=== 测试按键名称显示 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("按键名称显示测试")
    root.geometry("600x400")
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="按键名称显示测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 状态显示
    status_var = tk.StringVar(value="准备就绪")
    status_label = ttk.Label(main_frame, textvariable=status_var, font=("Arial", 12))
    status_label.pack(pady=10)
    
    # 按键状态显示
    expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
    # 按键名称映射
    key_names = {
        656: "锁定键",
        657: "SOS键", 
        658: "喇叭键",
        659: "档位加",
        660: "智驾键",
        661: "静音键",
        662: "档位减",
        663: "语音键"
    }
    key_vars = {}
    key_labels = {}
    
    keys_frame = ttk.LabelFrame(main_frame, text="按键状态", padding="10")
    keys_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    for i, key_code in enumerate(expected_keys):
        row = i // 4
        col = i % 4
        
        key_frame = ttk.Frame(keys_frame)
        key_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
        
        key_vars[key_code] = tk.StringVar(value="未测试")
        
        ttk.Label(key_frame, text=f"{key_names[key_code]}:", font=("Arial", 10)).pack(side=tk.LEFT)
        
        status_label = ttk.Label(
            key_frame,
            textvariable=key_vars[key_code],
            font=("Arial", 10, "bold"),
            foreground="gray"
        )
        status_label.pack(side=tk.LEFT, padx=(5, 0))
        key_labels[key_code] = status_label
    
    for i in range(4):
        keys_frame.columnconfigure(i, weight=1)
    
    # 提示
    hint_label = ttk.Label(
        main_frame,
        text="请依次按下手柄上的8个功能按键，每个按键按下并松开即可\n包括：锁定键、SOS键、喇叭键、档位加、智驾键、静音键、档位减、语音键",
        font=("Arial", 11),
        justify=tk.CENTER
    )
    hint_label.pack(pady=10)
    
    # 日志显示
    log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
    log_frame.pack(fill=tk.X, pady=10)
    
    log_text = tk.Text(log_frame, height=6, wrap=tk.WORD)
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message):
        timestamp = time.strftime("%H:%M:%S")
        log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 模拟按键测试
    def simulate_key_test():
        """模拟按键测试过程"""
        status_var.set("开始模拟测试...")
        log_message("开始模拟按键测试...")
        root.update_idletasks()
        
        for i, key_code in enumerate(expected_keys):
            key_name = key_names[key_code]
            
            # 模拟按键按下
            status_var.set(f"模拟{key_name}按下...")
            key_vars[key_code].set("按下")
            key_labels[key_code].configure(foreground="orange")
            log_message(f"✓ 检测到{key_name}({key_code})按下")
            root.update_idletasks()
            time.sleep(0.8)
            
            # 模拟按键松开
            status_var.set(f"模拟{key_name}松开...")
            key_vars[key_code].set("PASS")
            key_labels[key_code].configure(foreground="green")
            log_message(f"✓ {key_name}({key_code})测试通过")
            root.update_idletasks()
            time.sleep(0.8)
            
            # 更新进度
            status_var.set(f"已完成 {i+1}/8 个按键测试")
            root.update_idletasks()
        
        status_var.set("模拟测试完成!")
        log_message("所有按键测试完成")
        root.update_idletasks()
    
    # 重置按钮
    def reset_test():
        """重置测试状态"""
        status_var.set("准备就绪")
        for key_code in expected_keys:
            key_vars[key_code].set("未测试")
            key_labels[key_code].configure(foreground="gray")
        log_message("测试状态已重置")
        root.update_idletasks()
    
    # 显示按键映射
    def show_key_mapping():
        """显示按键映射信息"""
        log_message("=== 按键映射信息 ===")
        for key_code in expected_keys:
            log_message(f"按键{key_code}: {key_names[key_code]}")
        log_message("=== 映射信息结束 ===")
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    simulate_button = ttk.Button(
        button_frame,
        text="模拟测试",
        command=simulate_key_test
    )
    simulate_button.pack(side=tk.LEFT, padx=(0, 10))
    
    reset_button = ttk.Button(
        button_frame,
        text="重置",
        command=reset_test
    )
    reset_button.pack(side=tk.LEFT, padx=(0, 10))
    
    mapping_button = ttk.Button(
        button_frame,
        text="显示映射",
        command=show_key_mapping
    )
    mapping_button.pack(side=tk.LEFT)
    
    log_message("按键名称显示测试器已就绪")
    log_message("点击'模拟测试'查看新的按键名称显示")
    log_message("点击'显示映射'查看按键码与名称的对应关系")
    
    # 自动显示映射信息
    show_key_mapping()
    
    root.mainloop()

def test_key_name_parsing():
    """测试按键名称解析"""
    print("\n=== 测试按键名称解析 ===")
    
    # 按键名称映射
    key_names = {
        656: "锁定键",
        657: "SOS键", 
        658: "喇叭键",
        659: "档位加",
        660: "智驾键",
        661: "静音键",
        662: "档位减",
        663: "语音键"
    }
    
    # 模拟事件
    test_events = [
        ("Event: time 1751301654.282726, type 1 (EV_KEY), code 659 (?), value 1", 659, 1),
        ("Event: time 1751301654.282726, type 1 (EV_KEY), code 659 (?), value 0", 659, 0),
        ("Event: time 1751301654.282726, type 1 (EV_KEY), code 661 (?), value 1", 661, 1),
        ("Event: time 1751301654.282726, type 1 (EV_KEY), code 661 (?), value 0", 661, 0),
        ("Event: time 1751301654.282726, type 1 (EV_KEY), code 656 (?), value 1", 656, 1),
        ("Event: time 1751301654.282726, type 1 (EV_KEY), code 656 (?), value 0", 656, 0),
    ]
    
    print("测试按键名称解析...")
    for event_line, expected_code, expected_value in test_events:
        print(f"\n处理事件: {event_line}")
        
        # 解析事件
        key_code = None
        value = None
        
        if "EV_KEY" in event_line and "code " in event_line:
            parts = event_line.split(", ")
            for part in parts:
                part = part.strip()
                if "code " in part:
                    code_match = part.split("code ")[1].split(" ")[0]
                    key_code = int(code_match)
                elif "value " in part:
                    value = int(part.split("value ")[1])
        
        # 验证解析结果
        if key_code == expected_code and value == expected_value:
            key_name = key_names.get(key_code, f"按键{key_code}")
            if value == 1:
                print(f"  ✓ 检测到{key_name}({key_code})按下")
            elif value == 0:
                print(f"  ✓ {key_name}({key_code})测试通过")
        else:
            print(f"  ✗ 解析失败: 期望({expected_code}, {expected_value}), 实际({key_code}, {value})")
    
    print("\n按键名称解析测试完成")

if __name__ == "__main__":
    print("按键名称显示测试工具")
    print("=" * 50)
    
    # 测试按键名称解析
    test_key_name_parsing()
    
    # 测试UI显示
    print("\n启动UI测试...")
    test_key_names_display()
