#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试4G模组版本测试功能
验证：
1. ATI命令的发送和接收
2. Revision行的解析和模组型号提取
3. 只显示模组型号部分（如EG912UGLAAR03A15M08）
"""

import subprocess
import time
import re
import json
import os

def test_ati_command():
    """测试ATI命令"""
    print("=== 测试ATI命令 ===")
    
    try:
        # 模拟ATI命令的输出
        sample_output = """
AT
OK

ATI
Quectel
EG912U
Revision: EG912UGLAAR03A15M08

OK
"""
        
        print("模拟ATI命令输出:")
        for line in sample_output.strip().split('\n'):
            if line.strip():
                print(f"  {line}")
        
        # 测试正则表达式
        revision_pattern = re.compile(r'Revision:\s*([A-Z0-9]+)', re.IGNORECASE)
        
        for line in sample_output.split('\n'):
            match = revision_pattern.search(line)
            if match:
                module_version = match.group(1)
                print(f"\n✅ 成功提取模组型号: {module_version}")
                return module_version
        
        print("❌ 未找到模组版本信息")
        return None
        
    except Exception as e:
        print(f"❌ 测试ATI命令失败: {str(e)}")
        return None

def test_regex_patterns():
    """测试不同格式的正则表达式匹配"""
    print("\n=== 测试正则表达式匹配 ===")
    
    test_cases = [
        "Revision: EG912UGLAAR03A15M08",
        "Revision:EG912UGLAAR03A15M08",
        "revision: EG912UGLAAR03A15M08",
        "REVISION: EG912UGLAAR03A15M08",
        "Revision: EC25EFAR06A08M4G",
        "Revision: SIM7600G-H",
        "Revision:   EG912UGLAAR03A15M08   ",
    ]
    
    revision_pattern = re.compile(r'Revision:\s*([A-Z0-9]+)', re.IGNORECASE)
    
    print("测试用例:")
    for i, test_case in enumerate(test_cases, 1):
        print(f"{i}. {test_case}")
        match = revision_pattern.search(test_case)
        if match:
            module_version = match.group(1)
            print(f"   ✅ 匹配成功: {module_version}")
        else:
            print(f"   ❌ 匹配失败")
    
    return True

def test_adb_connection():
    """测试ADB连接"""
    print("\n=== 测试ADB连接 ===")
    
    try:
        result = subprocess.run(['adb', 'devices'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            output = result.stdout.strip()
            lines = output.split('\n')
            devices = [line for line in lines[1:] if line.strip() and 'device' in line]
            
            if devices:
                print(f"✅ ADB连接正常，检测到 {len(devices)} 个设备:")
                for device in devices:
                    print(f"   {device}")
                return True
            else:
                print("❌ 未检测到ADB设备")
                return False
        else:
            print(f"❌ ADB命令执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 检查ADB连接失败: {str(e)}")
        return False

def test_ttyusb0_access():
    """测试ttyUSB0设备访问"""
    print("\n=== 测试ttyUSB0设备访问 ===")
    
    try:
        # 检查ttyUSB0设备是否存在
        result = subprocess.run(['adb', 'shell', 'ls -l /dev/ttyUSB0'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            print("✅ ttyUSB0设备存在:")
            print(f"   {result.stdout.strip()}")
            
            # 测试设备权限
            result2 = subprocess.run(['adb', 'shell', 'cat /dev/ttyUSB0 &'], 
                                   capture_output=True, text=True, timeout=2)
            
            if result2.returncode == 0:
                print("✅ ttyUSB0设备可读取")
            else:
                print(f"⚠️ ttyUSB0设备读取权限问题: {result2.stderr}")
            
            return True
        else:
            print(f"❌ ttyUSB0设备不存在: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试ttyUSB0设备失败: {str(e)}")
        return False

def simulate_4g_test():
    """模拟4G模组版本测试"""
    print("\n=== 模拟4G模组版本测试 ===")
    
    print("模拟测试步骤:")
    print("1. 启动串口监听线程")
    print("2. 发送ATI命令到/dev/ttyUSB0")
    print("3. 监听串口返回数据")
    print("4. 解析Revision行提取模组型号")
    
    # 模拟串口返回数据
    mock_serial_data = [
        "AT",
        "OK",
        "",
        "ATI",
        "Quectel",
        "EG912U",
        "Revision: EG912UGLAAR03A15M08",
        "",
        "OK"
    ]
    
    print("\n模拟串口返回数据:")
    revision_pattern = re.compile(r'Revision:\s*([A-Z0-9]+)', re.IGNORECASE)
    module_version = None
    
    for line in mock_serial_data:
        if line.strip():
            print(f"串口输出: {line}")
            match = revision_pattern.search(line)
            if match:
                module_version = match.group(1)
                print(f"检测到模组版本: {module_version}")
                break
    
    if module_version:
        print(f"\n✅ 4G模组版本测试成功，模组型号: {module_version}")
        return True
    else:
        print("\n❌ 未检测到模组版本信息")
        return False

def check_config_update():
    """检查配置文件更新"""
    print("\n=== 检查配置文件更新 ===")
    
    if not os.path.exists('config.json'):
        print("❌ config.json 文件不存在")
        return False
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 查找4G测试项目
        test_project = None
        for project in config.get("test_projects", []):
            if project.get("id") == "4g_test":
                test_project = project
                break
        
        if not test_project:
            print("❌ 未找到4G测试项目配置")
            return False
        
        print("✅ 找到4G测试项目配置:")
        print(f"   ID: {test_project['id']}")
        print(f"   名称: {test_project['name']}")
        print(f"   描述: {test_project['description']}")
        print(f"   类型: {test_project['type']}")
        print(f"   命令: {test_project['commands']}")
        print(f"   期望模式: {test_project['expected_pattern']}")
        
        # 检查是否已更新为ATI命令
        commands = test_project.get('commands', [])
        ati_found = any('ATI' in cmd for cmd in commands)
        revision_pattern = test_project.get('expected_pattern', '')
        
        if ati_found and 'Revision' in revision_pattern:
            print("✅ 配置已正确更新为ATI命令和Revision模式")
            return True
        else:
            print("❌ 配置未正确更新")
            return False
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {str(e)}")
        return False

def provide_test_guidance():
    """提供测试指导"""
    print("\n📋 4G模组版本测试修改说明:")
    
    print("\n🔧 修改内容:")
    print("1. 测试名称: '4G模组测试' → '4G模组版本测试'")
    print("2. AT命令: 'AT+CCID' → 'ATI'")
    print("3. 命令发送: echo -e 'AT+CCID\\r' → echo -e 'ATI\\r'")
    print("4. 数据解析: 提取CCID → 提取Revision行中的模组型号")
    print("5. 结果显示: 显示完整CCID → 只显示模组型号")
    
    print("\n📋 ATI命令返回示例:")
    print("ATI")
    print("Quectel")
    print("EG912U")
    print("Revision: EG912UGLAAR03A15M08")
    print("OK")
    
    print("\n🔍 解析逻辑:")
    print("- 查找包含'Revision:'的行")
    print("- 使用正则表达式提取冒号后的模组型号")
    print("- 只显示模组型号部分：EG912UGLAAR03A15M08")
    
    print("\n✅ 预期结果:")
    print("- 测试成功时显示：模组型号: EG912UGLAAR03A15M08")
    print("- 测试失败时显示：未检测到模组版本")
    print("- 日志中显示完整的串口通信过程")

def main():
    """主测试函数"""
    print("4G模组版本测试功能验证")
    print("=" * 50)
    
    # 提供测试指导
    provide_test_guidance()
    
    # 测试ATI命令
    module_version = test_ati_command()
    
    # 测试正则表达式
    regex_success = test_regex_patterns()
    
    # 检查ADB连接
    adb_success = test_adb_connection()
    
    # 测试ttyUSB0访问（如果ADB连接正常）
    ttyusb0_success = False
    if adb_success:
        ttyusb0_success = test_ttyusb0_access()
    
    # 模拟4G测试
    simulation_success = simulate_4g_test()
    
    # 检查配置更新
    config_success = check_config_update()
    
    # 总结测试结果
    print("\n📊 测试结果总结:")
    print(f"ATI命令解析: {'✅' if module_version else '❌'}")
    print(f"正则表达式: {'✅' if regex_success else '❌'}")
    print(f"ADB连接: {'✅' if adb_success else '❌'}")
    print(f"ttyUSB0访问: {'✅' if ttyusb0_success else '❌' if adb_success else '⏭️'}")
    print(f"测试模拟: {'✅' if simulation_success else '❌'}")
    print(f"配置更新: {'✅' if config_success else '❌'}")
    
    if module_version and regex_success and simulation_success and config_success:
        print("\n🎉 4G模组版本测试功能修改成功！")
        print("- ATI命令解析正常")
        print("- 模组型号提取正确")
        print("- 配置文件已更新")
    else:
        print("\n⚠️ 部分功能需要检查")
    
    print("\n📝 使用说明:")
    print("- 在实际测试中，程序会发送ATI命令到/dev/ttyUSB0")
    print("- 解析返回的Revision行提取模组型号")
    print("- 测试结果只显示模组型号，不显示完整信息")

if __name__ == "__main__":
    main()
