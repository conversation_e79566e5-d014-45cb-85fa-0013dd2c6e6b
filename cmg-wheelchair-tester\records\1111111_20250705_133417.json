{"sn": "1111111", "timestamp": "2025-07-05T13:34:17.766841", "results": {"usb_test": {"test_name": "USB关键器件测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T13:34:15.662111"}, "can_test": {"test_name": "CAN0测试", "status": "失败", "message": "CAN0 down失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T13:34:16.736182"}, "gps_test": {"test_name": "GPS测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "4g_test": {"test_name": "4G模组测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "key_test": {"test_name": "按键测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "led_test": {"test_name": "按键灯测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "torch_test": {"test_name": "手电筒测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "light_sensor_test": {"test_name": "光感测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "speaker_test": {"test_name": "喇叭测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}, "wifi_test": {"test_name": "WiFi测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T13:34:15.583240"}}}