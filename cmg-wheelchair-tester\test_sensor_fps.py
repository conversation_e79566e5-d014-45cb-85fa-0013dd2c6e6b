#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试传感器FPS数据解析功能
"""

import subprocess
import re
import time

def test_adb_connection():
    """测试ADB连接"""
    print("=== 测试ADB连接 ===")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        print(f"ADB devices返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("ADB连接正常")
            devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            connected_devices = [line for line in devices if 'device' in line]
            
            if connected_devices:
                print(f"已连接设备: {len(connected_devices)}个")
                for device in connected_devices:
                    print(f"  {device}")
                return True
            else:
                print("❌ 没有连接的设备")
                return False
        else:
            print(f"❌ ADB连接失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ ADB连接测试出错: {str(e)}")
        return False

def parse_sensor_fps_data(log_content):
    """解析传感器FPS数据"""
    try:
        import re
        
        print("开始解析传感器FPS数据...")
        
        # 查找包含"sensor fps:"关键字段的行
        lines = log_content.split('\n')
        fps_sections = []
        
        print(f"日志总行数: {len(lines)}")
        
        # 找到所有包含"sensor fps:"的行及其后续的FPS数据
        for i, line in enumerate(lines):
            if "sensor fps:" in line:
                print(f"找到sensor fps关键字段在第 {i+1} 行: {line.strip()}")
                
                # 收集这个section的FPS数据
                section_data = {}
                # 从下一行开始查找FPS数据，最多查找10行
                for j in range(i + 1, min(i + 11, len(lines))):
                    fps_line = lines[j].strip()
                    if not fps_line:
                        continue
                    
                    print(f"  检查第 {j+1} 行: {fps_line}")
                    
                    # 匹配FPS数据格式: FPS Accel: 184
                    fps_match = re.match(r'FPS\s+(\w+):\s+(\d+)', fps_line)
                    if fps_match:
                        sensor_name = fps_match.group(1)
                        fps_value = int(fps_match.group(2))
                        section_data[sensor_name] = fps_value
                        print(f"    ✅ 解析到: {sensor_name} = {fps_value}")
                    elif fps_line.startswith('FPS '):
                        print(f"    ⚠️ FPS行格式不匹配: {fps_line}")
                        continue
                    else:
                        print(f"    ❌ 非FPS行，结束section: {fps_line}")
                        break
                
                if section_data:
                    fps_sections.append(section_data)
                    print(f"  完成section {len(fps_sections)}: {section_data}")
        
        print(f"总共找到 {len(fps_sections)} 个FPS数据段")
        
        if not fps_sections:
            return None
        
        # 取最近5条数据（如果不足5条则取全部）
        recent_sections = fps_sections[-5:]
        print(f"使用最近 {len(recent_sections)} 条数据计算平均值")
        
        # 计算每个传感器的平均FPS
        sensor_averages = {}
        all_sensors = set()
        
        # 收集所有传感器名称
        for section in recent_sections:
            all_sensors.update(section.keys())
        
        print(f"发现的传感器: {list(all_sensors)}")
        
        # 计算每个传感器的平均值
        for sensor in all_sensors:
            values = []
            for section in recent_sections:
                if sensor in section:
                    values.append(section[sensor])
            
            if values:
                avg_fps = sum(values) / len(values)
                sensor_averages[sensor] = avg_fps
                print(f"传感器 {sensor}: {values} -> 平均 {avg_fps:.1f}")
        
        return sensor_averages
        
    except Exception as e:
        print(f"解析传感器FPS数据出错: {str(e)}")
        return None

def test_sensor_parsing_with_sample_data():
    """使用示例数据测试传感器解析功能"""
    print("\n=== 测试传感器解析功能（示例数据） ===")
    
    # 模拟传感器日志数据
    sample_log = """
I0701 00:23:05.936022(1404319) sensor_service.cc:223] sensor fps:
FPS Accel: 180
FPS Gyro: 182
FPS LaserVector: 190
FPS LaserVector2: 195
FPS Odom: 98

I0701 00:23:08.936022(1404319) sensor_service.cc:223] sensor fps:
FPS Accel: 184
FPS Gyro: 186
FPS LaserVector: 196
FPS LaserVector2: 200
FPS Odom: 100

I0701 00:23:11.936022(1404319) sensor_service.cc:223] sensor fps:
FPS Accel: 188
FPS Gyro: 190
FPS LaserVector: 200
FPS LaserVector2: 205
FPS Odom: 102

I0701 00:23:14.936022(1404319) sensor_service.cc:223] sensor fps:
FPS Accel: 182
FPS Gyro: 184
FPS LaserVector: 194
FPS LaserVector2: 198
FPS Odom: 99

I0701 00:23:17.936022(1404319) sensor_service.cc:223] sensor fps:
FPS Accel: 186
FPS Gyro: 188
FPS LaserVector: 198
FPS LaserVector2: 202
FPS Odom: 101
"""
    
    print("示例日志数据:")
    print(sample_log)
    
    # 解析数据
    fps_data = parse_sensor_fps_data(sample_log)
    
    if fps_data:
        print("\n✅ 传感器解析成功")
        print("传感器FPS平均数据:")
        
        result_messages = []
        for sensor_name, avg_fps in fps_data.items():
            print(f"  {sensor_name}: {avg_fps:.1f} FPS")
            result_messages.append(f"{sensor_name}:{avg_fps:.1f}")
        
        result_string = " | ".join(result_messages)
        print(f"\n最终结果字符串: {result_string}")
        return True
    else:
        print("❌ 传感器解析失败")
        return False

def test_real_sensor_command():
    """测试真实的传感器命令"""
    print("\n=== 测试真实传感器命令 ===")
    
    try:
        # 执行cat命令读取传感器日志
        command = "cat /sdcard/lmv/normal_logs/sensor/sensor_newest"
        print(f"执行命令: adb shell {command}")
        
        result = subprocess.run(['adb', 'shell', command], 
                              capture_output=True, text=True, timeout=15)
        
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print("✅ 传感器日志读取成功")
            print(f"日志长度: {len(output)} 字符")
            
            # 显示日志的前几行和后几行
            lines = output.split('\n')
            print(f"日志总行数: {len(lines)}")
            
            if len(lines) > 10:
                print("\n前5行:")
                for i, line in enumerate(lines[:5]):
                    print(f"  {i+1}: {line}")
                
                print("\n后5行:")
                for i, line in enumerate(lines[-5:], len(lines)-4):
                    print(f"  {i}: {line}")
            else:
                print("\n完整日志:")
                for i, line in enumerate(lines):
                    print(f"  {i+1}: {line}")
            
            # 解析传感器FPS数据
            fps_data = parse_sensor_fps_data(output)
            
            if fps_data:
                print("\n✅ 传感器FPS解析成功")
                print("传感器FPS平均数据:")
                
                result_messages = []
                for sensor_name, avg_fps in fps_data.items():
                    print(f"  {sensor_name}: {avg_fps:.1f} FPS")
                    result_messages.append(f"{sensor_name}:{avg_fps:.1f}")
                
                result_string = " | ".join(result_messages)
                print(f"\n📋 测试结果: {result_string}")
                return True, result_string
            else:
                print("❌ 未找到有效的传感器FPS数据")
                return False, "未找到FPS数据"
        else:
            print(f"❌ 传感器日志读取失败: {result.stderr}")
            return False, "日志读取失败"
            
    except Exception as e:
        print(f"❌ 传感器命令测试出错: {str(e)}")
        return False, f"错误: {str(e)}"

def test_complete_sensor_flow():
    """测试完整的传感器测试流程"""
    print("\n=== 测试完整的传感器测试流程 ===")
    
    print("步骤1: 测试传感器解析算法...")
    parsing_success = test_sensor_parsing_with_sample_data()
    
    if not parsing_success:
        print("❌ 传感器解析算法测试失败")
        return False
    
    print("\n步骤2: 测试真实设备...")
    sensor_success, result = test_real_sensor_command()
    
    if sensor_success:
        print(f"\n🎉 传感器测试成功！")
        print(f"传感器FPS数据: {result}")
        return True
    else:
        print(f"\n❌ 传感器测试失败: {result}")
        return False

if __name__ == "__main__":
    print("传感器FPS数据测试功能验证")
    print("=" * 50)
    
    # 测试ADB连接
    if not test_adb_connection():
        print("\n❌ ADB连接失败，无法进行传感器测试")
        exit(1)
    
    # 测试完整的传感器测试流程
    success = test_complete_sensor_flow()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 传感器FPS数据测试功能验证成功！")
    else:
        print("❌ 传感器FPS数据测试功能验证失败")
    
    print("\n测试完成")
