import os
import sys
import tabula
import pandas as pd
from tkinter import Tk, filedialog, messagebox, Label, Button, StringVar
from tkinter.ttk import Progressbar

class PDFToExcelConverter:
    def __init__(self):
        self.window = Tk()
        self.window.title("PDF转Excel工具")
        self.window.geometry("400x200")
        
        # 设置窗口样式
        self.window.configure(bg='#f0f0f0')
        
        # 创建变量
        self.pdf_path = StringVar()
        self.output_path = StringVar()
        
        # 创建界面元素
        self.create_widgets()
        
    def create_widgets(self):
        # 选择PDF文件按钮
        Label(self.window, text="PDF转Excel工具", font=('Arial', 16), bg='#f0f0f0').pack(pady=10)
        
        Button(self.window, text="选择PDF文件", command=self.select_pdf, 
               bg='#4CAF50', fg='white', width=20).pack(pady=5)
        
        But<PERSON>(self.window, text="选择保存位置", command=self.select_output, 
               bg='#2196F3', fg='white', width=20).pack(pady=5)
        
        Button(self.window, text="开始转换", command=self.convert, 
               bg='#FF9800', fg='white', width=20).pack(pady=5)
        
        # 进度条
        self.progress = Progressbar(self.window, length=300, mode='determinate')
        self.progress.pack(pady=10)
        
    def select_pdf(self):
        file_path = filedialog.askopenfilename(
            filetypes=[("PDF files", "*.pdf")]
        )
        if file_path:
            self.pdf_path.set(file_path)
            
    def select_output(self):
        file_path = filedialog.asksaveasfilename(
            defaultextension=".xlsx",
            filetypes=[("Excel files", "*.xlsx")]
        )
        if file_path:
            self.output_path.set(file_path)
            
    def convert(self):
        if not self.pdf_path.get() or not self.output_path.get():
            messagebox.showerror("错误", "请选择PDF文件和保存位置！")
            return
            
        try:
            # 读取PDF中的所有表格
            self.progress['value'] = 20
            self.window.update()
            
            tables = tabula.read_pdf(self.pdf_path.get(), pages='all')
            
            self.progress['value'] = 50
            self.window.update()
            
            # 将所有表格分sheet写入Excel文件
            if tables:
                with pd.ExcelWriter(self.output_path.get()) as writer:
                    for i, table in enumerate(tables):
                        table.to_excel(writer, sheet_name=f'Table_{i+1}', index=False)
            else:
                messagebox.showerror("错误", "未在PDF中检测到表格！")
                return
            
            self.progress['value'] = 100
            self.window.update()
            
            messagebox.showinfo("成功", "转换完成！")
            
        except Exception as e:
            messagebox.showerror("错误", f"转换过程中出现错误：{str(e)}")
            
    def run(self):
        self.window.mainloop()

if __name__ == "__main__":
    app = PDFToExcelConverter()
    app.run() 