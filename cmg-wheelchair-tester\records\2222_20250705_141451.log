[2025-07-05 14:14:43] 开始测试设备: 2222
[2025-07-05 14:14:43] 测试数据已清除
[2025-07-05 14:14:43] 开始执行全部测试...
[2025-07-05 14:14:43] 开始测试: USB关键器件测试
[2025-07-05 14:14:43] 执行USB设备检测...
[2025-07-05 14:14:43] 执行命令: adb shell lsusb
[2025-07-05 14:14:43] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:14:44] 开始测试: CAN0测试
[2025-07-05 14:14:44] 执行CAN0测试流程...
[2025-07-05 14:14:44] 执行命令: adb shell ip link set can0 down
[2025-07-05 14:14:44] CAN0 down失败: error: no devices/emulators found

[2025-07-05 14:14:45] 开始测试: GPS测试
[2025-07-05 14:14:45] 执行GPS测试...
[2025-07-05 14:14:45] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-05 14:14:45] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:14:45] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-05 14:14:45] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:14:45] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-05 14:14:45] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:14:45] GPS信号检测失败
[2025-07-05 14:14:46] 开始测试: 4G模组测试
[2025-07-05 14:14:46] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-05 14:14:46] 监听线程已启动，等待串口数据...
[2025-07-05 14:14:48] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-05 14:14:48] AT指令发送失败: error: no devices/emulators found

[2025-07-05 14:14:49] 开始测试: 按键测试
[2025-07-05 14:14:49] 执行按键测试...
[2025-07-05 14:14:50] 请按手柄按键进行测试...
[2025-07-05 14:14:50] 正在停止测试...
[2025-07-05 14:14:51] 全部测试完成
[2025-07-05 14:14:51] 测试记录已保存: records/2222_20250705_141451.json

