#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试4G网络测试功能
验证：
1. 4G网络测试项目已添加到配置
2. 测试前关闭WiFi (ifconfig wlan0 down)
3. 使用4G网络ping www.baidu.com
4. 计算10秒延时平均值
5. 测试后打开WiFi (ifconfig wlan0 up)
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import subprocess
import threading
import time
import re

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}, "test_projects": []}

def test_4g_network_config():
    """测试4G网络配置"""
    print("=== 测试4G网络配置 ===")
    
    config = load_config()
    test_projects = config.get("test_projects", [])
    work_processes = config.get("work_processes", {})
    
    # 检查4G网络测试项目是否存在
    found_4g_test = False
    for project in test_projects:
        if project.get("id") == "4g_network_test":
            found_4g_test = True
            print(f"✅ 找到4G网络测试项目:")
            print(f"  ID: {project['id']}")
            print(f"  名称: {project['name']}")
            print(f"  描述: {project['description']}")
            print(f"  类型: {project['type']}")
            print(f"  测试时长: {project.get('test_duration', 'N/A')}秒")
            print(f"  ping目标: {project.get('ping_target', 'N/A')}")
            break
    
    if not found_4g_test:
        print("❌ 未找到4G网络测试项目")
        return False
    
    # 检查工序配置中是否包含4G网络测试
    for process_name, process_config in work_processes.items():
        test_ids = process_config.get("test_ids", [])
        if "4g_network_test" in test_ids:
            print(f"✅ 工序 '{process_name}' 包含4G网络测试")
            # 检查4G测试是否在WiFi测试前
            try:
                g4_index = test_ids.index("4g_network_test")
                wifi_index = test_ids.index("wifi_test")
                if g4_index < wifi_index:
                    print(f"✅ 4G网络测试在WiFi测试前 (位置: {g4_index+1} < {wifi_index+1})")
                else:
                    print(f"❌ 4G网络测试应该在WiFi测试前")
            except ValueError:
                print("⚠️ 未找到WiFi测试项目")
    
    return True

def simulate_4g_network_test():
    """模拟4G网络测试"""
    print("\n=== 模拟4G网络测试 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("4G网络测试模拟")
    root.geometry("800x600")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 800) // 2
    y = (screen_height - 600) // 2
    root.geometry(f"800x600+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="4G网络测试功能验证", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 测试说明
    info_frame = ttk.LabelFrame(main_frame, text="4G网络测试流程", padding="15")
    info_frame.pack(fill=tk.X, pady=(0, 20))
    
    info_text = """🔧 4G网络测试流程:

1. 关闭WiFi网络:
   • 执行命令: adb shell ifconfig wlan0 down
   • 确保网络流量走4G网络

2. 4G网络ping测试:
   • 目标: www.baidu.com
   • 时长: 10秒 (10个ping包)
   • 提取延时数据

3. 计算延时统计:
   • 平均延时
   • 最小延时
   • 最大延时
   • 成功率

4. 恢复WiFi网络:
   • 执行命令: adb shell ifconfig wlan0 up
   • 等待WiFi重新连接

✅ 测试通过条件: 至少80%的ping包成功返回"""
    
    ttk.Label(info_frame, text=info_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 状态显示
    status_var = tk.StringVar(value="准备开始4G网络测试...")
    status_label = ttk.Label(main_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack(pady=(0, 15))
    
    # 日志显示
    log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    log_text = tk.Text(log_frame, height=15, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message, color=None):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if color:
            start_pos = log_text.index(tk.END + "-1c")
            log_text.insert(tk.END, log_entry)
            end_pos = log_text.index(tk.END + "-1c")
            log_text.tag_add(color, start_pos, end_pos)
            log_text.tag_config(color, foreground=color)
        else:
            log_text.insert(tk.END, log_entry)
        
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 测试状态
    test_state = {"running": False}
    
    def run_4g_test_simulation():
        """运行4G网络测试模拟"""
        test_state["running"] = True
        status_var.set("正在执行4G网络测试...")
        
        def simulate_test():
            try:
                log_message("执行4G网络测试...", "blue")
                log_message("第一步：关闭WiFi网络...")
                
                # 模拟关闭WiFi
                log_message("执行命令: adb shell ifconfig wlan0 down")
                time.sleep(1)
                log_message("WiFi已关闭，等待4G网络连接...")
                time.sleep(2)
                
                log_message("第二步：使用4G网络进行ping测试...")
                log_message("ping目标: www.baidu.com")
                log_message("测试时长: 10秒")
                
                # 模拟ping测试
                log_message("执行命令: adb shell ping -c 10 www.baidu.com")
                
                # 模拟ping输出
                ping_results = []
                for i in range(1, 11):
                    if not test_state["running"]:
                        break
                    
                    # 模拟延时（随机生成20-80ms的延时）
                    import random
                    delay = random.uniform(20.0, 80.0)
                    ping_results.append(delay)
                    
                    log_message(f"64 bytes from 14.215.177.38: icmp_seq={i} ttl=54 time={delay:.1f} ms")
                    time.sleep(0.8)
                
                if test_state["running"]:
                    # 计算统计数据
                    if ping_results:
                        avg_delay = sum(ping_results) / len(ping_results)
                        min_delay = min(ping_results)
                        max_delay = max(ping_results)
                        success_rate = len(ping_results) / 10 * 100
                        
                        log_message("4G网络延时统计:", "green")
                        log_message(f"  成功ping包: {len(ping_results)} 个")
                        log_message(f"  平均延时: {avg_delay:.1f} ms")
                        log_message(f"  最小延时: {min_delay:.1f} ms")
                        log_message(f"  最大延时: {max_delay:.1f} ms")
                        log_message(f"  成功率: {success_rate:.1f}%")
                        
                        if success_rate >= 80:
                            log_message("4G网络测试通过 ✅", "green")
                            status_var.set(f"4G网络测试通过 - 平均延时: {avg_delay:.1f}ms")
                        else:
                            log_message("4G网络测试失败：丢包率过高 ❌", "red")
                            status_var.set("4G网络测试失败 - 丢包率过高")
                    
                    log_message("第三步：重新打开WiFi网络...")
                    log_message("执行命令: adb shell ifconfig wlan0 up")
                    time.sleep(1)
                    log_message("WiFi已重新打开")
                    time.sleep(1)
                    log_message("4G网络测试完成", "blue")
                
            except Exception as e:
                log_message(f"4G网络测试出错: {str(e)}", "red")
            finally:
                test_state["running"] = False
        
        threading.Thread(target=simulate_test, daemon=True).start()
    
    def run_real_4g_test():
        """运行真实的4G网络测试"""
        if not messagebox.askyesno("确认", "这将执行真实的4G网络测试，会临时关闭WiFi。确定继续吗？"):
            return
        
        test_state["running"] = True
        status_var.set("正在执行真实4G网络测试...")
        
        def real_test():
            try:
                log_message("执行真实4G网络测试...", "blue")
                log_message("第一步：关闭WiFi网络...")
                
                # 真实关闭WiFi
                result = subprocess.run(
                    ["adb", "shell", "ifconfig", "wlan0", "down"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    log_message("WiFi已关闭")
                else:
                    log_message(f"关闭WiFi失败: {result.stderr}", "red")
                    return
                
                time.sleep(3)
                log_message("第二步：使用4G网络进行ping测试...")
                
                # 真实ping测试
                result = subprocess.run(
                    ["adb", "shell", "ping", "-c", "10", "www.baidu.com"],
                    capture_output=True,
                    text=True,
                    timeout=20
                )
                
                if result.returncode == 0:
                    ping_output = result.stdout
                    log_message("ping测试输出:")
                    for line in ping_output.split('\n'):
                        if line.strip():
                            log_message(f"  {line}")
                    
                    # 解析延时数据
                    delay_times = []
                    for line in ping_output.split('\n'):
                        if "64 bytes from" in line and "time=" in line:
                            try:
                                time_part = line.split("time=")[1].split("ms")[0].strip()
                                delay_time = float(time_part)
                                delay_times.append(delay_time)
                            except (IndexError, ValueError):
                                continue
                    
                    if delay_times:
                        avg_delay = sum(delay_times) / len(delay_times)
                        min_delay = min(delay_times)
                        max_delay = max(delay_times)
                        success_rate = len(delay_times) / 10 * 100
                        
                        log_message("4G网络延时统计:", "green")
                        log_message(f"  成功ping包: {len(delay_times)} 个")
                        log_message(f"  平均延时: {avg_delay:.1f} ms")
                        log_message(f"  最小延时: {min_delay:.1f} ms")
                        log_message(f"  最大延时: {max_delay:.1f} ms")
                        log_message(f"  成功率: {success_rate:.1f}%")
                        
                        if success_rate >= 80:
                            log_message("4G网络测试通过 ✅", "green")
                            status_var.set(f"4G网络测试通过 - 平均延时: {avg_delay:.1f}ms")
                        else:
                            log_message("4G网络测试失败：丢包率过高 ❌", "red")
                            status_var.set("4G网络测试失败 - 丢包率过高")
                    else:
                        log_message("未能解析到延时数据", "red")
                else:
                    log_message(f"ping测试失败: {result.stderr}", "red")
                
            except subprocess.TimeoutExpired:
                log_message("4G网络测试超时", "red")
            except Exception as e:
                log_message(f"4G网络测试出错: {str(e)}", "red")
            finally:
                # 恢复WiFi
                try:
                    log_message("第三步：重新打开WiFi网络...")
                    result = subprocess.run(
                        ["adb", "shell", "ifconfig", "wlan0", "up"],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    
                    if result.returncode == 0:
                        log_message("WiFi已重新打开")
                    else:
                        log_message(f"重新打开WiFi失败: {result.stderr}", "red")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    log_message(f"重新打开WiFi时出错: {str(e)}", "red")
                
                test_state["running"] = False
        
        threading.Thread(target=real_test, daemon=True).start()
    
    def stop_test():
        """停止测试"""
        test_state["running"] = False
        log_message("测试已停止")
        status_var.set("测试已停止")
    
    def clear_log():
        """清空日志"""
        log_text.delete(1.0, tk.END)
        status_var.set("准备开始4G网络测试...")
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X)
    
    ttk.Button(button_frame, text="模拟4G测试", 
              command=run_4g_test_simulation, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="真实4G测试", 
              command=run_real_4g_test, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="停止测试", 
              command=stop_test, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="清空日志", 
              command=clear_log, width=12).pack(side=tk.RIGHT, padx=5)
    
    # 初始化日志
    log_message("4G网络测试功能验证已准备就绪")
    log_message("点击'模拟4G测试'查看测试流程")
    log_message("点击'真实4G测试'执行实际测试（需要ADB连接）")
    
    print("4G网络测试模拟界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("4G网络测试功能验证")
    print("=" * 50)
    
    print("🔧 验证内容:")
    print("  1. 4G网络测试项目配置")
    print("  2. 测试前关闭WiFi (ifconfig wlan0 down)")
    print("  3. 使用4G网络ping www.baidu.com")
    print("  4. 计算10秒延时平均值")
    print("  5. 测试后打开WiFi (ifconfig wlan0 up)")
    
    # 测试配置
    if test_4g_network_config():
        print("\n✅ 配置验证通过")
    else:
        print("\n❌ 配置验证失败")
    
    # 创建模拟测试界面
    simulate_4g_network_test()
    
    print("\n测试完成")
