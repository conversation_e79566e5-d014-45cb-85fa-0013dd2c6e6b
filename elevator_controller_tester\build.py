import os
import sys
import shutil
import subprocess

def clean_build():
    """清理旧的构建文件"""
    if os.path.exists('build'):
        shutil.rmtree('build')
    if os.path.exists('dist'):
        shutil.rmtree('dist')
    if os.path.exists('梯控测试上位机.spec'):
        os.remove('梯控测试上位机.spec')

def main():
    # 清理旧的构建文件
    clean_build()
    
    # 构建参数
    build_params = [
        'pyinstaller',
        '--noconsole',
        '--onefile',
        '--clean',
        '--name=梯控测试上位机',
        '--add-binary=C:\\Windows\\System32\\vcruntime140.dll;.',
        '--add-binary=C:\\Windows\\System32\\msvcp140.dll;.',
        '--add-binary=C:\\Windows\\System32\\vcruntime140_1.dll;.',
        '--add-binary=C:\\Windows\\System32\\msvcp140_1.dll;.',
        '--hidden-import=PyQt5',
        '--hidden-import=PyQt5.QtCore',
        '--hidden-import=PyQt5.QtGui',
        '--hidden-import=PyQt5.QtWidgets',
        'main.py'
    ]
    
    # 打印构建参数
    print('构建参数:', ' '.join(build_params))
    
    # 执行构建命令
    try:
        subprocess.run(build_params, check=True)
        print('构建完成！')
        
        # 检查生成的文件
        if os.path.exists('dist/梯控测试上位机.exe'):
            print('可执行文件已生成:', os.path.abspath('dist/梯控测试上位机.exe'))
        else:
            print('错误：可执行文件未生成！')
            
    except subprocess.CalledProcessError as e:
        print('构建失败:', e)
        sys.exit(1)

if __name__ == '__main__':
    main() 