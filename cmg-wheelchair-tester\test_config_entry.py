#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置入口功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import os
import sys

def test_config_files():
    """测试配置文件是否存在"""
    print("=== 测试配置文件 ===")
    
    files_to_check = [
        ("主配置文件", "config.json"),
        ("配置管理器", "config_manager.py"),
        ("快速配置工具", "quick_start_config.py"),
        ("配置管理指南", "CONFIG_MANAGER_GUIDE.md"),
        ("主程序", "main.py")
    ]
    
    all_exist = True
    for name, filename in files_to_check:
        if os.path.exists(filename):
            print(f"✅ {name}: {filename}")
        else:
            print(f"❌ {name}: {filename} (不存在)")
            all_exist = False
    
    return all_exist

def test_config_entry_ui():
    """测试配置入口UI"""
    print("\n=== 测试配置入口UI ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("配置入口测试")
    root.geometry("600x500")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="CMG测试配置入口", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 说明文本
    info_text = """配置管理入口位置：

1. 主程序按钮入口：
   • 在主程序界面右上角有"配置管理"按钮
   • 点击可直接启动配置管理器

2. 菜单栏入口：
   • 配置 → 配置管理器
   • 配置 → 快速配置
   • 配置 → 重新加载配置
   • 配置 → 验证配置

3. 独立启动：
   • 直接运行: python config_manager.py
   • 快速配置: python quick_start_config.py

4. 工具菜单：
   • 工具 → ADB设备检查
   • 工具 → 清空日志
   • 工具 → 测试连接

5. 帮助菜单：
   • 帮助 → 使用说明
   • 帮助 → 配置管理帮助
   • 帮助 → 关于"""
    
    info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT, font=("Microsoft YaHei UI", 10))
    info_label.pack(fill=tk.X, pady=(0, 20))
    
    # 按钮区域
    button_frame = ttk.LabelFrame(main_frame, text="测试配置工具", padding="10")
    button_frame.pack(fill=tk.X, pady=(0, 20))
    
    def launch_config_manager():
        """启动配置管理器"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "config_manager.py"])
            messagebox.showinfo("成功", "配置管理器已启动")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {str(e)}")
    
    def launch_quick_config():
        """启动快速配置"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "quick_start_config.py"])
            messagebox.showinfo("成功", "快速配置工具已启动")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {str(e)}")
    
    def launch_main_program():
        """启动主程序"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, "main.py"])
            messagebox.showinfo("成功", "主程序已启动")
        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {str(e)}")
    
    def show_config_structure():
        """显示配置结构"""
        structure_window = tk.Toplevel(root)
        structure_window.title("配置文件结构")
        structure_window.geometry("500x400")
        
        structure_text = """配置文件结构说明：

config.json - 主配置文件
├── adb_settings - ADB设置
├── wifi_settings - WiFi设置  
├── work_processes - 工序配置
│   ├── 基础连接测试
│   ├── 通信模块测试
│   ├── 人机交互测试
│   ├── 传感器测试
│   └── 完整测试
└── test_projects - 测试项目
    ├── device_connection
    ├── rom_version_test
    ├── usb_test
    ├── can_test
    ├── gps_test
    ├── 4g_test
    ├── key_test
    ├── led_test
    ├── torch_test
    ├── joystick_test
    ├── front_camera_test
    ├── light_sensor_test
    ├── back_camera_test
    ├── speaker_test
    ├── bluetooth_test
    ├── wifi_test
    ├── accel_test
    ├── gyro_test
    ├── laser_test
    ├── laser2_test
    └── odom_test

config_backups/ - 配置备份目录
├── config_backup_20241201_120000.json
├── config_backup_20241201_130000.json
└── ...

配置管理工具：
├── config_manager.py - 图形化配置管理器
├── quick_start_config.py - 快速配置工具
└── CONFIG_MANAGER_GUIDE.md - 使用指南"""
        
        text_widget = tk.Text(structure_window, wrap=tk.WORD, font=("Consolas", 9))
        scrollbar = ttk.Scrollbar(structure_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10, padx=(0, 10))
        
        text_widget.insert(tk.END, structure_text)
        text_widget.config(state=tk.DISABLED)
    
    # 按钮
    ttk.Button(button_frame, text="启动配置管理器", command=launch_config_manager).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="启动快速配置", command=launch_quick_config).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="启动主程序", command=launch_main_program).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="配置结构", command=show_config_structure).pack(side=tk.LEFT, padx=5)
    
    # 入口说明
    entry_frame = ttk.LabelFrame(main_frame, text="入口位置说明", padding="10")
    entry_frame.pack(fill=tk.X, pady=(0, 20))
    
    entry_text = """主程序中的配置入口：

1. 按钮入口（推荐）：
   • 位置：主界面右上角
   • 按钮：[配置管理] 
   • 功能：直接启动配置管理器

2. 菜单入口（完整功能）：
   • 配置菜单：配置管理器、快速配置、重新加载、验证配置
   • 工具菜单：ADB检查、清空日志、测试连接
   • 帮助菜单：使用说明、配置帮助、关于

3. 快捷键（计划中）：
   • F5：重新加载配置
   • Ctrl+M：打开配置管理器
   • F1：显示帮助"""
    
    entry_label = ttk.Label(entry_frame, text=entry_text, justify=tk.LEFT, font=("Microsoft YaHei UI", 9))
    entry_label.pack(fill=tk.X)
    
    # 底部信息
    status_frame = ttk.Frame(main_frame)
    status_frame.pack(fill=tk.X, pady=(10, 0))
    
    status_text = "配置入口测试界面 - 验证配置管理功能的各种入口方式"
    ttk.Label(status_frame, text=status_text, font=("Microsoft YaHei UI", 8), foreground="gray").pack()
    
    print("配置入口测试UI已创建")
    root.mainloop()

def show_entry_summary():
    """显示入口总结"""
    print("""
=== 配置管理入口总结 ===

📍 主要入口位置：

1. 主程序按钮入口：
   • 位置：主界面右上角 "配置管理" 按钮
   • 功能：一键启动配置管理器
   • 适用：日常配置维护

2. 主程序菜单入口：
   • 配置菜单：完整的配置管理功能
   • 工具菜单：辅助工具和检查功能
   • 帮助菜单：使用说明和帮助信息

3. 独立启动：
   • python config_manager.py - 图形化配置管理
   • python quick_start_config.py - 快速配置工具

🛠️ 配置管理功能：

• 测试项目管理：添加、编辑、删除测试项目
• 工序管理：创建和管理测试工序
• 配置预览：实时查看JSON配置
• 备份恢复：自动备份和手动恢复
• 导入导出：配置文件的导入导出
• 配置验证：检查配置完整性和正确性

📚 使用建议：

• 日常维护：使用主程序中的"配置管理"按钮
• 批量操作：使用快速配置工具
• 学习了解：查看CONFIG_MANAGER_GUIDE.md
• 问题排查：使用配置验证功能
""")

if __name__ == "__main__":
    print("CMG测试配置入口功能验证")
    print("=" * 50)
    
    # 检查配置文件
    if not test_config_files():
        print("\n❌ 部分配置文件缺失，请检查文件完整性")
        exit(1)
    
    print("\n✅ 所有配置文件检查通过")
    
    # 显示入口总结
    show_entry_summary()
    
    # 启动测试UI
    print("\n启动配置入口测试界面...")
    test_config_entry_ui()
    
    print("\n测试完成")
