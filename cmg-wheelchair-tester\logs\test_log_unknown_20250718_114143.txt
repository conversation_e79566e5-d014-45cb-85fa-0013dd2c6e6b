[2025-07-18 11:41:26] === 开始新的测试 ===
[2025-07-18 11:41:26] 开始测试
[2025-07-18 11:41:26] 工序: 右手臂功能测试
[2025-07-18 11:41:26] 序列号: None
[2025-07-18 11:41:26] 开始运行 右手臂功能测试 工序测试，共 17 个项目
[2025-07-18 11:41:26] 
开始执行: 设备连接状态检测
[2025-07-18 11:41:26] 设备连接测试失败：未检测到设备
[2025-07-18 11:41:26] 
开始执行: 3568版本测试
[2025-07-18 11:41:26] 执行3568版本测试...
[2025-07-18 11:41:26] 读取ROM版本号...
[2025-07-18 11:41:26] 执行命令: adb shell uname -a
[2025-07-18 11:41:26] ❌ uname命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:27] 
开始执行: USB关键器件检测
[2025-07-18 11:41:27] 执行USB设备检测...
[2025-07-18 11:41:27] 执行命令: adb shell lsusb
[2025-07-18 11:41:27] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:27] 
开始执行: CAN0测试
[2025-07-18 11:41:27] 执行CAN0测试流程...
[2025-07-18 11:41:27] 执行命令: adb shell ip link set can0 down
[2025-07-18 11:41:27] CAN0 down失败: error: no devices/emulators found

[2025-07-18 11:41:28] 
开始执行: GPS测试
[2025-07-18 11:41:28] 执行GPS测试...
[2025-07-18 11:41:28] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 11:41:28] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:28] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-18 11:41:28] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:28] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-18 11:41:28] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:28] GPS信号检测失败
[2025-07-18 11:41:28] 
开始执行: 4G模组测试
[2025-07-18 11:41:28] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 11:41:28] 监听线程已启动，等待串口数据...
[2025-07-18 11:41:30] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 11:41:31] AT指令发送失败: error: no devices/emulators found

[2025-07-18 11:41:31] 
开始执行: 按键测试
[2025-07-18 11:41:31] 开始按键测试...
[2025-07-18 11:41:31] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 11:41:33] 按键测试失败 - 只检测到0个按键
[2025-07-18 11:41:36] 
开始执行: 按键灯测试
[2025-07-18 11:41:36] 开始LED背光灯测试...
[2025-07-18 11:41:36] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:41:36] LED控制命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:36] 
开始执行: 手电筒测试
[2025-07-18 11:41:36] 开始手电筒LED测试...
[2025-07-18 11:41:36] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 11:41:36] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:37] 
开始执行: 摇杆使能测试
[2025-07-18 11:41:37] 执行摇杆测试...
[2025-07-18 11:41:37] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 11:41:37] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:37] 
开始执行: 前摄像头测试
[2025-07-18 11:41:37] 开始执行前摄像头测试...
[2025-07-18 11:41:37] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 11:41:37] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:38] 
开始执行: 光感测试
[2025-07-18 11:41:38] 执行光感测试...
[2025-07-18 11:41:38] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 11:41:38] 光感测试失败 - 未检测到数值变化
[2025-07-18 11:41:38] 
开始执行: 回充摄像头测试
[2025-07-18 11:41:38] 开始执行回充摄像头测试...
[2025-07-18 11:41:39] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 11:41:39] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:39] 
开始执行: 喇叭测试
[2025-07-18 11:41:39] 执行喇叭测试...
[2025-07-18 11:41:39] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 11:41:39] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:40] 
开始执行: 蓝牙测试
[2025-07-18 11:41:40] 执行蓝牙测试...
[2025-07-18 11:41:40] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 11:41:40] 执行命令: adb shell bluetoothctl show
[2025-07-18 11:41:40] ❌ 命令执行失败: error: no devices/emulators found

[2025-07-18 11:41:40] 
开始执行: 4G网络测试
[2025-07-18 11:41:40] 执行4G网络测试...
[2025-07-18 11:41:40] 第一步：关闭WiFi网络...
[2025-07-18 11:41:40] 关闭WiFi失败: error: no devices/emulators found

[2025-07-18 11:41:40] 第三步：重新打开WiFi网络...
[2025-07-18 11:41:40] 重新打开WiFi失败: error: no devices/emulators found

[2025-07-18 11:41:42] 
开始执行: WiFi测试
[2025-07-18 11:41:42] 执行WiFi测试...
[2025-07-18 11:41:42] 第一步：关闭4G网络...
[2025-07-18 11:41:43] 4G网络已关闭，等待网络切换...
[2025-07-18 11:41:43] WiFi测试出错: cannot access local variable 'time' where it is not associated with a value
[2025-07-18 11:41:43] 第三步：重新打开4G网络...
[2025-07-18 11:41:43] 4G网络已重新打开
[2025-07-18 11:41:43] 重新打开4G网络时出错: cannot access local variable 'time' where it is not associated with a value
[2025-07-18 11:41:43] 
测试完成 - 通过率: 0/17
[2025-07-18 11:41:43] ❌ 存在测试失败项！

