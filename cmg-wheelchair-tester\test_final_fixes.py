#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终UI修复效果
验证：
1. 工序选择弹窗在主程序中心位置
2. 按钮字体在按钮中心位置
3. 序列号输入后回车键直接开始测试
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}}

def center_dialog_on_parent(parent, dialog):
    """将对话框居中显示在父窗口上"""
    try:
        # 更新窗口信息
        parent.update_idletasks()
        dialog.update_idletasks()
        
        # 获取主窗口位置和尺寸
        root_x = parent.winfo_x()
        root_y = parent.winfo_y()
        root_width = parent.winfo_width()
        root_height = parent.winfo_height()
        
        # 获取对话框尺寸
        dialog_width = dialog.winfo_width()
        dialog_height = dialog.winfo_height()
        
        # 计算居中位置
        x = root_x + (root_width - dialog_width) // 2
        y = root_y + (root_height - dialog_height) // 2
        
        # 确保对话框不会超出屏幕边界
        screen_width = dialog.winfo_screenwidth()
        screen_height = dialog.winfo_screenheight()
        
        x = max(0, min(x, screen_width - dialog_width))
        y = max(0, min(y, screen_height - dialog_height))
        
        dialog.geometry(f"+{x}+{y}")
        print(f"✅ 对话框已居中: 位置({x}, {y}), 尺寸({dialog_width}x{dialog_height})")
    except Exception as e:
        print(f"居中对话框时出错: {e}")

def test_process_selection_dialog():
    """测试工序选择对话框"""
    print("=== 测试工序选择对话框 ===")
    
    config = load_config()
    work_processes = config.get("work_processes", {})
    
    if not work_processes:
        print("❌ 没有找到工序配置")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title("主程序窗口 - 测试工序选择")
    root.geometry("1200x950")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 1200) // 2
    y = (screen_height - 950) // 2
    root.geometry(f"1200x950+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    ttk.Label(main_frame, text="主程序窗口", font=("Arial", 16, "bold")).pack(pady=20)
    ttk.Label(main_frame, text="点击按钮测试工序选择对话框", 
             font=("Microsoft YaHei UI", 12)).pack(pady=10)
    
    def show_process_dialog():
        """显示工序选择对话框"""
        # 创建工序选择对话框
        dialog = tk.Toplevel(root)
        dialog.title("选择测试工序")
        dialog.geometry("400x200")
        dialog.transient(root)
        dialog.grab_set()
        dialog.resizable(False, False)
        
        # 强制更新窗口尺寸信息
        root.update()
        dialog.update()
        
        # 等待窗口完全创建后居中
        root.after(10, lambda: center_dialog_on_parent(root, dialog))
        
        # 禁止关闭窗口
        dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        result = {"selected": False}
        
        main_frame = ttk.Frame(dialog, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="请选择测试工序", 
                               font=("Microsoft YaHei UI", 14, "bold"))
        title_label.pack(pady=(0, 25))
        
        # 工序选择区域
        select_frame = ttk.Frame(main_frame)
        select_frame.pack(fill=tk.X, pady=(0, 30))
        select_frame.columnconfigure(1, weight=1)
        
        # 工序标签
        ttk.Label(select_frame, text="测试工序:", 
                 font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)
        
        # 工序下拉列表
        process_var = tk.StringVar()
        process_names = list(work_processes.keys())
        process_combo = ttk.Combobox(
            select_frame,
            textvariable=process_var,
            values=process_names,
            state="readonly",
            font=("Microsoft YaHei UI", 11),
            width=25
        )
        process_combo.grid(row=0, column=1, sticky=(tk.W, tk.E))
        
        # 默认选择第一个工序
        if process_names:
            process_combo.current(0)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def confirm_selection():
            """确认选择"""
            selected_process = process_var.get()
            if selected_process:
                result["selected"] = True
                messagebox.showinfo("选择结果", f"已选择工序: {selected_process}")
                dialog.destroy()
            else:
                messagebox.showwarning("警告", "请选择一个工序")
        
        def cancel_selection():
            """取消选择"""
            result["selected"] = False
            dialog.destroy()
        
        # 按钮区域居中布局
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)
        
        # 按钮布局：确认选择在左边，退出程序在右边
        confirm_btn = ttk.Button(button_container, text="确认选择", 
                               command=confirm_selection, 
                               style="Accent.TButton",
                               width=15)  # 增加宽度确保字体居中
        confirm_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        exit_btn = ttk.Button(button_container, text="退出程序", 
                            command=cancel_selection,
                            width=15)  # 增加宽度确保字体居中
        exit_btn.pack(side=tk.LEFT)
        
        # 绑定回车键确认
        dialog.bind('<Return>', lambda e: confirm_selection())
        
        # 设置焦点到下拉框
        process_combo.focus()
        
        print("✅ 工序选择对话框已显示")
    
    def show_serial_dialog():
        """显示序列号输入对话框"""
        # 创建序列号输入对话框
        dialog = tk.Toplevel(root)
        dialog.title("输入序列号")
        dialog.geometry("500x280")
        dialog.transient(root)
        dialog.grab_set()
        dialog.resizable(False, False)
        
        # 强制更新窗口尺寸信息
        root.update()
        dialog.update()
        
        # 等待窗口完全创建后居中
        root.after(10, lambda: center_dialog_on_parent(root, dialog))
        
        # 禁止关闭窗口
        dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        result = {"confirmed": False, "serial_number": ""}
        
        main_frame = ttk.Frame(dialog, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text="输入序列号", 
                               font=("Microsoft YaHei UI", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 当前工序信息
        process_info = "当前工序: 整机半成品功能测试"
        info_label = ttk.Label(main_frame, text=process_info, 
                              font=("Microsoft YaHei UI", 10), 
                              foreground="blue")
        info_label.pack(pady=(0, 20))
        
        # 序列号输入区域
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=(0, 25))
        input_frame.columnconfigure(1, weight=1)
        
        # 输入标签
        ttk.Label(input_frame, text="设备序列号:", 
                 font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)
        
        # 序列号输入框
        serial_var = tk.StringVar()
        serial_entry = ttk.Entry(input_frame, textvariable=serial_var, 
                               font=("Microsoft YaHei UI", 11), width=20)
        serial_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
        serial_entry.focus()
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def confirm_serial():
            """确认序列号并模拟开始测试"""
            serial_number = serial_var.get().strip()
            if serial_number:
                result["confirmed"] = True
                result["serial_number"] = serial_number
                dialog.destroy()
                
                # 模拟开始测试
                messagebox.showinfo("测试开始", 
                                  f"序列号: {serial_number}\n\n✅ 回车键功能正常！\n现在会直接开始测试")
            else:
                messagebox.showwarning("警告", "请输入序列号")
                serial_entry.focus()
        
        def cancel_serial():
            """取消输入"""
            result["confirmed"] = False
            dialog.destroy()
        
        # 绑定回车键
        serial_entry.bind("<Return>", lambda e: confirm_serial())
        
        # 按钮区域居中布局
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)
        
        # 按钮布局：开始测试在左边，退出程序在右边
        start_btn = ttk.Button(button_container, text="开始测试", 
                             command=confirm_serial, 
                             style="Accent.TButton",
                             width=15)  # 增加宽度确保字体居中
        start_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        exit_btn = ttk.Button(button_container, text="退出程序", 
                            command=cancel_serial,
                            width=15)  # 增加宽度确保字体居中
        exit_btn.pack(side=tk.LEFT)
        
        print("✅ 序列号输入对话框已显示")
    
    # 测试按钮
    test_frame = ttk.Frame(main_frame)
    test_frame.pack(pady=20)
    
    ttk.Button(test_frame, text="测试工序选择对话框", 
              command=show_process_dialog, width=20).pack(side=tk.LEFT, padx=10)
    ttk.Button(test_frame, text="测试序列号输入对话框", 
              command=show_serial_dialog, width=20).pack(side=tk.LEFT, padx=10)
    
    # 修复说明
    fix_frame = ttk.LabelFrame(main_frame, text="最终修复内容", padding="15")
    fix_frame.pack(fill=tk.BOTH, expand=True, pady=20)
    
    fix_text = """🔧 最终UI修复内容:

1. 工序选择弹窗居中显示 ✅
   • 修复前：可能不在主程序中心
   • 修复后：使用延迟居中算法，确保在主程序窗口中心

2. 按钮字体居中显示 ✅
   • 修复前：按钮字体可能不居中
   • 修复后：增加按钮宽度到15，使用居中容器布局

3. 序列号输入回车键功能 ✅
   • 修复前：回车后需要再点击主界面开始测试按钮
   • 修复后：回车键直接调用 start_all_tests() 开始测试

🎯 测试方法:
1. 点击"测试工序选择对话框"，检查是否在主窗口中心
2. 检查按钮字体是否居中显示
3. 点击"测试序列号输入对话框"，输入序列号后按回车键
4. 验证是否直接开始测试，无需再次点击"""
    
    ttk.Label(fix_frame, text=fix_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.BOTH, expand=True)
    
    print("最终UI修复测试界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("最终UI修复效果测试")
    print("=" * 50)
    
    print("🔧 修复内容:")
    print("  1. 工序选择弹窗在主程序中心位置")
    print("  2. 按钮字体在按钮中心位置")
    print("  3. 序列号输入后回车键直接开始测试")
    
    # 创建测试界面
    test_process_selection_dialog()
    
    print("\n测试完成")
