#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理快速入门示例
演示如何通过代码维护工序和测试项目
"""

import json
import os
from datetime import datetime

class QuickConfigManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载配置失败: {e}")
        
        return {"test_projects": [], "work_processes": {}}
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 创建备份
            if os.path.exists(self.config_file):
                backup_file = f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                os.rename(self.config_file, backup_file)
                print(f"已创建备份: {backup_file}")
            
            # 保存新配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            
            print("配置已保存")
            return True
        except Exception as e:
            print(f"保存配置失败: {e}")
            return False
    
    def add_test_project(self, project_id, name, description, test_type, 
                        command=None, expected_pattern=None, manual_check=False, **kwargs):
        """添加测试项目"""
        project = {
            "id": project_id,
            "name": name,
            "description": description,
            "type": test_type
        }
        
        if command:
            project["command"] = command
        if expected_pattern:
            project["expected_pattern"] = expected_pattern
        if manual_check:
            project["manual_check"] = manual_check
        
        # 添加其他参数
        project.update(kwargs)
        
        # 检查是否已存在
        test_projects = self.config.get("test_projects", [])
        for i, existing in enumerate(test_projects):
            if existing["id"] == project_id:
                test_projects[i] = project
                print(f"已更新测试项目: {name}")
                return
        
        # 添加新项目
        test_projects.append(project)
        self.config["test_projects"] = test_projects
        print(f"已添加测试项目: {name}")
    
    def remove_test_project(self, project_id):
        """删除测试项目"""
        test_projects = self.config.get("test_projects", [])
        original_count = len(test_projects)
        
        # 从测试项目中删除
        test_projects = [p for p in test_projects if p["id"] != project_id]
        self.config["test_projects"] = test_projects
        
        # 从所有工序中删除
        work_processes = self.config.get("work_processes", {})
        for process_name, process in work_processes.items():
            test_ids = process.get("test_ids", [])
            if project_id in test_ids:
                test_ids.remove(project_id)
                print(f"已从工序 '{process_name}' 中移除项目 {project_id}")
        
        if len(test_projects) < original_count:
            print(f"已删除测试项目: {project_id}")
        else:
            print(f"未找到测试项目: {project_id}")
    
    def add_work_process(self, process_name, description, test_ids):
        """添加工序"""
        work_processes = self.config.get("work_processes", {})
        
        work_processes[process_name] = {
            "description": description,
            "test_ids": test_ids
        }
        
        self.config["work_processes"] = work_processes
        print(f"已添加工序: {process_name} (包含 {len(test_ids)} 个测试项目)")
    
    def remove_work_process(self, process_name):
        """删除工序"""
        work_processes = self.config.get("work_processes", {})
        
        if process_name in work_processes:
            del work_processes[process_name]
            print(f"已删除工序: {process_name}")
        else:
            print(f"未找到工序: {process_name}")
    
    def list_test_projects(self):
        """列出所有测试项目"""
        test_projects = self.config.get("test_projects", [])
        print(f"\n=== 测试项目列表 ({len(test_projects)} 个) ===")
        
        for i, project in enumerate(test_projects, 1):
            print(f"{i:2d}. {project['name']} ({project['id']})")
            print(f"     类型: {project['type']}")
            print(f"     描述: {project['description']}")
            if project.get('command'):
                print(f"     命令: {project['command']}")
            print()
    
    def list_work_processes(self):
        """列出所有工序"""
        work_processes = self.config.get("work_processes", {})
        print(f"\n=== 工序列表 ({len(work_processes)} 个) ===")
        
        for process_name, process in work_processes.items():
            test_ids = process.get("test_ids", [])
            print(f"工序: {process_name}")
            print(f"  描述: {process['description']}")
            print(f"  包含测试项目: {len(test_ids)} 个")
            
            # 显示测试项目名称
            test_projects = self.config.get("test_projects", [])
            for test_id in test_ids:
                for project in test_projects:
                    if project["id"] == test_id:
                        print(f"    - {project['name']} ({test_id})")
                        break
            print()
    
    def validate_config(self):
        """验证配置"""
        print("\n=== 配置验证 ===")
        
        test_projects = self.config.get("test_projects", [])
        work_processes = self.config.get("work_processes", {})
        
        # 检查测试项目ID重复
        project_ids = [p["id"] for p in test_projects]
        duplicate_ids = set([x for x in project_ids if project_ids.count(x) > 1])
        
        if duplicate_ids:
            print(f"❌ 发现重复的项目ID: {duplicate_ids}")
        else:
            print("✅ 所有项目ID唯一")
        
        # 检查工序中的测试项目ID是否存在
        valid_project_ids = set(project_ids)
        
        for process_name, process in work_processes.items():
            test_ids = process.get("test_ids", [])
            invalid_ids = [tid for tid in test_ids if tid not in valid_project_ids]
            
            if invalid_ids:
                print(f"❌ 工序 '{process_name}' 包含无效的项目ID: {invalid_ids}")
            else:
                print(f"✅ 工序 '{process_name}' 所有项目ID有效")
        
        print("验证完成\n")

def demo_config_management():
    """演示配置管理"""
    print("=== CMG测试配置管理演示 ===\n")
    
    # 创建配置管理器
    config_manager = QuickConfigManager()
    
    print("1. 查看当前配置")
    config_manager.list_test_projects()
    config_manager.list_work_processes()
    
    print("\n2. 添加新的测试项目示例")
    
    # 添加一个新的测试项目
    config_manager.add_test_project(
        project_id="demo_test",
        name="演示测试",
        description="这是一个演示测试项目",
        test_type="demo_test",
        command="echo 'Hello World'",
        expected_pattern="Hello",
        manual_check=False
    )
    
    print("\n3. 添加新的工序示例")
    
    # 添加一个新的工序
    config_manager.add_work_process(
        process_name="演示工序",
        description="包含演示测试的工序",
        test_ids=["demo_test", "device_connection"]
    )
    
    print("\n4. 查看更新后的配置")
    config_manager.list_work_processes()
    
    print("\n5. 验证配置")
    config_manager.validate_config()
    
    # 询问是否保存
    save_choice = input("是否保存配置更改？(y/n): ").lower().strip()
    if save_choice == 'y':
        config_manager.save_config()
        print("配置已保存")
    else:
        print("配置未保存")
    
    # 清理演示数据
    if save_choice != 'y':
        print("\n6. 清理演示数据")
        config_manager.remove_test_project("demo_test")
        config_manager.remove_work_process("演示工序")

def show_maintenance_tips():
    """显示维护技巧"""
    print("""
=== 工序和测试项目维护技巧 ===

1. 测试项目管理：
   • 使用有意义的项目ID和名称
   • 保持项目描述的准确性
   • 定期检查和更新测试命令
   • 合理设置手动检查标志

2. 工序管理：
   • 按功能模块组织工序
   • 确保工序描述清晰明确
   • 定期审查工序包含的测试项目
   • 避免工序之间的重复和冲突

3. 配置维护：
   • 定期备份配置文件
   • 使用版本控制管理配置变更
   • 在生产环境前充分测试配置
   • 记录重要的配置更改原因

4. 推荐的工序分类：
   • 基础连接测试：设备连接、版本检查
   • 硬件测试：USB、CAN、传感器等
   • 通信测试：WiFi、蓝牙、4G、GPS
   • 人机交互：按键、LED、摄像头、喇叭
   • 完整测试：所有测试项目

5. 常用操作：
   • 使用图形界面工具进行日常维护
   • 使用代码脚本进行批量操作
   • 定期验证配置的完整性
   • 保持配置文档的更新
""")

if __name__ == "__main__":
    print("CMG测试配置管理快速入门")
    print("=" * 50)
    
    while True:
        print("\n请选择操作：")
        print("1. 演示配置管理")
        print("2. 查看维护技巧")
        print("3. 启动图形界面工具")
        print("4. 退出")
        
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            demo_config_management()
        elif choice == "2":
            show_maintenance_tips()
        elif choice == "3":
            print("启动图形界面工具...")
            os.system("python config_manager.py")
        elif choice == "4":
            print("再见！")
            break
        else:
            print("无效选择，请重试")
