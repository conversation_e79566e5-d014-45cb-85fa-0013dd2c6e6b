[2025-07-10 20:01:31] 开始测试 - SN: 1111111111
[2025-07-10 20:01:31] 
开始执行: 设备连接状态检测
[2025-07-10 20:01:31] 设备连接测试通过，检测到 1 个设备
[2025-07-10 20:01:31] 设备: ?	device
[2025-07-10 20:01:32] 
开始执行: USB关键器件检测
[2025-07-10 20:01:32] 执行USB设备检测...
[2025-07-10 20:01:32] 执行命令: adb shell lsusb
[2025-07-10 20:01:32] 设备返回数据:
[2025-07-10 20:01:32] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-10 20:01:32] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-10 20:01:32] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-10 20:01:32] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-10 20:01:32] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-10 20:01:32] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-10 20:01:32] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-10 20:01:32] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-10 20:01:32] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-10 20:01:32] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-10 20:01:32] 总共解析到 9 个设备
[2025-07-10 20:01:32] ✅ 所有预期的设备ID都已找到
[2025-07-10 20:01:32] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-10 20:01:32] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-10 20:01:32] ✅ USB设备检测通过
[2025-07-10 20:01:33] 
开始执行: CAN0测试
[2025-07-10 20:01:33] 执行CAN0测试流程...
[2025-07-10 20:01:33] 执行命令: adb shell ip link set can0 down
[2025-07-10 20:01:33] CAN0已关闭
[2025-07-10 20:01:33] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-10 20:01:33] CAN0已启动
[2025-07-10 20:01:33] CAN监听线程已启动...
[2025-07-10 20:01:34] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-10 20:01:34] CAN测试数据已发送，等待监听返回...
[2025-07-10 20:01:34] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-10 20:01:36] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-10 20:01:36] 
开始执行: GPS测试
[2025-07-10 20:01:36] 执行GPS测试...
[2025-07-10 20:01:36] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-10 20:01:47] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-10 20:01:47] 
开始执行: 4G模组测试
[2025-07-10 20:01:47] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-10 20:01:47] 监听线程已启动，等待串口数据...
[2025-07-10 20:01:49] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-10 20:01:49] AT+CCID指令已发送，等待串口返回...
[2025-07-10 20:01:49] 串口输出: AT+CCID
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: +CME ERROR: 13
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: AT+C
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: AT+C
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: AT+C
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: AT+C
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: AT+C
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: AT+C
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: AT+C
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:49] 串口输出: 
[2025-07-10 20:01:49] 串口输出: AT+C
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:01:50] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:01:50] 串口输出: AT+C
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:01:50] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:01:50] 串口输出: AT+C
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:01:50] 串口输出: +CME ERROR: 58
[2025-07-10 20:01:50] 串口输出: 
[2025-07-10 20:02:01] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-10 20:02:02] 
开始执行: 按键测试
[2025-07-10 20:02:02] 开始按键测试...
[2025-07-10 20:02:02] 执行命令: adb shell evtest /dev/input/event5
[2025-07-10 20:02:05] 原始事件: Event: time 1751300083.437937, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-10 20:02:05] 解析结果: key_code=662, value=1
[2025-07-10 20:02:05] ✓ 检测到档位减(662)按下
[2025-07-10 20:02:05] 原始事件: Event: time 1751300083.587924, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-10 20:02:05] 解析结果: key_code=662, value=0
[2025-07-10 20:02:05] ✓ 档位减(662)测试通过
[2025-07-10 20:02:05] 原始事件: Event: time 1751300084.761346, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-10 20:02:05] 解析结果: key_code=658, value=1
[2025-07-10 20:02:05] ✓ 检测到喇叭键(658)按下
[2025-07-10 20:02:05] 原始事件: Event: time 1751300084.987890, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-10 20:02:05] 解析结果: key_code=658, value=0
[2025-07-10 20:02:05] ✓ 喇叭键(658)测试通过
[2025-07-10 20:02:05] 原始事件: Event: time 1751300085.837948, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-10 20:02:05] 解析结果: key_code=662, value=1
[2025-07-10 20:02:05] ✓ 检测到档位减(662)按下
[2025-07-10 20:02:05] 原始事件: Event: time 1751300086.011211, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-10 20:02:05] 解析结果: key_code=662, value=0
[2025-07-10 20:02:06] ✓ 档位减(662)测试通过
[2025-07-10 20:02:09] 原始事件: Event: time 1751300089.661319, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-10 20:02:09] 解析结果: key_code=661, value=1
[2025-07-10 20:02:09] ✓ 检测到静音键(661)按下
[2025-07-10 20:02:09] 原始事件: Event: time 1751300089.787935, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-10 20:02:09] 解析结果: key_code=661, value=0
[2025-07-10 20:02:09] ✓ 静音键(661)测试通过
[2025-07-10 20:02:11] 原始事件: Event: time 1751300091.537937, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-10 20:02:11] 解析结果: key_code=660, value=1
[2025-07-10 20:02:11] ✓ 检测到智驾键(660)按下
[2025-07-10 20:02:11] 原始事件: Event: time 1751300091.637951, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-10 20:02:11] 解析结果: key_code=660, value=0
[2025-07-10 20:02:11] ✓ 智驾键(660)测试通过
[2025-07-10 20:02:12] 原始事件: Event: time 1751300092.791241, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-10 20:02:12] 解析结果: key_code=660, value=1
[2025-07-10 20:02:12] ✓ 检测到智驾键(660)按下
[2025-07-10 20:02:13] 原始事件: Event: time 1751300093.011253, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-10 20:02:13] 解析结果: key_code=660, value=0
[2025-07-10 20:02:13] ✓ 智驾键(660)测试通过
[2025-07-10 20:02:14] 原始事件: Event: time 1751300094.037923, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-10 20:02:14] 解析结果: key_code=657, value=1
[2025-07-10 20:02:14] ✓ 检测到SOS键(657)按下
[2025-07-10 20:02:14] 原始事件: Event: time 1751300094.187893, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-10 20:02:14] 解析结果: key_code=657, value=0
[2025-07-10 20:02:14] ✓ SOS键(657)测试通过
[2025-07-10 20:02:15] 原始事件: Event: time 1751300095.637901, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-10 20:02:15] 解析结果: key_code=663, value=1
[2025-07-10 20:02:15] ✓ 检测到语音键(663)按下
[2025-07-10 20:02:15] 原始事件: Event: time 1751300095.761255, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-10 20:02:15] 解析结果: key_code=663, value=0
[2025-07-10 20:02:15] ✓ 语音键(663)测试通过
[2025-07-10 20:02:17] 原始事件: Event: time 1751300097.187998, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-10 20:02:17] 解析结果: key_code=661, value=1
[2025-07-10 20:02:17] ✓ 检测到静音键(661)按下
[2025-07-10 20:02:17] 原始事件: Event: time 1751300097.361252, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-10 20:02:17] 解析结果: key_code=661, value=0
[2025-07-10 20:02:17] ✓ 静音键(661)测试通过
[2025-07-10 20:02:18] 原始事件: Event: time 1751300098.511352, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-10 20:02:18] 解析结果: key_code=657, value=1
[2025-07-10 20:02:18] ✓ 检测到SOS键(657)按下
[2025-07-10 20:02:18] 原始事件: Event: time 1751300098.661298, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-10 20:02:18] 解析结果: key_code=657, value=0
[2025-07-10 20:02:18] ✓ SOS键(657)测试通过
[2025-07-10 20:02:20] 原始事件: Event: time 1751300100.661660, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-10 20:02:20] 解析结果: key_code=658, value=1
[2025-07-10 20:02:20] ✓ 检测到喇叭键(658)按下
[2025-07-10 20:02:20] 原始事件: Event: time 1751300100.838170, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-10 20:02:20] 解析结果: key_code=658, value=0
[2025-07-10 20:02:20] ✓ 喇叭键(658)测试通过
[2025-07-10 20:02:21] 原始事件: Event: time 1751300101.611301, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-10 20:02:21] 解析结果: key_code=663, value=1
[2025-07-10 20:02:21] ✓ 检测到语音键(663)按下
[2025-07-10 20:02:21] 原始事件: Event: time 1751300101.787937, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-10 20:02:21] 解析结果: key_code=663, value=0
[2025-07-10 20:02:21] ✓ 语音键(663)测试通过
[2025-07-10 20:02:23] 原始事件: Event: time 1751300103.237996, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-10 20:02:23] 解析结果: key_code=659, value=1
[2025-07-10 20:02:23] ✓ 检测到档位加(659)按下
[2025-07-10 20:02:23] 原始事件: Event: time 1751300103.414581, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-10 20:02:23] 解析结果: key_code=659, value=0
[2025-07-10 20:02:23] ✓ 档位加(659)测试通过
[2025-07-10 20:02:26] 原始事件: Event: time 1751300106.587907, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-10 20:02:26] 解析结果: key_code=659, value=1
[2025-07-10 20:02:26] ✓ 检测到档位加(659)按下
[2025-07-10 20:02:26] 原始事件: Event: time 1751300106.737956, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-10 20:02:26] 解析结果: key_code=659, value=0
[2025-07-10 20:02:26] ✓ 档位加(659)测试通过
[2025-07-10 20:02:29] 原始事件: Event: time 1751300109.111271, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-10 20:02:29] 解析结果: key_code=660, value=1
[2025-07-10 20:02:29] ✓ 检测到智驾键(660)按下
[2025-07-10 20:02:29] 原始事件: Event: time 1751300109.361348, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-10 20:02:29] 解析结果: key_code=660, value=0
[2025-07-10 20:02:29] ✓ 智驾键(660)测试通过
[2025-07-10 20:02:31] 原始事件: Event: time 1751300111.261325, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-10 20:02:31] 解析结果: key_code=656, value=1
[2025-07-10 20:02:31] ✓ 检测到锁定键(656)按下
[2025-07-10 20:02:31] 原始事件: Event: time 1751300111.561325, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-10 20:02:31] 解析结果: key_code=656, value=0
[2025-07-10 20:02:31] ✓ 锁定键(656)测试通过
[2025-07-10 20:02:31] 按键测试完成 - 检测到8个按键
[2025-07-10 20:02:34] 
开始执行: 按键灯测试
[2025-07-10 20:02:34] 开始LED背光灯测试...
[2025-07-10 20:02:34] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-10 20:02:34] LED控制命令执行成功
[2025-07-10 20:02:38] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-10 20:02:38] LED灯已关闭
[2025-07-10 20:02:38] LED测试通过 - 背光灯正常点亮
[2025-07-10 20:02:38] 
开始执行: 手电筒测试
[2025-07-10 20:02:38] 开始手电筒LED测试...
[2025-07-10 20:02:38] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-10 20:02:38] 手电筒控制命令执行成功
[2025-07-10 20:02:39] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-10 20:02:39] 手电筒已关闭
[2025-07-10 20:02:39] 手电筒测试通过 - 手电筒正常点亮
[2025-07-10 20:02:40] 
开始执行: 摇杆使能测试
[2025-07-10 20:02:40] 执行摇杆测试...
[2025-07-10 20:02:40] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-10 20:02:40] 命令执行成功
[2025-07-10 20:02:40] 返回数据: 255
[2025-07-10 20:02:40] 摇杆测试通过，值: 255
[2025-07-10 20:02:40] 
开始执行: 前摄像头测试
[2025-07-10 20:02:40] 开始执行前摄像头测试...
[2025-07-10 20:02:40] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-10 20:02:40] 命令执行成功，返回: 无输出
[2025-07-10 20:02:40] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-10 20:02:41] 命令执行失败: ERROR: from element /GstPipeline:pipeline0/GstV4l2Src:v4l2src0: Internal data stream error.
Additional debug info:
../libs/gst/base/gstbasesrc.c(3132): gst_base_src_loop (): /GstPipeline:pipeline0/GstV4l2Src:v4l2src0:
streaming stopped, reason not-negotiated (-4)

[2025-07-10 20:02:41] 
开始执行: 光感测试
[2025-07-10 20:02:41] 执行光感测试...
[2025-07-10 20:02:41] 执行命令: adb shell evtest /dev/input/event1
[2025-07-10 20:02:45] 光感测试完成 - 检测到数值变化
[2025-07-10 20:02:45] 数值从 0 变化到 1
[2025-07-10 20:02:45] 
开始执行: 回充摄像头测试
[2025-07-10 20:02:45] 开始执行回充摄像头测试...
[2025-07-10 20:02:45] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-10 20:02:45] 命令执行成功，返回: 无输出
[2025-07-10 20:02:45] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-10 20:02:46] 命令执行成功，返回: 无输出
[2025-07-10 20:02:46] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-10 20:02:46] 拉取命令执行成功
[2025-07-10 20:02:46] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 1.0 MB/s (13304 bytes in 0.012s)
[2025-07-10 20:02:46] 图片已保存: output.jpg
[2025-07-10 20:02:48] 用户确认结果: 通过
[2025-07-10 20:02:48] 
开始执行: 喇叭测试
[2025-07-10 20:02:48] 执行喇叭测试...
[2025-07-10 20:02:48] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-10 20:03:02] 命令执行成功
[2025-07-10 20:03:02] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-10 20:03:02] 音频播放完成
[2025-07-10 20:03:03] 
开始执行: 蓝牙测试
[2025-07-10 20:03:03] 执行蓝牙测试...
[2025-07-10 20:03:03] 启动蓝牙服务...
[2025-07-10 20:03:03] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-10 20:03:03] 开启蓝牙...
[2025-07-10 20:03:03] 执行命令: adb shell bluetoothctl devices
[2025-07-10 20:03:03] 命令执行成功
[2025-07-10 20:03:03] 无输出数据
[2025-07-10 20:03:03] 
开始执行: WiFi测试
[2025-07-10 20:03:03] 执行WiFi测试...
[2025-07-10 20:03:03] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-10 20:03:03] 命令执行成功，无输出
[2025-07-10 20:03:03] 执行命令 2: adb shell rm -f /var/run/wpa_supplicant/wlan0
[2025-07-10 20:03:03] 命令执行成功，无输出
[2025-07-10 20:03:03] 执行命令 3: adb shell ip link set wlan0 down
[2025-07-10 20:03:03] 命令执行成功，无输出
[2025-07-10 20:03:03] 执行命令 4: adb shell wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && wpa_cli -i wlan0 add_network && wpa_cli -i wlan0 set_network 0 ssid '"Orion_SZ_5G"' && wpa_cli -i wlan0 set_network 0 psk '"Orion@2025"' && wpa_cli -i wlan0 enable_network 0 && udhcpc -i wlan0 && iw wlan0 link && ip addr show wlan0
[2025-07-10 20:03:07] 命令执行成功，返回数据:
[2025-07-10 20:03:07]   Successfully initialized wpa_supplicant
[2025-07-10 20:03:07]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-10 20:03:07]   1
[2025-07-10 20:03:07]   OK
[2025-07-10 20:03:07]   OK
[2025-07-10 20:03:07]   OK
[2025-07-10 20:03:07]   deleting routers
[2025-07-10 20:03:07]   adding dns ************
[2025-07-10 20:03:07]   adding dns ***********
[2025-07-10 20:03:07]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-10 20:03:07]   SSID: Orion_SZ_5G
[2025-07-10 20:03:07]   freq: 5300
[2025-07-10 20:03:07]   RX: 2664 bytes (6 packets)
[2025-07-10 20:03:07]   TX: 1796 bytes (9 packets)
[2025-07-10 20:03:07]   signal: -48 dBm
[2025-07-10 20:03:07]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-10 20:03:07]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-10 20:03:07]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-10 20:03:07]   link/ether 24:21:5e:c0:3d:52 brd ff:ff:ff:ff:ff:ff
[2025-07-10 20:03:07]   inet 192.168.20.176/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-10 20:03:07]   valid_lft forever preferred_lft forever
[2025-07-10 20:03:07]   inet6 fe80::f53:7702:d5c3:f4d5/64 scope link tentative
[2025-07-10 20:03:07]   valid_lft forever preferred_lft forever
[2025-07-10 20:03:07] 执行命令 5: adb shell ping -c 3 www.baidu.com
[2025-07-10 20:03:09] 命令执行成功，返回数据:
[2025-07-10 20:03:09]   PING www.a.shifen.com (157.148.69.151) 56(84) bytes of data.
[2025-07-10 20:03:09]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=1 ttl=54 time=19.9 ms
[2025-07-10 20:03:09]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=2 ttl=54 time=10.2 ms
[2025-07-10 20:03:09]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=3 ttl=54 time=11.1 ms
[2025-07-10 20:03:09]   --- www.a.shifen.com ping statistics ---
[2025-07-10 20:03:09]   3 packets transmitted, 3 received, 0% packet loss, time 2003ms
[2025-07-10 20:03:09]   rtt min/avg/max/mdev = 10.193/13.730/19.900/4.378 ms
[2025-07-10 20:03:09] ✅ ping测试成功，网络连通性正常
[2025-07-10 20:03:09] WiFi连接成功
[2025-07-10 20:03:09] 
测试完成 - 通过率: 11/15
[2025-07-10 20:03:09] ❌ 存在测试失败项！
[2025-07-10 20:03:09] 测试记录已保存: records/1111111111_20250710_200309.json

