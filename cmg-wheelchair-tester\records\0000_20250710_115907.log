[2025-07-10 11:57:46] 开始测试 - SN: 0000
[2025-07-10 11:57:46] 
开始执行: 设备连接状态检测
[2025-07-10 11:57:46] 设备连接测试通过，检测到 1 个设备
[2025-07-10 11:57:46] 设备: ?	device
[2025-07-10 11:57:46] 
开始执行: USB关键器件检测
[2025-07-10 11:57:46] 执行USB设备检测...
[2025-07-10 11:57:46] 执行命令: adb shell lsusb
[2025-07-10 11:57:46] 设备返回数据:
[2025-07-10 11:57:46] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-10 11:57:46] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-10 11:57:46] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-10 11:57:46] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-10 11:57:46] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-10 11:57:46] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-10 11:57:46] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-10 11:57:46] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-10 11:57:46] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-10 11:57:46] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-10 11:57:46] 总共解析到 9 个设备
[2025-07-10 11:57:46] ✅ 所有预期的设备ID都已找到
[2025-07-10 11:57:47] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-10 11:57:47] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-10 11:57:47] ✅ USB设备检测通过
[2025-07-10 11:57:47] 
开始执行: CAN0测试
[2025-07-10 11:57:47] 执行CAN0测试流程...
[2025-07-10 11:57:47] 执行命令: adb shell ip link set can0 down
[2025-07-10 11:57:47] CAN0已关闭
[2025-07-10 11:57:47] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-10 11:57:47] CAN0已启动
[2025-07-10 11:57:47] CAN监听线程已启动...
[2025-07-10 11:57:48] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-10 11:57:48] CAN测试数据已发送，等待监听返回...
[2025-07-10 11:57:48] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-10 11:57:50] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-10 11:57:51] 
开始执行: GPS测试
[2025-07-10 11:57:51] 执行GPS测试...
[2025-07-10 11:57:51] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-10 11:58:01] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-10 11:58:01] 
开始执行: 4G模组测试
[2025-07-10 11:58:01] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-10 11:58:01] 监听线程已启动，等待串口数据...
[2025-07-10 11:58:03] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-10 11:58:03] AT+CCID指令已发送，等待串口返回...
[2025-07-10 11:58:03] 串口输出: AT+CCID
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 13
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: AT+C
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:03] 串口输出: +CME ERROR: 58
[2025-07-10 11:58:03] 串口输出: 
[2025-07-10 11:58:15] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-10 11:58:16] 
开始执行: 按键测试
[2025-07-10 11:58:16] 开始按键测试...
[2025-07-10 11:58:16] 执行命令: adb shell evtest /dev/input/event5
[2025-07-10 11:58:18] 原始事件: Event: time 1751299297.174398, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-10 11:58:18] 解析结果: key_code=662, value=1
[2025-07-10 11:58:18] ✓ 检测到档位减(662)按下
[2025-07-10 11:58:18] 原始事件: Event: time 1751299297.397713, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-10 11:58:18] 解析结果: key_code=662, value=0
[2025-07-10 11:58:18] ✓ 档位减(662)测试通过
[2025-07-10 11:58:19] 原始事件: Event: time 1751299297.899261, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-10 11:58:19] 解析结果: key_code=661, value=1
[2025-07-10 11:58:19] ✓ 检测到静音键(661)按下
[2025-07-10 11:58:19] 原始事件: Event: time 1751299298.097721, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-10 11:58:19] 解析结果: key_code=661, value=0
[2025-07-10 11:58:19] ✓ 静音键(661)测试通过
[2025-07-10 11:58:20] 原始事件: Event: time 1751299298.597696, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-10 11:58:20] 解析结果: key_code=659, value=1
[2025-07-10 11:58:20] ✓ 检测到档位加(659)按下
[2025-07-10 11:58:20] 原始事件: Event: time 1751299298.824381, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-10 11:58:20] 解析结果: key_code=659, value=0
[2025-07-10 11:58:20] ✓ 档位加(659)测试通过
[2025-07-10 11:58:20] 原始事件: Event: time 1751299299.074908, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-10 11:58:20] 解析结果: key_code=660, value=1
[2025-07-10 11:58:20] ✓ 检测到智驾键(660)按下
[2025-07-10 11:58:20] 原始事件: Event: time 1751299299.274400, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-10 11:58:20] 解析结果: key_code=660, value=0
[2025-07-10 11:58:20] ✓ 智驾键(660)测试通过
[2025-07-10 11:58:21] 原始事件: Event: time 1751299299.647681, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-10 11:58:21] 解析结果: key_code=663, value=1
[2025-07-10 11:58:21] ✓ 检测到语音键(663)按下
[2025-07-10 11:58:21] 原始事件: Event: time 1751299299.847707, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-10 11:58:21] 解析结果: key_code=663, value=0
[2025-07-10 11:58:21] ✓ 语音键(663)测试通过
[2025-07-10 11:58:21] 原始事件: Event: time 1751299300.124386, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-10 11:58:21] 解析结果: key_code=658, value=1
[2025-07-10 11:58:21] ✓ 检测到喇叭键(658)按下
[2025-07-10 11:58:21] 原始事件: Event: time 1751299300.248994, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-10 11:58:21] 解析结果: key_code=658, value=0
[2025-07-10 11:58:21] ✓ 喇叭键(658)测试通过
[2025-07-10 11:58:22] 原始事件: Event: time 1751299301.324350, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-10 11:58:22] 解析结果: key_code=657, value=1
[2025-07-10 11:58:22] ✓ 检测到SOS键(657)按下
[2025-07-10 11:58:22] 原始事件: Event: time 1751299301.547700, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-10 11:58:22] 解析结果: key_code=657, value=0
[2025-07-10 11:58:22] ✓ SOS键(657)测试通过
[2025-07-10 11:58:23] 原始事件: Event: time 1751299301.874363, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-10 11:58:23] 解析结果: key_code=656, value=1
[2025-07-10 11:58:23] ✓ 检测到锁定键(656)按下
[2025-07-10 11:58:23] 原始事件: Event: time 1751299302.147676, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-10 11:58:23] 解析结果: key_code=656, value=0
[2025-07-10 11:58:23] ✓ 锁定键(656)测试通过
[2025-07-10 11:58:23] 按键测试完成 - 检测到8个按键
[2025-07-10 11:58:26] 
开始执行: 按键灯测试
[2025-07-10 11:58:26] 开始LED背光灯测试...
[2025-07-10 11:58:26] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-10 11:58:26] LED控制命令执行成功
[2025-07-10 11:58:31] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-10 11:58:31] LED灯已关闭
[2025-07-10 11:58:31] LED测试通过 - 背光灯正常点亮
[2025-07-10 11:58:32] 
开始执行: 手电筒测试
[2025-07-10 11:58:32] 开始手电筒LED测试...
[2025-07-10 11:58:32] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-10 11:58:32] 手电筒控制命令执行成功
[2025-07-10 11:58:34] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-10 11:58:34] 手电筒已关闭
[2025-07-10 11:58:34] 手电筒测试通过 - 手电筒正常点亮
[2025-07-10 11:58:34] 
开始执行: 摇杆使能测试
[2025-07-10 11:58:34] 执行摇杆测试...
[2025-07-10 11:58:34] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-10 11:58:34] 命令执行成功
[2025-07-10 11:58:34] 返回数据: 255
[2025-07-10 11:58:34] 摇杆测试通过，值: 255
[2025-07-10 11:58:34] 
开始执行: 前摄像头测试
[2025-07-10 11:58:34] 开始执行前摄像头测试...
[2025-07-10 11:58:34] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-10 11:58:34] 命令执行成功，返回: 无输出
[2025-07-10 11:58:34] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-10 11:58:36] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.314655615
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-10 11:58:36] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-10 11:58:36] 拉取命令执行成功
[2025-07-10 11:58:36] 返回数据: [ 34%] /data/camera/cam0_3840x2160.jpg
[ 68%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 23.4 MB/s (190373 bytes in 0.008s)
[2025-07-10 11:58:36] 图片已保存: cam0_3840x2160.jpg
[2025-07-10 11:58:38] 用户确认结果: 通过
[2025-07-10 11:58:38] 
开始执行: 光感测试
[2025-07-10 11:58:38] 执行光感测试...
[2025-07-10 11:58:38] 执行命令: adb shell evtest /dev/input/event1
[2025-07-10 11:58:41] 光感测试完成 - 检测到数值变化
[2025-07-10 11:58:41] 数值从 0 变化到 1
[2025-07-10 11:58:41] 
开始执行: 回充摄像头测试
[2025-07-10 11:58:41] 开始执行回充摄像头测试...
[2025-07-10 11:58:41] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-10 11:58:41] 命令执行成功，返回: 无输出
[2025-07-10 11:58:41] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-10 11:58:42] 命令执行成功，返回: 无输出
[2025-07-10 11:58:42] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-10 11:58:42] 拉取命令执行成功
[2025-07-10 11:58:42] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 9.9 MB/s (30768 bytes in 0.003s)
[2025-07-10 11:58:42] 图片已保存: output.jpg
[2025-07-10 11:58:46] 用户确认结果: 通过
[2025-07-10 11:58:46] 
开始执行: 喇叭测试
[2025-07-10 11:58:46] 执行喇叭测试...
[2025-07-10 11:58:46] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-10 11:59:00] 命令执行成功
[2025-07-10 11:59:00] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-10 11:59:00] 音频播放完成
[2025-07-10 11:59:00] 
开始执行: 蓝牙测试
[2025-07-10 11:59:00] 执行蓝牙测试...
[2025-07-10 11:59:00] 启动蓝牙服务...
[2025-07-10 11:59:00] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-10 11:59:00] 开启蓝牙...
[2025-07-10 11:59:00] 执行命令: adb shell bluetoothctl devices
[2025-07-10 11:59:01] 命令执行成功
[2025-07-10 11:59:01] 无输出数据
[2025-07-10 11:59:01] 
开始执行: WiFi测试
[2025-07-10 11:59:01] 执行WiFi测试...
[2025-07-10 11:59:01] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-10 11:59:01] 命令执行成功，无输出
[2025-07-10 11:59:01] 执行命令 2: adb shell rm -f /var/run/wpa_supplicant/wlan0
[2025-07-10 11:59:01] 命令执行成功，无输出
[2025-07-10 11:59:01] 执行命令 3: adb shell ip link set wlan0 down
[2025-07-10 11:59:01] 命令执行成功，无输出
[2025-07-10 11:59:01] 执行命令 4: adb shell wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && wpa_cli -i wlan0 add_network && wpa_cli -i wlan0 set_network 0 ssid '"Orion_SZ_5G"' && wpa_cli -i wlan0 set_network 0 psk '"Orion@2025"' && wpa_cli -i wlan0 enable_network 0 && udhcpc -i wlan0 && iw wlan0 link && ip addr show wlan0
[2025-07-10 11:59:04] 命令执行成功，返回数据:
[2025-07-10 11:59:04]   Successfully initialized wpa_supplicant
[2025-07-10 11:59:04]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-10 11:59:04]   1
[2025-07-10 11:59:04]   OK
[2025-07-10 11:59:04]   OK
[2025-07-10 11:59:04]   OK
[2025-07-10 11:59:04]   deleting routers
[2025-07-10 11:59:04]   adding dns ************
[2025-07-10 11:59:04]   adding dns ***********
[2025-07-10 11:59:04]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-10 11:59:04]   SSID: Orion_SZ_5G
[2025-07-10 11:59:04]   freq: 5300
[2025-07-10 11:59:04]   RX: 3357 bytes (11 packets)
[2025-07-10 11:59:04]   TX: 2086 bytes (14 packets)
[2025-07-10 11:59:04]   signal: -46 dBm
[2025-07-10 11:59:04]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-10 11:59:04]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-10 11:59:04]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-10 11:59:04]   link/ether 24:21:5e:c0:3b:8c brd ff:ff:ff:ff:ff:ff
[2025-07-10 11:59:04]   inet 192.168.20.178/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-10 11:59:04]   valid_lft forever preferred_lft forever
[2025-07-10 11:59:04]   inet6 fe80::9e6a:9294:5735:16b8/64 scope link tentative
[2025-07-10 11:59:04]   valid_lft forever preferred_lft forever
[2025-07-10 11:59:04] 执行命令 5: adb shell ping -c 3 www.baidu.com
[2025-07-10 11:59:06] 命令执行成功，返回数据:
[2025-07-10 11:59:06]   PING www.a.shifen.com (157.148.69.151) 56(84) bytes of data.
[2025-07-10 11:59:06]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=1 ttl=54 time=10.8 ms
[2025-07-10 11:59:07]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=2 ttl=54 time=11.4 ms
[2025-07-10 11:59:07]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=3 ttl=54 time=12.0 ms
[2025-07-10 11:59:07]   --- www.a.shifen.com ping statistics ---
[2025-07-10 11:59:07]   3 packets transmitted, 3 received, 0% packet loss, time 2002ms
[2025-07-10 11:59:07]   rtt min/avg/max/mdev = 10.782/11.403/12.043/0.514 ms
[2025-07-10 11:59:07] ✅ ping测试成功，网络连通性正常
[2025-07-10 11:59:07] WiFi连接成功
[2025-07-10 11:59:07] 
测试完成 - 通过率: 12/15
[2025-07-10 11:59:07] ❌ 存在测试失败项！
[2025-07-10 11:59:07] 测试记录已保存: records/0000_20250710_115907.json

