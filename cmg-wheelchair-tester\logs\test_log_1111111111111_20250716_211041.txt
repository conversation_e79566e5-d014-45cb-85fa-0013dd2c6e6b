[2025-07-16 21:09:18] 开始测试 - SN: 1111111111111
[2025-07-16 21:09:18] 
开始执行: 设备连接状态检测
[2025-07-16 21:09:18] 设备连接测试通过，检测到 1 个设备
[2025-07-16 21:09:18] 设备: ?	device
[2025-07-16 21:09:19] 
开始执行: USB关键器件检测
[2025-07-16 21:09:19] 执行USB设备检测...
[2025-07-16 21:09:19] 执行命令: adb shell lsusb
[2025-07-16 21:09:19] 设备返回数据:
[2025-07-16 21:09:19] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-16 21:09:19] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-16 21:09:19] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-16 21:09:19] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-16 21:09:19] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-16 21:09:19] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-16 21:09:19] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-16 21:09:19] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-16 21:09:19] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-16 21:09:19] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-16 21:09:19] 总共解析到 9 个设备
[2025-07-16 21:09:19] ✅ 所有预期的设备ID都已找到
[2025-07-16 21:09:20] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-16 21:09:20] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-16 21:09:20] ✅ USB设备检测通过
[2025-07-16 21:09:20] 
开始执行: CAN0测试
[2025-07-16 21:09:20] 执行CAN0测试流程...
[2025-07-16 21:09:20] 执行命令: adb shell ip link set can0 down
[2025-07-16 21:09:20] CAN0已关闭
[2025-07-16 21:09:20] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-16 21:09:20] CAN0已启动
[2025-07-16 21:09:20] CAN监听线程已启动...
[2025-07-16 21:09:21] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-16 21:09:21] CAN测试数据已发送，等待监听返回...
[2025-07-16 21:09:21] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-16 21:09:23] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-16 21:09:24] 
开始执行: GPS测试
[2025-07-16 21:09:24] 执行GPS测试...
[2025-07-16 21:09:24] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-16 21:09:34] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-16 21:09:34] 
开始执行: 4G模组测试
[2025-07-16 21:09:35] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-16 21:09:35] 监听线程已启动，等待串口数据...
[2025-07-16 21:09:37] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-16 21:09:37] AT+CCID指令已发送，等待串口返回...
[2025-07-16 21:09:37] 串口输出: AT+CCID
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: +CME ERROR: 13
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: AT+C
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: +CME ERROR: 58
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: AT+C
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: +CME ERROR: 58
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: AT+C
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: +CME ERROR: 58
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: AT+C
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: +CME ERROR: 58
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: AT+C
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: +CME ERROR: 58
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: AT+C
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:37] 串口输出: +CME ERROR: 58
[2025-07-16 21:09:37] 串口输出: 
[2025-07-16 21:09:49] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-16 21:09:49] 
开始执行: 按键测试
[2025-07-16 21:09:49] 开始按键测试...
[2025-07-16 21:09:50] 执行命令: adb shell evtest /dev/input/event5
[2025-07-16 21:09:52] 原始事件: Event: time 1751299274.816643, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-16 21:09:52] 解析结果: key_code=662, value=1
[2025-07-16 21:09:52] ✓ 检测到档位减(662)按下
[2025-07-16 21:09:52] 原始事件: Event: time 1751299274.993451, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-16 21:09:52] 解析结果: key_code=662, value=0
[2025-07-16 21:09:52] ✓ 档位减(662)测试通过
[2025-07-16 21:09:52] 原始事件: Event: time 1751299275.719839, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-16 21:09:52] 解析结果: key_code=661, value=1
[2025-07-16 21:09:52] ✓ 检测到静音键(661)按下
[2025-07-16 21:09:52] 原始事件: Event: time 1751299275.943165, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-16 21:09:52] 解析结果: key_code=661, value=0
[2025-07-16 21:09:52] ✓ 静音键(661)测试通过
[2025-07-16 21:09:53] 原始事件: Event: time 1751299276.816590, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-16 21:09:53] 解析结果: key_code=659, value=1
[2025-07-16 21:09:53] ✓ 检测到档位加(659)按下
[2025-07-16 21:09:53] 原始事件: Event: time 1751299277.016599, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-16 21:09:53] 解析结果: key_code=659, value=0
[2025-07-16 21:09:53] ✓ 档位加(659)测试通过
[2025-07-16 21:09:54] 原始事件: Event: time 1751299277.593199, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-16 21:09:54] 解析结果: key_code=662, value=1
[2025-07-16 21:09:54] ✓ 检测到档位减(662)按下
[2025-07-16 21:09:54] 原始事件: Event: time 1751299277.746571, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-16 21:09:54] 解析结果: key_code=662, value=0
[2025-07-16 21:09:54] ✓ 档位减(662)测试通过
[2025-07-16 21:09:56] 原始事件: Event: time 1751299279.266558, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-16 21:09:56] 解析结果: key_code=663, value=1
[2025-07-16 21:09:56] ✓ 检测到语音键(663)按下
[2025-07-16 21:09:56] 原始事件: Event: time 1751299279.443186, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-16 21:09:56] 解析结果: key_code=663, value=0
[2025-07-16 21:09:56] ✓ 语音键(663)测试通过
[2025-07-16 21:09:57] 原始事件: Event: time 1751299280.243202, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-16 21:09:57] 解析结果: key_code=656, value=1
[2025-07-16 21:09:57] ✓ 检测到锁定键(656)按下
[2025-07-16 21:09:57] 原始事件: Event: time 1751299280.393226, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-16 21:09:57] 解析结果: key_code=656, value=0
[2025-07-16 21:09:57] ✓ 锁定键(656)测试通过
[2025-07-16 21:09:57] 原始事件: Event: time 1751299280.916719, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-16 21:09:57] 解析结果: key_code=660, value=1
[2025-07-16 21:09:57] ✓ 检测到智驾键(660)按下
[2025-07-16 21:09:58] 原始事件: Event: time 1751299281.093205, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-16 21:09:58] 解析结果: key_code=660, value=0
[2025-07-16 21:09:58] ✓ 智驾键(660)测试通过
[2025-07-16 21:09:58] 原始事件: Event: time 1751299281.466547, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-16 21:09:58] 解析结果: key_code=657, value=1
[2025-07-16 21:09:58] ✓ 检测到SOS键(657)按下
[2025-07-16 21:09:58] 原始事件: Event: time 1751299281.693192, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-16 21:09:58] 解析结果: key_code=657, value=0
[2025-07-16 21:09:58] ✓ SOS键(657)测试通过
[2025-07-16 21:09:59] 原始事件: Event: time 1751299282.493163, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-16 21:09:59] 解析结果: key_code=663, value=1
[2025-07-16 21:09:59] ✓ 检测到语音键(663)按下
[2025-07-16 21:09:59] 原始事件: Event: time 1751299282.643161, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-16 21:09:59] 解析结果: key_code=663, value=0
[2025-07-16 21:09:59] ✓ 语音键(663)测试通过
[2025-07-16 21:10:02] 原始事件: Event: time 1751299286.016501, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-16 21:10:02] 解析结果: key_code=658, value=1
[2025-07-16 21:10:02] ✓ 检测到喇叭键(658)按下
[2025-07-16 21:10:03] 原始事件: Event: time 1751299286.266560, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-16 21:10:03] 解析结果: key_code=658, value=0
[2025-07-16 21:10:03] ✓ 喇叭键(658)测试通过
[2025-07-16 21:10:03] 按键测试完成 - 检测到8个按键
[2025-07-16 21:10:05] 
开始执行: 按键灯测试
[2025-07-16 21:10:06] 开始LED背光灯测试...
[2025-07-16 21:10:06] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-16 21:10:06] LED控制命令执行成功
[2025-07-16 21:10:06] 🔧 显示确认对话框: LED测试确认
[2025-07-16 21:10:06] 🔧 对话框窗口已创建
[2025-07-16 21:10:06] 🔧 '是'按钮已创建
[2025-07-16 21:10:06] 🔧 '否'按钮已创建
[2025-07-16 21:10:06] 🔧 对话框显示完成，等待用户响应...
[2025-07-16 21:10:10] 👤 用户选择: 是 (测试通过)
[2025-07-16 21:10:10] 🔧 对话框关闭，用户响应: yes
[2025-07-16 21:10:10] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-16 21:10:10] LED灯已关闭
[2025-07-16 21:10:10] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-16 21:10:11] 
开始执行: 手电筒测试
[2025-07-16 21:10:11] 开始手电筒LED测试...
[2025-07-16 21:10:11] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-16 21:10:11] 手电筒控制命令执行成功
[2025-07-16 21:10:11] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-16 21:10:11] 🔧 对话框窗口已创建
[2025-07-16 21:10:11] 🔧 '是'按钮已创建
[2025-07-16 21:10:11] 🔧 '否'按钮已创建
[2025-07-16 21:10:11] 🔧 对话框显示完成，等待用户响应...
[2025-07-16 21:10:13] 👤 用户选择: 是 (测试通过)
[2025-07-16 21:10:13] 🔧 对话框关闭，用户响应: yes
[2025-07-16 21:10:13] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-16 21:10:13] 手电筒已关闭
[2025-07-16 21:10:13] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-16 21:10:14] 
开始执行: 摇杆使能测试
[2025-07-16 21:10:14] 执行摇杆测试...
[2025-07-16 21:10:14] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-16 21:10:14] 命令执行成功
[2025-07-16 21:10:14] 返回数据: 255
[2025-07-16 21:10:14] 摇杆测试通过，值: 255
[2025-07-16 21:10:14] 
开始执行: 前摄像头测试
[2025-07-16 21:10:14] 开始执行前摄像头测试...
[2025-07-16 21:10:14] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-16 21:10:14] 命令执行成功，返回: 无输出
[2025-07-16 21:10:14] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-16 21:10:16] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.323715741
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-16 21:10:16] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-16 21:10:16] 拉取命令执行成功
[2025-07-16 21:10:16] 返回数据: [ 34%] /data/camera/cam0_3840x2160.jpg
[ 68%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 16.9 MB/s (191297 bytes in 0.011s)
[2025-07-16 21:10:16] 图片已保存: cam0_3840x2160.jpg
[2025-07-16 21:10:18] 用户确认结果: 通过
[2025-07-16 21:10:19] 
开始执行: 光感测试
[2025-07-16 21:10:19] 执行光感测试...
[2025-07-16 21:10:19] 执行命令: adb shell evtest /dev/input/event1
[2025-07-16 21:10:22] 光感测试完成 - 检测到数值变化
[2025-07-16 21:10:22] 数值从 0 变化到 1
[2025-07-16 21:10:22] 
开始执行: 回充摄像头测试
[2025-07-16 21:10:22] 开始执行回充摄像头测试...
[2025-07-16 21:10:22] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-16 21:10:22] 命令执行成功，返回: 无输出
[2025-07-16 21:10:22] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-16 21:10:23] 命令执行成功，返回: 无输出
[2025-07-16 21:10:23] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-16 21:10:23] 拉取命令执行成功
[2025-07-16 21:10:23] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 4.8 MB/s (54432 bytes in 0.011s)
[2025-07-16 21:10:23] 图片已保存: output.jpg
[2025-07-16 21:10:26] 用户确认结果: 通过
[2025-07-16 21:10:26] 
开始执行: 喇叭测试
[2025-07-16 21:10:26] 执行喇叭测试...
[2025-07-16 21:10:26] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-16 21:10:40] 命令执行成功
[2025-07-16 21:10:40] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-16 21:10:40] 音频播放完成
[2025-07-16 21:10:40] 
开始执行: 蓝牙测试
[2025-07-16 21:10:40] 执行蓝牙测试...
[2025-07-16 21:10:40] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-16 21:10:40] 执行命令: adb shell bluetoothctl show
[2025-07-16 21:10:41] 命令执行成功
[2025-07-16 21:10:41] 返回数据:
[2025-07-16 21:10:41]   Controller 24:21:5E:C0:30:F3 (public)
[2025-07-16 21:10:41]   Name: CMG-1
[2025-07-16 21:10:41]   Alias: CMG-1
[2025-07-16 21:10:41]   Class: 0x006c0000 (7077888)
[2025-07-16 21:10:41]   Powered: yes
[2025-07-16 21:10:41]   PowerState: on
[2025-07-16 21:10:41]   Discoverable: no
[2025-07-16 21:10:41]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-16 21:10:41]   Pairable: yes
[2025-07-16 21:10:41]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-16 21:10:41]   Modalias: usb:v1D6Bp0246d0544
[2025-07-16 21:10:41]   Discovering: no
[2025-07-16 21:10:41]   Roles: central
[2025-07-16 21:10:41]   Roles: peripheral
[2025-07-16 21:10:41]   Advertising Features:
[2025-07-16 21:10:41]   ActiveInstances: 0x00 (0)
[2025-07-16 21:10:41]   SupportedInstances: 0x10 (16)
[2025-07-16 21:10:41]   SupportedIncludes: tx-power
[2025-07-16 21:10:41]   SupportedIncludes: appearance
[2025-07-16 21:10:41]   SupportedIncludes: local-name
[2025-07-16 21:10:41]   SupportedSecondaryChannels: 1M
[2025-07-16 21:10:41]   SupportedSecondaryChannels: 2M
[2025-07-16 21:10:41]   SupportedSecondaryChannels: Coded
[2025-07-16 21:10:41]   SupportedCapabilities Key: MaxAdvLen
[2025-07-16 21:10:41]   SupportedCapabilities Value: 0x1f (31)
[2025-07-16 21:10:41]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-16 21:10:41]   SupportedCapabilities Value: 0x1f (31)
[2025-07-16 21:10:41]   SupportedFeatures: CanSetTxPower
[2025-07-16 21:10:41]   SupportedFeatures: HardwareOffload
[2025-07-16 21:10:41]   Advertisement Monitor Features:
[2025-07-16 21:10:41]   SupportedMonitorTypes: or_patterns
[2025-07-16 21:10:41] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-16 21:10:41] 蓝牙控制器MAC地址: 24:21:5E:C0:30:F3
[2025-07-16 21:10:41] 
开始执行: WiFi测试
[2025-07-16 21:10:41] 执行WiFi测试...
[2025-07-16 21:10:41] 开始网络发包延时测试...
[2025-07-16 21:10:41] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-16 21:10:41] 正在进行10秒钟的网络延时测试...
[2025-07-16 21:10:41] ❌ ping命令执行失败: ping: www.baidu.com: Temporary failure in name resolution

[2025-07-16 21:10:41] 
测试完成 - 通过率: 12/15
[2025-07-16 21:10:41] ❌ 存在测试失败项！
[2025-07-16 21:10:41] 测试记录已保存: records/1111111111111_20250716_211041.json
[2025-07-16 21:10:41] 测试日志已保存: records/1111111111111_20250716_211041.log

