[2025-07-11 15:38:47] 开始测试 - SN: 11111111111111111
[2025-07-11 15:38:47] 
开始执行: 设备连接状态检测
[2025-07-11 15:38:47] 设备连接测试通过，检测到 1 个设备
[2025-07-11 15:38:47] 设备: ?	device
[2025-07-11 15:38:47] 
开始执行: USB关键器件检测
[2025-07-11 15:38:47] 执行USB设备检测...
[2025-07-11 15:38:47] 执行命令: adb shell lsusb
[2025-07-11 15:38:47] 设备返回数据:
[2025-07-11 15:38:47] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-11 15:38:47] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-11 15:38:47] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-11 15:38:47] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-11 15:38:47] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-11 15:38:47] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-11 15:38:47] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-11 15:38:47] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-11 15:38:47] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-11 15:38:48] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-11 15:38:48] 总共解析到 9 个设备
[2025-07-11 15:38:48] ✅ 所有预期的设备ID都已找到
[2025-07-11 15:38:48] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 15:38:48] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 15:38:48] ✅ USB设备检测通过
[2025-07-11 15:38:48] 
开始执行: CAN0测试
[2025-07-11 15:38:48] 执行CAN0测试流程...
[2025-07-11 15:38:48] 执行命令: adb shell ip link set can0 down
[2025-07-11 15:38:48] CAN0已关闭
[2025-07-11 15:38:48] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-11 15:38:48] CAN0已启动
[2025-07-11 15:38:48] CAN监听线程已启动...
[2025-07-11 15:38:49] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-11 15:38:49] CAN测试数据已发送，等待监听返回...
[2025-07-11 15:38:49] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-11 15:38:51] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-11 15:38:51] 
开始执行: GPS测试
[2025-07-11 15:38:51] 执行GPS测试...
[2025-07-11 15:38:51] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-11 15:39:01] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-11 15:39:02] 
开始执行: 4G模组测试
[2025-07-11 15:39:02] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-11 15:39:02] 监听线程已启动，等待串口数据...
[2025-07-11 15:39:04] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-11 15:39:04] AT+CCID指令已发送，等待串口返回...
[2025-07-11 15:39:04] 串口输出: AT+CCID
[2025-07-11 15:39:04] 串口输出: 
[2025-07-11 15:39:04] 串口输出: 
[2025-07-11 15:39:04] 串口输出: +CCID: 89860114851012535241
[2025-07-11 15:39:06] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-11 15:39:07] 
开始执行: 按键测试
[2025-07-11 15:39:07] 开始按键测试...
[2025-07-11 15:39:07] 执行命令: adb shell evtest /dev/input/event5
[2025-07-11 15:39:10] 原始事件: Event: time 1752219551.808686, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 15:39:10] 解析结果: key_code=662, value=1
[2025-07-11 15:39:10] ✓ 检测到档位减(662)按下
[2025-07-11 15:39:10] 原始事件: Event: time 1752219551.958423, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 15:39:10] 解析结果: key_code=662, value=0
[2025-07-11 15:39:10] ✓ 档位减(662)测试通过
[2025-07-11 15:39:11] 原始事件: Event: time 1752219552.758650, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-11 15:39:11] 解析结果: key_code=658, value=1
[2025-07-11 15:39:11] ✓ 检测到喇叭键(658)按下
[2025-07-11 15:39:11] 原始事件: Event: time 1752219552.908497, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-11 15:39:11] 解析结果: key_code=658, value=0
[2025-07-11 15:39:11] ✓ 喇叭键(658)测试通过
[2025-07-11 15:39:13] 原始事件: Event: time 1752219554.358516, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-11 15:39:13] 解析结果: key_code=661, value=1
[2025-07-11 15:39:13] ✓ 检测到静音键(661)按下
[2025-07-11 15:39:13] 原始事件: Event: time 1752219554.558651, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-11 15:39:13] 解析结果: key_code=661, value=0
[2025-07-11 15:39:13] ✓ 静音键(661)测试通过
[2025-07-11 15:39:14] 按键测试失败 - 只检测到3个按键
[2025-07-11 15:39:16] 
开始执行: 按键灯测试
[2025-07-11 15:39:16] 开始LED背光灯测试...
[2025-07-11 15:39:16] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-11 15:39:16] LED控制命令执行失败: error: no devices/emulators found

[2025-07-11 15:39:17] 
开始执行: 手电筒测试
[2025-07-11 15:39:17] 开始手电筒LED测试...
[2025-07-11 15:39:17] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-11 15:39:17] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-11 15:39:17] 
开始执行: 摇杆使能测试
[2025-07-11 15:39:17] 执行摇杆测试...
[2025-07-11 15:39:17] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-11 15:39:18] 命令执行失败: error: no devices/emulators found

[2025-07-11 15:39:18] 
开始执行: 前摄像头测试
[2025-07-11 15:39:18] 开始执行前摄像头测试...
[2025-07-11 15:39:18] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-11 15:39:18] 命令执行失败: error: no devices/emulators found

[2025-07-11 15:39:19] 
开始执行: 光感测试
[2025-07-11 15:39:19] 执行光感测试...
[2025-07-11 15:39:19] 执行命令: adb shell evtest /dev/input/event1
[2025-07-11 15:39:19] 光感测试失败 - 未检测到数值变化
[2025-07-11 15:39:19] 
开始执行: 回充摄像头测试
[2025-07-11 15:39:19] 开始执行回充摄像头测试...
[2025-07-11 15:39:19] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-11 15:39:19] 命令执行失败: error: no devices/emulators found

[2025-07-11 15:39:20] 
开始执行: 喇叭测试
[2025-07-11 15:39:20] 执行喇叭测试...
[2025-07-11 15:39:20] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-11 15:39:20] 命令执行失败: error: no devices/emulators found

[2025-07-11 15:39:20] 
开始执行: 蓝牙测试
[2025-07-11 15:39:20] 执行蓝牙测试...
[2025-07-11 15:39:20] 启动蓝牙服务...
[2025-07-11 15:39:20] 启动蓝牙服务失败: error: no devices/emulators found

[2025-07-11 15:39:20] 开启蓝牙...
[2025-07-11 15:39:20] 开启蓝牙失败: error: no devices/emulators found

[2025-07-11 15:39:20] 执行命令: adb shell bluetoothctl devices
[2025-07-11 15:39:20] 命令执行失败: error: no devices/emulators found

[2025-07-11 15:39:21] 
开始执行: WiFi测试
[2025-07-11 15:39:21] 执行WiFi测试...
[2025-07-11 15:39:21] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-11 15:39:21] 命令执行失败: error: no devices/emulators found

[2025-07-11 15:39:21] 
测试完成 - 通过率: 4/15
[2025-07-11 15:39:21] ❌ 存在测试失败项！
[2025-07-11 15:39:21] 测试记录已保存: records/11111111111111111_20250711_153921.json

