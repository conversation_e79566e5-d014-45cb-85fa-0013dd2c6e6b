{"sn": "8888888888888888", "timestamp": "2025-07-18T12:43:30.674876", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-18T12:41:28.402464"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-18T12:41:29.249043"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-18T12:41:30.311878"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-18T12:41:31.393223"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-18T12:41:35.245660"}, "4g_test": {"test_name": "4G模组版本测试", "status": "通过", "message": "EG912UGLAAR03A15M08", "timestamp": "2025-07-18T12:41:45.800057"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-18T12:41:50.459741"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-18T12:42:11.313118"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-18T12:42:15.803690"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-18T12:42:17.542326"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T12:42:18.100474"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-18T12:42:26.471143"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T12:42:41.674527"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-18T12:42:50.295139"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 24:21:5E:C0:30:4A", "timestamp": "2025-07-18T12:43:04.717691"}, "4g_network_test": {"test_name": "4G网络测试", "status": "失败", "message": "4G网络不通", "timestamp": "2025-07-18T12:43:05.339728"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "平均延时: 10.93ms (优秀)", "timestamp": "2025-07-18T12:43:10.989475"}}}