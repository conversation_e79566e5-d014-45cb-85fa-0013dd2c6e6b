[2025-07-18 13:25:50] === 开始新的测试 ===
[2025-07-18 13:25:50] 开始测试
[2025-07-18 13:25:50] 工序: 右手臂功能测试
[2025-07-18 13:25:50] 序列号: 444444444444
[2025-07-18 13:25:50] 开始运行 右手臂功能测试 工序测试，共 17 个项目
[2025-07-18 13:25:50] 
开始执行: 设备连接状态检测
[2025-07-18 13:25:50] 设备连接测试通过，检测到 1 个设备
[2025-07-18 13:25:50] 设备: ?	device
[2025-07-18 13:25:50] 
开始执行: 3568版本测试
[2025-07-18 13:25:50] 执行3568版本测试...
[2025-07-18 13:25:50] 读取ROM版本号...
[2025-07-18 13:25:50] 执行命令: adb shell uname -a
[2025-07-18 13:25:50] uname命令执行成功
[2025-07-18 13:25:50] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 13:25:50] ✅ 3568版本测试成功
[2025-07-18 13:25:50] ROM版本号: Jul 9 12:01:12
[2025-07-18 13:25:50] ✅ 确认为RK3568平台
[2025-07-18 13:25:50] 
开始执行: USB关键器件检测
[2025-07-18 13:25:50] 执行USB设备检测...
[2025-07-18 13:25:50] 执行命令: adb shell lsusb
[2025-07-18 13:25:51] 设备返回数据:
[2025-07-18 13:25:51] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 13:25:51] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 13:25:51] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 13:25:51] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 13:25:51] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 13:25:51] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 13:25:51] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 13:25:51] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 13:25:51] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 13:25:51] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 13:25:51] 总共解析到 9 个设备
[2025-07-18 13:25:51] ✅ 所有预期的设备ID都已找到
[2025-07-18 13:25:51] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 13:25:51] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 13:25:51] ✅ USB设备检测通过
[2025-07-18 13:25:51] 
开始执行: CAN0测试
[2025-07-18 13:25:51] 执行CAN0测试流程...
[2025-07-18 13:25:51] 执行命令: adb shell ip link set can0 down
[2025-07-18 13:25:51] CAN0已关闭
[2025-07-18 13:25:51] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 13:25:51] CAN0已启动
[2025-07-18 13:25:51] CAN监听线程已启动...
[2025-07-18 13:25:52] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 13:25:52] CAN测试数据已发送，等待监听返回...
[2025-07-18 13:25:52] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 13:25:54] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 13:25:54] 
开始执行: GPS测试
[2025-07-18 13:25:54] 执行GPS测试...
[2025-07-18 13:25:54] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 13:26:04] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 13:26:04] 
开始执行: 4G模组版本测试
[2025-07-18 13:26:04] 执行4G模组版本测试...（获取模组型号）
[2025-07-18 13:26:04] 监听线程已启动，等待串口数据...
[2025-07-18 13:26:06] 执行命令: adb shell "echo -e 'ATI\r' > /dev/ttyUSB0"
[2025-07-18 13:26:07] ATI指令已发送，等待串口返回模组版本信息...
[2025-07-18 13:26:07] 串口输出: 
[2025-07-18 13:26:07] 串口输出: Quectel
[2025-07-18 13:26:07] 串口输出: EG912U
[2025-07-18 13:26:07] 串口输出: 
[2025-07-18 13:26:07] 串口输出: 
[2025-07-18 13:26:07] 串口输出: 
[2025-07-18 13:26:07] 串口输出: ATI
[2025-07-18 13:26:07] 串口输出: 
[2025-07-18 13:26:07] 串口输出: 
[2025-07-18 13:26:07] 串口输出: Quectel
[2025-07-18 13:26:07] 串口输出: 
[2025-07-18 13:26:07] 串口输出: EG912U
[2025-07-18 13:26:07] 串口输出: Revision: EG912UGLAAR03A15M08
[2025-07-18 13:26:07] 检测到模组版本: EG912UGLAAR03A15M08
[2025-07-18 13:26:07] ✅ 4G模组版本测试成功，EG912UGLAAR03A15M08
[2025-07-18 13:26:07] 
开始执行: 按键测试
[2025-07-18 13:26:07] 开始按键测试...
[2025-07-18 13:26:08] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 13:26:17] 原始事件: Event: time 1752816378.043243, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-18 13:26:17] 解析结果: key_code=659, value=1
[2025-07-18 13:26:17] ✓ 检测到档位加(659)按下
[2025-07-18 13:26:17] 原始事件: Event: time 1752816378.216462, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-18 13:26:17] 解析结果: key_code=659, value=0
[2025-07-18 13:26:17] ✓ 档位加(659)测试通过
[2025-07-18 13:26:18] 原始事件: Event: time 1752816378.789810, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-18 13:26:18] 解析结果: key_code=660, value=1
[2025-07-18 13:26:18] ✓ 检测到智驾键(660)按下
[2025-07-18 13:26:18] 原始事件: Event: time 1752816379.066590, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-18 13:26:18] 解析结果: key_code=660, value=0
[2025-07-18 13:26:18] ✓ 智驾键(660)测试通过
[2025-07-18 13:26:19] 原始事件: Event: time 1752816379.693260, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-18 13:26:19] 解析结果: key_code=656, value=1
[2025-07-18 13:26:19] ✓ 检测到锁定键(656)按下
[2025-07-18 13:26:19] 原始事件: Event: time 1752816379.966609, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-18 13:26:19] 解析结果: key_code=656, value=0
[2025-07-18 13:26:19] ✓ 锁定键(656)测试通过
[2025-07-18 13:26:20] 原始事件: Event: time 1752816380.766485, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-18 13:26:20] 解析结果: key_code=657, value=1
[2025-07-18 13:26:20] ✓ 检测到SOS键(657)按下
[2025-07-18 13:26:20] 原始事件: Event: time 1752816380.966496, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-18 13:26:20] 解析结果: key_code=657, value=0
[2025-07-18 13:26:20] ✓ SOS键(657)测试通过
[2025-07-18 13:26:20] 原始事件: Event: time 1752816381.543173, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 13:26:20] 解析结果: key_code=663, value=1
[2025-07-18 13:26:20] ✓ 检测到语音键(663)按下
[2025-07-18 13:26:21] 原始事件: Event: time 1752816381.669770, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 13:26:21] 解析结果: key_code=663, value=0
[2025-07-18 13:26:21] ✓ 语音键(663)测试通过
[2025-07-18 13:26:21] 原始事件: Event: time 1752816382.493141, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-18 13:26:21] 解析结果: key_code=661, value=1
[2025-07-18 13:26:21] ✓ 检测到静音键(661)按下
[2025-07-18 13:26:22] 原始事件: Event: time 1752816382.666415, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-18 13:26:22] 解析结果: key_code=661, value=0
[2025-07-18 13:26:22] ✓ 静音键(661)测试通过
[2025-07-18 13:26:22] 原始事件: Event: time 1752816383.466478, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-18 13:26:22] 解析结果: key_code=658, value=1
[2025-07-18 13:26:22] ✓ 检测到喇叭键(658)按下
[2025-07-18 13:26:23] 原始事件: Event: time 1752816383.666462, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-18 13:26:23] 解析结果: key_code=658, value=0
[2025-07-18 13:26:23] ✓ 喇叭键(658)测试通过
[2025-07-18 13:26:26] 原始事件: Event: time 1752816387.393153, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-18 13:26:26] 解析结果: key_code=658, value=1
[2025-07-18 13:26:26] ✓ 检测到喇叭键(658)按下
[2025-07-18 13:26:27] 原始事件: Event: time 1752816387.743071, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-18 13:26:27] 解析结果: key_code=658, value=0
[2025-07-18 13:26:27] ✓ 喇叭键(658)测试通过
[2025-07-18 13:26:28] 原始事件: Event: time 1752816389.066577, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-18 13:26:28] 解析结果: key_code=662, value=1
[2025-07-18 13:26:28] ✓ 检测到档位减(662)按下
[2025-07-18 13:26:28] 原始事件: Event: time 1752816389.366575, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-18 13:26:28] 解析结果: key_code=662, value=0
[2025-07-18 13:26:28] ✓ 档位减(662)测试通过
[2025-07-18 13:26:29] 按键测试完成 - 检测到8个按键
[2025-07-18 13:26:31] 
开始执行: 按键灯测试
[2025-07-18 13:26:31] 开始LED背光灯测试...
[2025-07-18 13:26:31] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 13:26:31] LED控制命令执行成功
[2025-07-18 13:26:31] 🔧 显示确认对话框: LED测试确认
[2025-07-18 13:26:31] 🔧 对话框窗口已创建
[2025-07-18 13:26:31] 🔧 '是'按钮已创建
[2025-07-18 13:26:31] 🔧 '否'按钮已创建
[2025-07-18 13:26:31] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 13:26:32] 👤 用户选择: 是 (测试通过)
[2025-07-18 13:26:32] 🔧 对话框关闭，用户响应: yes
[2025-07-18 13:26:32] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-18 13:26:32] LED灯已关闭
[2025-07-18 13:26:32] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-18 13:26:33] 
开始执行: 手电筒测试
[2025-07-18 13:26:33] 开始手电筒LED测试...
[2025-07-18 13:26:33] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 13:26:33] 手电筒控制命令执行成功
[2025-07-18 13:26:33] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-18 13:26:33] 🔧 对话框窗口已创建
[2025-07-18 13:26:33] 🔧 '是'按钮已创建
[2025-07-18 13:26:33] 🔧 '否'按钮已创建
[2025-07-18 13:26:33] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 13:26:35] 👤 用户选择: 是 (测试通过)
[2025-07-18 13:26:36] 🔧 对话框关闭，用户响应: yes
[2025-07-18 13:26:36] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-18 13:26:36] 手电筒已关闭
[2025-07-18 13:26:36] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-18 13:26:36] 
开始执行: 摇杆使能测试
[2025-07-18 13:26:36] 执行摇杆测试...
[2025-07-18 13:26:36] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 13:26:36] 命令执行成功
[2025-07-18 13:26:36] 返回数据: 255
[2025-07-18 13:26:36] 摇杆测试通过，值: 255
[2025-07-18 13:26:37] 
开始执行: 前摄像头测试
[2025-07-18 13:26:37] 开始执行前摄像头测试...
[2025-07-18 13:26:37] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 13:26:37] 命令执行成功，返回: 无输出
[2025-07-18 13:26:37] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-18 13:26:38] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.317234393
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-18 13:26:38] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-18 13:26:38] 拉取命令执行成功
[2025-07-18 13:26:38] 返回数据: [ 39%] /data/camera/cam0_3840x2160.jpg
[ 78%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 28.6 MB/s (167610 bytes in 0.006s)
[2025-07-18 13:26:38] 图片已保存: cam0_3840x2160.jpg
[2025-07-18 13:26:39] 用户确认结果: 通过
[2025-07-18 13:26:39] 
开始执行: 光感测试
[2025-07-18 13:26:39] 执行光感测试...
[2025-07-18 13:26:39] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 13:26:44] 光感测试完成 - 检测到数值变化
[2025-07-18 13:26:44] 数值从 5 变化到 0
[2025-07-18 13:26:44] 
开始执行: 回充摄像头测试
[2025-07-18 13:26:44] 开始执行回充摄像头测试...
[2025-07-18 13:26:44] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 13:26:44] 命令执行成功，返回: 无输出
[2025-07-18 13:26:44] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-18 13:26:45] 命令执行成功，返回: 无输出
[2025-07-18 13:26:45] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-18 13:26:45] 拉取命令执行成功
[2025-07-18 13:26:45] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 10.8 MB/s (38408 bytes in 0.003s)
[2025-07-18 13:26:45] 图片已保存: output.jpg
[2025-07-18 13:26:46] 用户确认结果: 通过
[2025-07-18 13:26:46] 
开始执行: 喇叭测试
[2025-07-18 13:26:46] 执行喇叭测试...
[2025-07-18 13:26:46] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 13:27:00] 命令执行成功
[2025-07-18 13:27:00] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-18 13:27:00] 音频播放完成
[2025-07-18 13:27:01] 
开始执行: 蓝牙测试
[2025-07-18 13:27:01] 执行蓝牙测试...
[2025-07-18 13:27:01] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 13:27:01] 执行命令: adb shell bluetoothctl show
[2025-07-18 13:27:01] 命令执行成功
[2025-07-18 13:27:01] 返回数据:
[2025-07-18 13:27:01]   Controller 24:21:5E:C0:30:4A (public)
[2025-07-18 13:27:01]   Name: CMG-1
[2025-07-18 13:27:01]   Alias: CMG-1
[2025-07-18 13:27:01]   Class: 0x006c0000 (7077888)
[2025-07-18 13:27:01]   Powered: yes
[2025-07-18 13:27:01]   PowerState: on
[2025-07-18 13:27:01]   Discoverable: no
[2025-07-18 13:27:01]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-18 13:27:01]   Pairable: yes
[2025-07-18 13:27:01]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-18 13:27:01]   Modalias: usb:v1D6Bp0246d0544
[2025-07-18 13:27:01]   Discovering: no
[2025-07-18 13:27:01]   Roles: central
[2025-07-18 13:27:01]   Roles: peripheral
[2025-07-18 13:27:01]   Advertising Features:
[2025-07-18 13:27:01]   ActiveInstances: 0x00 (0)
[2025-07-18 13:27:01]   SupportedInstances: 0x10 (16)
[2025-07-18 13:27:01]   SupportedIncludes: tx-power
[2025-07-18 13:27:01]   SupportedIncludes: appearance
[2025-07-18 13:27:01]   SupportedIncludes: local-name
[2025-07-18 13:27:01]   SupportedSecondaryChannels: 1M
[2025-07-18 13:27:01]   SupportedSecondaryChannels: 2M
[2025-07-18 13:27:01]   SupportedSecondaryChannels: Coded
[2025-07-18 13:27:01]   SupportedCapabilities Key: MaxAdvLen
[2025-07-18 13:27:01]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 13:27:01]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-18 13:27:01]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 13:27:01]   SupportedFeatures: CanSetTxPower
[2025-07-18 13:27:01]   SupportedFeatures: HardwareOffload
[2025-07-18 13:27:01]   Advertisement Monitor Features:
[2025-07-18 13:27:01]   SupportedMonitorTypes: or_patterns
[2025-07-18 13:27:01] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-18 13:27:01] 蓝牙控制器MAC地址: 24:21:5E:C0:30:4A
[2025-07-18 13:27:02] 
开始执行: 4G网络测试
[2025-07-18 13:27:02] 执行4G网络测试...
[2025-07-18 13:27:02] 第一步：关闭WiFi网络...
[2025-07-18 13:27:02] WiFi已关闭，等待4G网络连接...
[2025-07-18 13:27:05] 第二步：使用4G网络进行ping测试...
[2025-07-18 13:27:05] ping目标: www.baidu.com
[2025-07-18 13:27:05] 测试时长: 10秒
[2025-07-18 13:27:05] 4G网络ping测试失败: ping: www.baidu.com: Temporary failure in name resolution

[2025-07-18 13:27:05] 第三步：重新打开WiFi网络...
[2025-07-18 13:27:05] WiFi已重新打开
[2025-07-18 13:27:08] 
开始执行: WiFi测试
[2025-07-18 13:27:08] 执行WiFi测试...
[2025-07-18 13:27:08] 第一步：关闭4G网络...
[2025-07-18 13:27:08] 关闭usb0接口（4G网络）...
[2025-07-18 13:27:08] 执行命令: adb shell ifconfig usb0 down
[2025-07-18 13:27:08] ✅ 4G网络已关闭
[2025-07-18 13:27:10] 第二步：连接WiFi网络...
[2025-07-18 13:27:10] 停止现有的wpa_supplicant进程...
[2025-07-18 13:27:10] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 13:27:10] 清理wpa_supplicant socket文件...
[2025-07-18 13:27:10] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 13:27:10] 关闭wlan0接口...
[2025-07-18 13:27:10] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 13:27:10] 连接WiFi网络...
[2025-07-18 13:27:10] 执行WiFi连接命令...
[2025-07-18 13:27:10] SSID: Orion_SZ_5G
[2025-07-18 13:27:14] WiFi连接命令执行完成，返回数据:
[2025-07-18 13:27:14]   Successfully initialized wpa_supplicant
[2025-07-18 13:27:14]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-18 13:27:14]   1
[2025-07-18 13:27:14]   OK
[2025-07-18 13:27:14]   OK
[2025-07-18 13:27:14]   OK
[2025-07-18 13:27:14]   deleting routers
[2025-07-18 13:27:14]   adding dns ************
[2025-07-18 13:27:14]   adding dns ***********
[2025-07-18 13:27:14]   Connected to 6c:c4:9f:2b:78:70 (on wlan0)
[2025-07-18 13:27:14]   SSID: Orion_SZ_5G
[2025-07-18 13:27:14]   freq: 5220
[2025-07-18 13:27:14]   RX: 77999 bytes (814 packets)
[2025-07-18 13:27:14]   TX: 52863 bytes (662 packets)
[2025-07-18 13:27:14]   signal: -71 dBm
[2025-07-18 13:27:14]   rx bitrate: 162.5 MBit/s 40MHz HE-MCS 7 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-18 13:27:14]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-18 13:27:14]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-18 13:27:14]   link/ether 24:21:5e:c0:3c:0b brd ff:ff:ff:ff:ff:ff
[2025-07-18 13:27:14]   inet 192.168.20.167/24 brd 192.168.20.255 scope global wlan0
[2025-07-18 13:27:14]   valid_lft forever preferred_lft forever
[2025-07-18 13:27:14]   inet6 fe80::c5c9:135f:3de4:4a19/64 scope link tentative
[2025-07-18 13:27:14]   valid_lft forever preferred_lft forever
[2025-07-18 13:27:14] ✅ WiFi连接成功
[2025-07-18 13:27:14] 等待网络稳定...
[2025-07-18 13:27:17] 第二步：开始网络发包延时测试...
[2025-07-18 13:27:17] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-18 13:27:17] 正在进行10秒钟的网络延时测试...
[2025-07-18 13:27:26] ping命令执行成功
[2025-07-18 13:27:26] 返回数据:
[2025-07-18 13:27:26]   PING www.a.shifen.com (157.148.69.186) 56(84) bytes of data.
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=1 ttl=54 time=12.0 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=2 ttl=54 time=12.7 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=3 ttl=54 time=11.7 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=4 ttl=54 time=13.1 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=5 ttl=54 time=12.7 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=6 ttl=54 time=12.0 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=7 ttl=54 time=12.1 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=8 ttl=54 time=12.1 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=9 ttl=54 time=12.7 ms
[2025-07-18 13:27:26]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=10 ttl=54 time=12.3 ms
[2025-07-18 13:27:26]   --- www.a.shifen.com ping statistics ---
[2025-07-18 13:27:26]   10 packets transmitted, 10 received, 0% packet loss, time 9013ms
[2025-07-18 13:27:26]   rtt min/avg/max/mdev = 11.657/12.346/13.109/0.421 ms
[2025-07-18 13:27:26] 检测到延时: 12.0 ms
[2025-07-18 13:27:26] 检测到延时: 12.7 ms
[2025-07-18 13:27:26] 检测到延时: 11.7 ms
[2025-07-18 13:27:26] 检测到延时: 13.1 ms
[2025-07-18 13:27:26] 检测到延时: 12.7 ms
[2025-07-18 13:27:26] 检测到延时: 12.0 ms
[2025-07-18 13:27:26] 检测到延时: 12.1 ms
[2025-07-18 13:27:26] 检测到延时: 12.1 ms
[2025-07-18 13:27:26] 检测到延时: 12.7 ms
[2025-07-18 13:27:26] 检测到延时: 12.3 ms
[2025-07-18 13:27:26] ✅ WiFi延时测试成功
[2025-07-18 13:27:26] 发包数量: 10 个
[2025-07-18 13:27:26] 平均延时: 12.34 ms
[2025-07-18 13:27:26] 最小延时: 11.70 ms
[2025-07-18 13:27:26] 最大延时: 13.10 ms
[2025-07-18 13:27:26] 第三步：恢复4G网络...
[2025-07-18 13:27:26] 重新打开usb0接口（4G网络）...
[2025-07-18 13:27:26] 执行命令: adb shell ifconfig usb0 up
[2025-07-18 13:27:26] ✅ 4G网络已恢复
[2025-07-18 13:27:28] 
测试完成 - 通过率: 15/17
[2025-07-18 13:27:28] ❌ 存在测试失败项！
[2025-07-18 13:27:28] 测试记录已保存: records/444444444444_20250718_132728.json
[2025-07-18 13:27:28] 测试日志已保存: records/444444444444_20250718_132728.log

