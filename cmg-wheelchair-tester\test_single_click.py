#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单项点击功能
直接测试双击和右键菜单是否工作
"""

import tkinter as tk
from tkinter import ttk
import json

def test_single_click():
    """测试单项点击功能"""
    print("=== 测试单项点击功能 ===")
    
    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        test_projects = config.get("test_projects", [])
        work_processes = config.get("work_processes", {})
    except Exception as e:
        print(f"配置加载失败: {e}")
        return
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("单项点击功能测试")
    root.geometry("800x600")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="单项点击功能测试", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 说明
    info_text = """测试说明:
1. 双击测试项目应该触发单项测试
2. 右键点击应该显示菜单
3. 观察日志输出确认功能是否正常"""
    
    ttk.Label(main_frame, text=info_text, justify=tk.LEFT).pack(pady=(0, 20))
    
    # 创建测试项目树
    columns = ("data", "result")
    test_tree = ttk.Treeview(main_frame, columns=columns, show="tree headings", height=10)
    
    test_tree.heading("#0", text="测试项目", anchor=tk.W)
    test_tree.heading("data", text="测试数据", anchor=tk.W)
    test_tree.heading("result", text="测试结果", anchor=tk.CENTER)
    
    test_tree.column("#0", width=200)
    test_tree.column("data", width=150)
    test_tree.column("result", width=80)
    
    test_tree.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 日志区域
    log_frame = ttk.LabelFrame(main_frame, text="事件日志", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True)
    
    log_text = tk.Text(log_frame, height=8, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        log_text.insert(tk.END, log_entry)
        log_text.see(tk.END)
        root.update_idletasks()
        print(message)  # 同时输出到控制台
    
    # 初始化测试项目
    if work_processes:
        process_name = "整机半成品功能测试"
        if process_name in work_processes:
            test_ids = work_processes[process_name]["test_ids"][:5]  # 只显示前5个
            
            for test_id in test_ids:
                project_name = test_id
                for project in test_projects:
                    if project["id"] == test_id:
                        project_name = project["name"]
                        break
                
                item = test_tree.insert("", "end", text=project_name)
                test_tree.set(item, "data", "")
                test_tree.set(item, "result", "")
    
    # 事件处理函数
    def on_double_click(event):
        """双击事件处理"""
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"✅ 双击事件触发: {test_name}")
            
            # 模拟测试执行
            test_tree.set(item[0], "result", "测试中")
            root.after(2000, lambda: finish_test(item[0], test_name))
        else:
            log_message("❌ 双击事件触发但没有选中项目")
    
    def finish_test(item, test_name):
        """完成测试"""
        import random
        if random.random() > 0.5:
            test_tree.set(item, "result", "PASS")
            test_tree.set(item, "data", "测试通过")
            log_message(f"✅ {test_name} - 测试通过")
        else:
            test_tree.set(item, "result", "FAIL")
            test_tree.set(item, "data", "测试失败")
            log_message(f"❌ {test_name} - 测试失败")
    
    def show_test_menu(event):
        """显示右键菜单"""
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"✅ 右键菜单事件触发: {test_name}")
            test_menu.post(event.x_root, event.y_root)
        else:
            log_message("❌ 右键菜单事件触发但没有选中项目")
    
    def run_menu_test():
        """从菜单运行测试"""
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"✅ 菜单测试触发: {test_name}")
            
            # 模拟测试执行
            test_tree.set(item[0], "result", "测试中")
            root.after(2000, lambda: finish_test(item[0], test_name))
    
    def show_test_details():
        """显示测试详情"""
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"✅ 查看详情触发: {test_name}")
            
            from tkinter import messagebox
            messagebox.showinfo("测试详情", f"测试项目: {test_name}")
    
    # 创建右键菜单
    test_menu = tk.Menu(root, tearoff=0)
    test_menu.add_command(label="运行测试", command=run_menu_test)
    test_menu.add_command(label="查看详情", command=show_test_details)
    
    # 绑定事件
    test_tree.bind("<Double-Button-1>", on_double_click)
    test_tree.bind("<Button-3>", show_test_menu)
    
    # 测试按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    def test_selection():
        """测试选择功能"""
        items = test_tree.get_children()
        if items:
            test_tree.selection_set(items[0])
            test_tree.focus(items[0])
            log_message("✅ 已选择第一个项目")
        else:
            log_message("❌ 没有项目可选择")
    
    def clear_log():
        """清空日志"""
        log_text.delete(1.0, tk.END)
        log_message("日志已清空")
    
    ttk.Button(button_frame, text="选择第一项", command=test_selection).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="清空日志", command=clear_log).pack(side=tk.LEFT, padx=5)
    
    # 初始化日志
    log_message("单项点击功能测试已准备就绪")
    log_message("请尝试双击测试项目或右键点击")
    log_message("观察日志输出确认功能是否正常工作")
    
    print("测试窗口已创建，请进行交互测试")
    root.mainloop()

if __name__ == "__main__":
    print("单项点击功能测试")
    print("=" * 50)
    
    test_single_click()
    
    print("\n测试完成")
