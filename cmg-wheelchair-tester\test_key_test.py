#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试按键测试功能
"""

import tkinter as tk
from tkinter import ttk
import subprocess
import threading
import time
import queue

def test_key_parsing():
    """测试按键事件解析功能"""
    print("=== 测试按键事件解析 ===")
    
    # 模拟evtest输出
    test_lines = [
        "Event: time 1720123456.123456, type 1 (EV_KEY), code 656 (KEY_F1), value 1",
        "Event: time 1720123456.223456, type 1 (EV_KEY), code 656 (KEY_F1), value 0",
        "Event: time 1720123456.323456, type 1 (EV_KEY), code 657 (KEY_F2), value 1",
        "Event: time 1720123456.423456, type 1 (EV_KEY), code 657 (KEY_F2), value 0",
        "Event: time 1720123456.523456, type 1 (EV_KEY), code 658 (KEY_F3), value 1",
        "Event: time 1720123456.623456, type 1 (EV_KEY), code 658 (KEY_F3), value 0",
    ]
    
    expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
    detected_keys = set()
    key_press_states = {}
    
    for line in test_lines:
        print(f"处理行: {line}")
        
        if "EV_KEY" in line and "KEY_" in line:
            try:
                key_code = None
                value = None
                
                parts = line.split(", ")
                for part in parts:
                    part = part.strip()
                    if part.startswith("code "):
                        code_str = part.split("code ")[1].split(" ")[0]
                        key_code = int(code_str)
                    elif part.startswith("value "):
                        value = int(part.split("value ")[1])
                
                if key_code in expected_keys and value is not None:
                    if value == 1:
                        key_press_states[key_code] = True
                        print(f"  -> 按键{key_code}按下")
                    elif value == 0 and key_press_states.get(key_code, False):
                        detected_keys.add(key_code)
                        print(f"  -> 按键{key_code}测试完成")
                
            except (ValueError, IndexError) as e:
                print(f"  -> 解析失败: {e}")
    
    print(f"\n检测到的按键: {sorted(detected_keys)}")
    print(f"测试结果: {'通过' if len(detected_keys) >= 3 else '失败'}")

def test_evtest_command():
    """测试evtest命令执行"""
    print("\n=== 测试evtest命令 ===")
    
    try:
        # 测试adb连接
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            print("ADB连接失败，无法进行实际测试")
            return
        
        devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
        if not any('device' in line for line in devices):
            print("没有连接的设备，无法进行实际测试")
            return
        
        print("ADB连接正常，设备已连接")
        
        # 测试evtest命令
        print("执行: adb shell evtest")
        process = subprocess.Popen(
            ['adb', 'shell', 'evtest'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待设备列表
        time.sleep(2)
        
        # 发送设备选择
        process.stdin.write("5\n")
        process.stdin.flush()
        
        print("已选择设备5，等待按键事件...")
        print("请在5秒内按一个按键进行测试...")
        
        # 读取输出（最多5秒）
        start_time = time.time()
        while time.time() - start_time < 5:
            if process.poll() is not None:
                break
            
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    print(f"输出: {line}")
                    
                    if "EV_KEY" in line:
                        print("检测到按键事件！")
                        break
            except:
                break
        
        # 终止进程
        try:
            process.terminate()
            process.wait(timeout=2)
        except:
            process.kill()
        
        print("evtest命令测试完成")
        
    except Exception as e:
        print(f"测试evtest命令时出错: {e}")

def create_test_gui():
    """创建测试GUI"""
    print("\n=== 创建测试GUI ===")
    
    root = tk.Tk()
    root.title("按键测试GUI测试")
    root.geometry("600x400")
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(
        main_frame,
        text="按键测试GUI测试",
        font=("Arial", 16, "bold")
    )
    title_label.pack(pady=(0, 20))
    
    # 按键状态显示
    keys_frame = ttk.LabelFrame(main_frame, text="按键状态", padding="10")
    keys_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
    
    expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
    key_vars = {}
    
    # 创建网格布局
    for i, key_code in enumerate(expected_keys):
        row = i // 4
        col = i % 4
        
        key_frame = ttk.Frame(keys_frame)
        key_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
        
        key_vars[key_code] = tk.StringVar(value="未测试")
        
        key_label = ttk.Label(
            key_frame,
            text=f"按键{key_code}:",
            font=("Arial", 10)
        )
        key_label.pack(side=tk.LEFT)
        
        status_label = ttk.Label(
            key_frame,
            textvariable=key_vars[key_code],
            font=("Arial", 10, "bold"),
            foreground="gray"
        )
        status_label.pack(side=tk.LEFT, padx=(5, 0))
    
    # 配置网格权重
    for i in range(4):
        keys_frame.columnconfigure(i, weight=1)
    
    # 测试按钮
    def simulate_key_press():
        """模拟按键按下"""
        for key_code in expected_keys:
            key_vars[key_code].set("完成")
            root.update()
            time.sleep(0.2)
    
    test_button = ttk.Button(
        main_frame,
        text="模拟按键测试",
        command=simulate_key_press
    )
    test_button.pack(pady=10)
    
    print("GUI创建完成，显示测试窗口...")
    root.mainloop()

if __name__ == "__main__":
    print("按键测试功能验证")
    print("=" * 50)
    
    # 运行测试
    test_key_parsing()
    
    # 如果需要测试实际命令，取消下面的注释
    # test_evtest_command()
    
    # 测试GUI
    create_test_gui()
