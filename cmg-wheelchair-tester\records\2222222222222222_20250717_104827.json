{"sn": "2222222222222222", "timestamp": "2025-07-17T10:48:27.548288", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-17T10:46:53.409608"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-17T10:46:53.752742"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-17T10:46:54.062514"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-17T10:46:57.773234"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-17T10:47:08.227083"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-17T10:47:22.840484"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-17T10:47:39.841288"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-17T10:47:44.843619"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-17T10:47:46.385596"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-17T10:47:46.944448"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-17T10:47:50.746661"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-17T10:47:54.382548"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-17T10:48:12.102573"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 40:55:48:07:E4:2D", "timestamp": "2025-07-17T10:48:26.418702"}, "wifi_test": {"test_name": "WiFi测试", "status": "失败", "message": "网络连接失败", "timestamp": "2025-07-17T10:48:26.959486"}}}