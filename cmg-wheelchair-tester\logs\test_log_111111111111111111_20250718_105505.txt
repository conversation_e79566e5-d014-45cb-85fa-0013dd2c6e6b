[2025-07-18 10:54:16] === 开始新的测试 ===
[2025-07-18 10:54:16] 开始测试
[2025-07-18 10:54:16] 工序: 右手臂功能测试
[2025-07-18 10:54:16] 序列号: 111111111111111111
[2025-07-18 10:54:16] 开始运行 右手臂功能测试 工序测试，共 16 个项目
[2025-07-18 10:54:16] 
开始执行: 设备连接状态检测
[2025-07-18 10:54:16] 设备连接测试通过，检测到 1 个设备
[2025-07-18 10:54:16] 设备: ?	device
[2025-07-18 10:54:17] 
开始执行: 3568版本测试
[2025-07-18 10:54:17] 执行3568版本测试...
[2025-07-18 10:54:17] 读取ROM版本号...
[2025-07-18 10:54:17] 执行命令: adb shell uname -a
[2025-07-18 10:54:17] uname命令执行成功
[2025-07-18 10:54:17] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 10:54:17] ✅ 3568版本测试成功
[2025-07-18 10:54:17] ROM版本号: Jul 9 12:01:12
[2025-07-18 10:54:17] ✅ 确认为RK3568平台
[2025-07-18 10:54:17] 
开始执行: USB关键器件检测
[2025-07-18 10:54:17] 执行USB设备检测...
[2025-07-18 10:54:17] 执行命令: adb shell lsusb
[2025-07-18 10:54:17] 设备返回数据:
[2025-07-18 10:54:17] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 10:54:17] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 10:54:17] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 10:54:17] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 10:54:17] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 10:54:17] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 10:54:17] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 10:54:17] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 10:54:17] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 10:54:17] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 10:54:17] 总共解析到 9 个设备
[2025-07-18 10:54:17] ✅ 所有预期的设备ID都已找到
[2025-07-18 10:54:17] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 10:54:17] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 10:54:17] ✅ USB设备检测通过
[2025-07-18 10:54:18] 
开始执行: CAN0测试
[2025-07-18 10:54:18] 执行CAN0测试流程...
[2025-07-18 10:54:18] 执行命令: adb shell ip link set can0 down
[2025-07-18 10:54:18] CAN0已关闭
[2025-07-18 10:54:18] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 10:54:18] CAN0已启动
[2025-07-18 10:54:18] CAN监听线程已启动...
[2025-07-18 10:54:19] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 10:54:19] CAN测试数据已发送，等待监听返回...
[2025-07-18 10:54:19] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 10:54:21] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 10:54:21] 
开始执行: GPS测试
[2025-07-18 10:54:21] 执行GPS测试...
[2025-07-18 10:54:21] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 10:54:32] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 10:54:32] 
开始执行: 4G模组测试
[2025-07-18 10:54:32] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 10:54:32] 监听线程已启动，等待串口数据...
[2025-07-18 10:54:34] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 10:54:34] AT+CCID指令已发送，等待串口返回...
[2025-07-18 10:54:34] 串口输出: AT+CCID
[2025-07-18 10:54:34] 串口输出: 
[2025-07-18 10:54:34] 串口输出: 
[2025-07-18 10:54:34] 串口输出: +CCID: 89860114851012535241
[2025-07-18 10:54:36] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-18 10:54:37] 
开始执行: 按键测试
[2025-07-18 10:54:37] 开始按键测试...
[2025-07-18 10:54:38] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 10:54:46] 原始事件: Event: time 1752807287.318368, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-18 10:54:46] 解析结果: key_code=662, value=1
[2025-07-18 10:54:46] ✓ 检测到档位减(662)按下
[2025-07-18 10:54:46] 原始事件: Event: time 1752807287.318420, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-18 10:54:47] 解析结果: key_code=659, value=1
[2025-07-18 10:54:47] ✓ 检测到档位加(659)按下
[2025-07-18 10:54:47] 原始事件: Event: time 1752807287.445120, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-18 10:54:47] 解析结果: key_code=662, value=0
[2025-07-18 10:54:47] ✓ 档位减(662)测试通过
[2025-07-18 10:54:47] 原始事件: Event: time 1752807287.495113, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-18 10:54:47] 解析结果: key_code=659, value=0
[2025-07-18 10:54:47] ✓ 档位加(659)测试通过
[2025-07-18 10:54:48] 原始事件: Event: time 1752807289.045250, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-18 10:54:48] 解析结果: key_code=661, value=1
[2025-07-18 10:54:48] ✓ 检测到静音键(661)按下
[2025-07-18 10:54:48] 原始事件: Event: time 1752807289.245242, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-18 10:54:48] 解析结果: key_code=661, value=0
[2025-07-18 10:54:48] ✓ 静音键(661)测试通过
[2025-07-18 10:54:49] 原始事件: Event: time 1752807290.268518, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 10:54:49] 解析结果: key_code=663, value=1
[2025-07-18 10:54:49] ✓ 检测到语音键(663)按下
[2025-07-18 10:54:50] 原始事件: Event: time 1752807290.418450, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 10:54:50] 解析结果: key_code=663, value=0
[2025-07-18 10:54:50] ✓ 语音键(663)测试通过
[2025-07-18 10:54:51] 原始事件: Event: time 1752807291.645076, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 10:54:51] 解析结果: key_code=663, value=1
[2025-07-18 10:54:51] ✓ 检测到语音键(663)按下
[2025-07-18 10:54:51] 原始事件: Event: time 1752807291.741677, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-18 10:54:51] 解析结果: key_code=660, value=1
[2025-07-18 10:54:51] ✓ 检测到智驾键(660)按下
[2025-07-18 10:54:51] 原始事件: Event: time 1752807291.791792, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 10:54:51] 解析结果: key_code=663, value=0
[2025-07-18 10:54:51] ✓ 语音键(663)测试通过
[2025-07-18 10:54:51] 原始事件: Event: time 1752807291.918512, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-18 10:54:51] 解析结果: key_code=660, value=0
[2025-07-18 10:54:51] ✓ 智驾键(660)测试通过
[2025-07-18 10:54:52] 原始事件: Event: time 1752807293.368357, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-18 10:54:52] 解析结果: key_code=656, value=1
[2025-07-18 10:54:53] ✓ 检测到锁定键(656)按下
[2025-07-18 10:54:53] 原始事件: Event: time 1752807293.645169, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-18 10:54:53] 解析结果: key_code=656, value=0
[2025-07-18 10:54:53] ✓ 锁定键(656)测试通过
[2025-07-18 10:54:54] 原始事件: Event: time 1752807294.418405, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-18 10:54:54] 解析结果: key_code=657, value=1
[2025-07-18 10:54:54] ✓ 检测到SOS键(657)按下
[2025-07-18 10:54:54] 原始事件: Event: time 1752807294.618391, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-18 10:54:54] 解析结果: key_code=657, value=0
[2025-07-18 10:54:54] ✓ SOS键(657)测试通过
[2025-07-18 10:54:55] 原始事件: Event: time 1752807295.818422, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 10:54:55] 解析结果: key_code=663, value=1
[2025-07-18 10:54:55] ✓ 检测到语音键(663)按下
[2025-07-18 10:54:55] 原始事件: Event: time 1752807295.991745, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 10:54:55] 解析结果: key_code=663, value=0
[2025-07-18 10:54:55] ✓ 语音键(663)测试通过
[2025-07-18 10:54:56] 原始事件: Event: time 1752807296.941706, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-18 10:54:56] 解析结果: key_code=661, value=1
[2025-07-18 10:54:56] ✓ 检测到静音键(661)按下
[2025-07-18 10:54:56] 按键测试失败 - 只检测到7个按键
[2025-07-18 10:54:59] 
开始执行: 按键灯测试
[2025-07-18 10:54:59] 开始LED背光灯测试...
[2025-07-18 10:54:59] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 10:54:59] LED控制命令执行失败: error: no devices/emulators found

[2025-07-18 10:55:00] 
开始执行: 手电筒测试
[2025-07-18 10:55:00] 开始手电筒LED测试...
[2025-07-18 10:55:00] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 10:55:00] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-18 10:55:01] 
开始执行: 摇杆使能测试
[2025-07-18 10:55:01] 执行摇杆测试...
[2025-07-18 10:55:01] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 10:55:01] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:55:01] 
开始执行: 前摄像头测试
[2025-07-18 10:55:01] 开始执行前摄像头测试...
[2025-07-18 10:55:02] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 10:55:02] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:55:02] 
开始执行: 光感测试
[2025-07-18 10:55:02] 执行光感测试...
[2025-07-18 10:55:02] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 10:55:02] 光感测试失败 - 未检测到数值变化
[2025-07-18 10:55:03] 
开始执行: 回充摄像头测试
[2025-07-18 10:55:03] 开始执行回充摄像头测试...
[2025-07-18 10:55:03] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 10:55:03] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:55:04] 
开始执行: 喇叭测试
[2025-07-18 10:55:04] 执行喇叭测试...
[2025-07-18 10:55:04] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 10:55:04] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:55:04] 
开始执行: 蓝牙测试
[2025-07-18 10:55:04] 执行蓝牙测试...
[2025-07-18 10:55:04] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 10:55:04] 执行命令: adb shell bluetoothctl show
[2025-07-18 10:55:04] ❌ 命令执行失败: error: no devices/emulators found

[2025-07-18 10:55:05] 
开始执行: WiFi测试
[2025-07-18 10:55:05] 执行WiFi测试...
[2025-07-18 10:55:05] 第一步：连接WiFi网络...
[2025-07-18 10:55:05] 停止现有的wpa_supplicant进程...
[2025-07-18 10:55:05] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 10:55:05] 清理wpa_supplicant socket文件...
[2025-07-18 10:55:05] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 10:55:05] 关闭wlan0接口...
[2025-07-18 10:55:05] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 10:55:05] 连接WiFi网络...
[2025-07-18 10:55:05] 执行WiFi连接命令...
[2025-07-18 10:55:05] SSID: Orion_SZ_5G
[2025-07-18 10:55:05] ❌ WiFi连接失败: error: no devices/emulators found

[2025-07-18 10:55:05] 
测试完成 - 通过率: 5/16
[2025-07-18 10:55:05] ❌ 存在测试失败项！
[2025-07-18 10:55:05] 测试记录已保存: records/111111111111111111_20250718_105505.json
[2025-07-18 10:55:05] 测试日志已保存: records/111111111111111111_20250718_105505.log

