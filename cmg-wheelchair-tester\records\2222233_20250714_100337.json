{"sn": "2222233", "timestamp": "2025-07-14T10:03:37.607253", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-14T10:01:17.228733"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-14T10:01:17.705872"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-14T10:01:18.541442"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-14T10:01:22.088790"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-14T10:01:32.476245"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-14T10:01:46.986960"}, "led_test": {"test_name": "按键灯测试", "status": "失败", "message": "无背光", "timestamp": "2025-07-14T10:02:41.497977"}, "torch_test": {"test_name": "手电筒测试", "status": "失败", "message": "手电筒异常", "timestamp": "2025-07-14T10:02:57.232233"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-14T10:03:03.279698"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-14T10:03:03.724718"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-14T10:03:05.018458"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-14T10:03:19.455323"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-14T10:03:22.491536"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 40:55:48:07:E4:2D", "timestamp": "2025-07-14T10:03:36.770470"}, "wifi_test": {"test_name": "WiFi测试", "status": "失败", "message": "网络连接失败", "timestamp": "2025-07-14T10:03:37.231334"}}}