{"sn": "2222222222222222", "timestamp": "2025-07-18T11:55:23.709426", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-18T11:54:28.912007"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-18T11:54:29.581864"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-18T11:54:30.532144"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-18T11:54:31.413887"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-18T11:54:35.457629"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-18T11:54:46.279630"}, "key_test": {"test_name": "按键测试", "status": "失败", "message": "按键测试失败，只检测到2/8个按键", "timestamp": "2025-07-18T11:55:01.261994"}, "led_test": {"test_name": "按键灯测试", "status": "失败", "message": "LED控制失败", "timestamp": "2025-07-18T11:55:09.429183"}, "torch_test": {"test_name": "手电筒测试", "status": "失败", "message": "手电筒控制失败", "timestamp": "2025-07-18T11:55:10.866780"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-18T11:55:11.736440"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-18T11:55:12.857866"}, "light_sensor_test": {"test_name": "光感测试", "status": "失败", "message": "光感异常", "timestamp": "2025-07-18T11:55:13.982375"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-18T11:55:14.924645"}, "speaker_test": {"test_name": "喇叭测试", "status": "失败", "message": "播放失败", "timestamp": "2025-07-18T11:55:15.795631"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-18T11:55:16.428414"}, "4g_network_test": {"test_name": "4G网络测试", "status": "失败", "message": "关闭WiFi失败", "timestamp": "2025-07-18T11:55:17.193608"}, "wifi_test": {"test_name": "WiFi测试", "status": "失败", "message": "关闭4G网络失败", "timestamp": "2025-07-18T11:55:19.850185"}, "accel_test": {"test_name": "Accel", "status": "失败", "message": "日志读取失败", "timestamp": "2025-07-18T11:55:20.482692"}, "gyro_test": {"test_name": "Gyro", "status": "失败", "message": "日志读取失败", "timestamp": "2025-07-18T11:55:21.126822"}, "laser_test": {"test_name": "Laser", "status": "失败", "message": "日志读取失败", "timestamp": "2025-07-18T11:55:21.818043"}, "laser2_test": {"test_name": "Laser2", "status": "失败", "message": "日志读取失败", "timestamp": "2025-07-18T11:55:22.430924"}, "odom_test": {"test_name": "<PERSON><PERSON>", "status": "失败", "message": "日志读取失败", "timestamp": "2025-07-18T11:55:23.075502"}}}