#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的蓝牙测试功能
"""

import subprocess
import re

def test_bluetooth_show_command():
    """测试bluetoothctl show命令"""
    print("=== 测试bluetoothctl show命令 ===")
    
    try:
        print("执行命令: adb shell bluetoothctl show")
        result = subprocess.run(['adb', 'shell', 'bluetoothctl', 'show'], 
                              capture_output=True, text=True, timeout=15)
        
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("命令执行成功")
            output = result.stdout.strip()
            
            if output:
                print("返回数据:")
                lines = output.split('\n')
                for line in lines:
                    if line.strip():
                        print(f"  {line.strip()}")
                
                # 查找Controller行并提取MAC地址
                controller_mac = None
                for line in lines:
                    line = line.strip()
                    if line.startswith("Controller "):
                        print(f"\n找到Controller行: {line}")
                        # 提取MAC地址
                        parts = line.split()
                        if len(parts) >= 2:
                            mac_address = parts[1]
                            print(f"提取的MAC地址: {mac_address}")
                            # 验证MAC地址格式 (XX:XX:XX:XX:XX:XX)
                            if len(mac_address) == 17 and mac_address.count(':') == 5:
                                controller_mac = mac_address
                                print(f"✅ 有效的MAC地址: {controller_mac}")
                                break
                            else:
                                print(f"❌ 无效的MAC地址格式: {mac_address}")
                
                if controller_mac:
                    print(f"\n✅ 蓝牙测试成功")
                    print(f"蓝牙控制器MAC地址: {controller_mac}")
                    return True, controller_mac
                else:
                    print("\n❌ 未找到有效的蓝牙控制器")
                    return False, None
            else:
                print("❌ 无输出数据")
                return False, None
        else:
            print(f"❌ 命令执行失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试出错: {str(e)}")
        return False, None

def test_mac_address_parsing():
    """测试MAC地址解析功能"""
    print("\n=== 测试MAC地址解析功能 ===")
    
    # 测试用的输出数据
    test_outputs = [
        """Controller 24:21:5E:C0:30:F3 (public)
	Name: BlueZ 5.50
	Alias: BlueZ 5.50
	Class: 0x000000
	Powered: yes
	Discoverable: no
	Pairable: yes""",
        
        """Controller AA:BB:CC:DD:EE:FF (public)
	Name: Bluetooth Controller
	Alias: My Device
	Powered: yes""",
        
        """No default controller available""",
        
        """Controller 12:34:56:78:9A:BC (public)
	Name: Test Controller
	Powered: no"""
    ]
    
    for i, output in enumerate(test_outputs):
        print(f"\n--- 测试用例 {i+1} ---")
        print("输入数据:")
        for line in output.split('\n'):
            print(f"  {line}")
        
        # 解析MAC地址
        controller_mac = None
        lines = output.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith("Controller "):
                print(f"找到Controller行: {line}")
                parts = line.split()
                if len(parts) >= 2:
                    mac_address = parts[1]
                    print(f"提取的MAC地址: {mac_address}")
                    # 验证MAC地址格式
                    if len(mac_address) == 17 and mac_address.count(':') == 5:
                        controller_mac = mac_address
                        print(f"✅ 有效的MAC地址: {controller_mac}")
                        break
                    else:
                        print(f"❌ 无效的MAC地址格式: {mac_address}")
        
        if not controller_mac:
            print("❌ 未找到有效的蓝牙控制器")
        
        print(f"结果: {'成功' if controller_mac else '失败'}")

def test_adb_connection():
    """测试ADB连接"""
    print("\n=== 测试ADB连接 ===")
    
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        print(f"ADB devices返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("ADB连接正常")
            devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            connected_devices = [line for line in devices if 'device' in line]
            
            if connected_devices:
                print(f"已连接设备: {len(connected_devices)}个")
                for device in connected_devices:
                    print(f"  {device}")
                return True
            else:
                print("❌ 没有连接的设备")
                return False
        else:
            print(f"❌ ADB连接失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ ADB连接测试出错: {str(e)}")
        return False

def test_bluetooth_availability():
    """测试蓝牙功能可用性"""
    print("\n=== 测试蓝牙功能可用性 ===")
    
    try:
        # 测试bluetoothctl命令是否可用
        print("测试bluetoothctl命令可用性...")
        result = subprocess.run(['adb', 'shell', 'which', 'bluetoothctl'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            bluetoothctl_path = result.stdout.strip()
            print(f"✅ bluetoothctl可用，路径: {bluetoothctl_path}")
        else:
            print("❌ bluetoothctl命令不可用")
            return False
        
        # 测试bluetoothctl版本
        print("获取bluetoothctl版本信息...")
        result = subprocess.run(['adb', 'shell', 'bluetoothctl', '--version'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"bluetoothctl版本: {version}")
        else:
            print("无法获取bluetoothctl版本")
        
        return True
        
    except Exception as e:
        print(f"❌ 蓝牙功能可用性测试出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("新蓝牙测试功能验证")
    print("=" * 50)
    
    # 测试ADB连接
    if not test_adb_connection():
        print("\n❌ ADB连接失败，无法进行蓝牙测试")
        exit(1)
    
    # 测试蓝牙功能可用性
    if not test_bluetooth_availability():
        print("\n❌ 蓝牙功能不可用")
        exit(1)
    
    # 测试MAC地址解析功能
    test_mac_address_parsing()
    
    # 测试实际的bluetoothctl show命令
    success, mac_address = test_bluetooth_show_command()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 新蓝牙测试功能验证成功！")
        print(f"检测到的蓝牙控制器MAC地址: {mac_address}")
    else:
        print("❌ 新蓝牙测试功能验证失败")
    
    print("\n测试完成")
