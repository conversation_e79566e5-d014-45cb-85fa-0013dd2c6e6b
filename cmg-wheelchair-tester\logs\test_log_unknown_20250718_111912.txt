[2025-07-18 11:17:01] === 开始新的测试 ===
[2025-07-18 11:17:01] 开始测试
[2025-07-18 11:17:01] 工序: 右手臂功能测试
[2025-07-18 11:17:01] 序列号: None
[2025-07-18 11:17:01] 开始运行 右手臂功能测试 工序测试，共 17 个项目
[2025-07-18 11:17:01] 
开始执行: 设备连接状态检测
[2025-07-18 11:17:01] 设备连接测试通过，检测到 1 个设备
[2025-07-18 11:17:01] 设备: ?	device
[2025-07-18 11:17:02] 
开始执行: 3568版本测试
[2025-07-18 11:17:02] 执行3568版本测试...
[2025-07-18 11:17:02] 读取ROM版本号...
[2025-07-18 11:17:02] 执行命令: adb shell uname -a
[2025-07-18 11:17:02] uname命令执行成功
[2025-07-18 11:17:02] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 11:17:02] ✅ 3568版本测试成功
[2025-07-18 11:17:02] ROM版本号: Jul 9 12:01:12
[2025-07-18 11:17:02] ✅ 确认为RK3568平台
[2025-07-18 11:17:03] 
开始执行: USB关键器件检测
[2025-07-18 11:17:03] 执行USB设备检测...
[2025-07-18 11:17:03] 执行命令: adb shell lsusb
[2025-07-18 11:17:03] 设备返回数据:
[2025-07-18 11:17:03] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 11:17:03] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 11:17:03] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 11:17:03] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 11:17:03] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 11:17:03] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 11:17:03] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 11:17:03] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 11:17:03] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 11:17:03] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 11:17:03] 总共解析到 9 个设备
[2025-07-18 11:17:03] ✅ 所有预期的设备ID都已找到
[2025-07-18 11:17:03] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 11:17:03] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 11:17:03] ✅ USB设备检测通过
[2025-07-18 11:17:03] 
开始执行: CAN0测试
[2025-07-18 11:17:03] 执行CAN0测试流程...
[2025-07-18 11:17:03] 执行命令: adb shell ip link set can0 down
[2025-07-18 11:17:03] CAN0已关闭
[2025-07-18 11:17:03] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 11:17:03] CAN0已启动
[2025-07-18 11:17:03] CAN监听线程已启动...
[2025-07-18 11:17:04] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 11:17:04] CAN测试数据已发送，等待监听返回...
[2025-07-18 11:17:04] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 11:17:06] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 11:17:07] 
开始执行: GPS测试
[2025-07-18 11:17:07] 执行GPS测试...
[2025-07-18 11:17:07] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 11:17:17] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 11:17:17] 
开始执行: 4G模组测试
[2025-07-18 11:17:17] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 11:17:17] 监听线程已启动，等待串口数据...
[2025-07-18 11:17:19] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 11:17:20] AT+CCID指令已发送，等待串口返回...
[2025-07-18 11:17:20] 串口输出: AT+CCID
[2025-07-18 11:17:20] 串口输出: 
[2025-07-18 11:17:20] 串口输出: 
[2025-07-18 11:17:20] 串口输出: +CCID: 89860114851012535241
[2025-07-18 11:17:22] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-18 11:17:22] 
开始执行: 按键测试
[2025-07-18 11:17:22] 开始按键测试...
[2025-07-18 11:17:22] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 11:18:25] ⏰ 按键测试超时 - 1分钟内只检测到0个按键
[2025-07-18 11:18:27] 
开始执行: 按键灯测试
[2025-07-18 11:18:27] 开始LED背光灯测试...
[2025-07-18 11:18:27] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:18:27] LED控制命令执行成功
[2025-07-18 11:18:27] 🔧 显示确认对话框: LED测试确认
[2025-07-18 11:18:27] 🔧 对话框窗口已创建
[2025-07-18 11:18:27] 🔧 '是'按钮已创建
[2025-07-18 11:18:27] 🔧 '否'按钮已创建
[2025-07-18 11:18:27] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 11:18:28] 👤 用户选择: 是 (测试通过)
[2025-07-18 11:18:29] 🔧 对话框关闭，用户响应: yes
[2025-07-18 11:18:29] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:18:29] LED灯已关闭
[2025-07-18 11:18:29] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-18 11:18:29] 
开始执行: 手电筒测试
[2025-07-18 11:18:29] 开始手电筒LED测试...
[2025-07-18 11:18:29] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 11:18:29] 手电筒控制命令执行成功
[2025-07-18 11:18:29] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-18 11:18:29] 🔧 对话框窗口已创建
[2025-07-18 11:18:29] 🔧 '是'按钮已创建
[2025-07-18 11:18:29] 🔧 '否'按钮已创建
[2025-07-18 11:18:29] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 11:18:30] 👤 用户选择: 是 (测试通过)
[2025-07-18 11:18:30] 🔧 对话框关闭，用户响应: yes
[2025-07-18 11:18:30] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-18 11:18:31] 手电筒已关闭
[2025-07-18 11:18:31] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-18 11:18:31] 
开始执行: 摇杆使能测试
[2025-07-18 11:18:31] 执行摇杆测试...
[2025-07-18 11:18:31] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 11:18:31] 命令执行成功
[2025-07-18 11:18:31] 返回数据: 255
[2025-07-18 11:18:31] 摇杆测试通过，值: 255
[2025-07-18 11:18:32] 
开始执行: 前摄像头测试
[2025-07-18 11:18:32] 开始执行前摄像头测试...
[2025-07-18 11:18:32] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 11:18:32] 命令执行成功，返回: 无输出
[2025-07-18 11:18:32] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-18 11:18:33] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.314167359
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-18 11:18:33] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-18 11:18:33] 拉取命令执行成功
[2025-07-18 11:18:33] 返回数据: [ 38%] /data/camera/cam0_3840x2160.jpg
[ 77%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 19.9 MB/s (168821 bytes in 0.008s)
[2025-07-18 11:18:33] 图片已保存: cam0_3840x2160.jpg
[2025-07-18 11:18:34] 用户确认结果: 通过
[2025-07-18 11:18:35] 
开始执行: 光感测试
[2025-07-18 11:18:35] 执行光感测试...
[2025-07-18 11:18:35] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 11:18:38] 光感测试完成 - 检测到数值变化
[2025-07-18 11:18:38] 数值从 0 变化到 5
[2025-07-18 11:18:38] 
开始执行: 回充摄像头测试
[2025-07-18 11:18:38] 开始执行回充摄像头测试...
[2025-07-18 11:18:38] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 11:18:38] 命令执行成功，返回: 无输出
[2025-07-18 11:18:38] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-18 11:18:39] 命令执行成功，返回: 无输出
[2025-07-18 11:18:39] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-18 11:18:39] 拉取命令执行成功
[2025-07-18 11:18:39] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 11.7 MB/s (43288 bytes in 0.004s)
[2025-07-18 11:18:39] 图片已保存: output.jpg
[2025-07-18 11:18:41] 用户确认结果: 通过
[2025-07-18 11:18:41] 
开始执行: 喇叭测试
[2025-07-18 11:18:41] 执行喇叭测试...
[2025-07-18 11:18:41] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 11:18:55] 命令执行成功
[2025-07-18 11:18:55] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-18 11:18:55] 音频播放完成
[2025-07-18 11:18:56] 
开始执行: 蓝牙测试
[2025-07-18 11:18:56] 执行蓝牙测试...
[2025-07-18 11:18:56] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 11:18:56] 执行命令: adb shell bluetoothctl show
[2025-07-18 11:18:56] 命令执行成功
[2025-07-18 11:18:56] 返回数据:
[2025-07-18 11:18:56]   Controller 24:21:5E:C0:30:4A (public)
[2025-07-18 11:18:56]   Name: CMG-1
[2025-07-18 11:18:56]   Alias: CMG-1
[2025-07-18 11:18:56]   Class: 0x006c0000 (7077888)
[2025-07-18 11:18:56]   Powered: yes
[2025-07-18 11:18:56]   PowerState: on
[2025-07-18 11:18:56]   Discoverable: no
[2025-07-18 11:18:56]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-18 11:18:56]   Pairable: yes
[2025-07-18 11:18:56]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:18:56]   Modalias: usb:v1D6Bp0246d0544
[2025-07-18 11:18:56]   Discovering: no
[2025-07-18 11:18:56]   Roles: central
[2025-07-18 11:18:56]   Roles: peripheral
[2025-07-18 11:18:56]   Advertising Features:
[2025-07-18 11:18:56]   ActiveInstances: 0x00 (0)
[2025-07-18 11:18:56]   SupportedInstances: 0x10 (16)
[2025-07-18 11:18:56]   SupportedIncludes: tx-power
[2025-07-18 11:18:56]   SupportedIncludes: appearance
[2025-07-18 11:18:56]   SupportedIncludes: local-name
[2025-07-18 11:18:56]   SupportedSecondaryChannels: 1M
[2025-07-18 11:18:56]   SupportedSecondaryChannels: 2M
[2025-07-18 11:18:56]   SupportedSecondaryChannels: Coded
[2025-07-18 11:18:56]   SupportedCapabilities Key: MaxAdvLen
[2025-07-18 11:18:56]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 11:18:56]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-18 11:18:56]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 11:18:56]   SupportedFeatures: CanSetTxPower
[2025-07-18 11:18:56]   SupportedFeatures: HardwareOffload
[2025-07-18 11:18:56]   Advertisement Monitor Features:
[2025-07-18 11:18:56]   SupportedMonitorTypes: or_patterns
[2025-07-18 11:18:56] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-18 11:18:56] 蓝牙控制器MAC地址: 24:21:5E:C0:30:4A
[2025-07-18 11:18:56] 
开始执行: 4G网络测试
[2025-07-18 11:18:56] 执行4G网络测试...
[2025-07-18 11:18:56] 第一步：关闭WiFi网络...
[2025-07-18 11:18:56] WiFi已关闭，等待4G网络连接...
[2025-07-18 11:18:59] 第二步：使用4G网络进行ping测试...
[2025-07-18 11:18:59] ping目标: www.baidu.com
[2025-07-18 11:18:59] 测试时长: 10秒
[2025-07-18 11:19:09] ping测试输出:
[2025-07-18 11:19:09]   PING www.baidu.com(2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606)) 56 data bytes
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=1 ttl=52 time=185 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=2 ttl=52 time=1061 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=3 ttl=52 time=217 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=4 ttl=52 time=228 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=5 ttl=52 time=480 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=6 ttl=52 time=93.0 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=7 ttl=52 time=242 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=8 ttl=52 time=79.3 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=9 ttl=52 time=49.3 ms
[2025-07-18 11:19:09]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=10 ttl=52 time=78.9 ms
[2025-07-18 11:19:09]   --- www.baidu.com ping statistics ---
[2025-07-18 11:19:09]   10 packets transmitted, 10 received, 0% packet loss, time 9026ms
[2025-07-18 11:19:09]   rtt min/avg/max/mdev = 49.320/271.415/1061.252/289.107 ms, pipe 2
[2025-07-18 11:19:09] 4G网络延时统计:
[2025-07-18 11:19:09]   成功ping包: 10 个
[2025-07-18 11:19:09]   平均延时: 271.4 ms
[2025-07-18 11:19:09]   最小延时: 49.3 ms
[2025-07-18 11:19:09]   最大延时: 1061.0 ms
[2025-07-18 11:19:09] 4G网络测试通过
[2025-07-18 11:19:09] 第三步：重新打开WiFi网络...
[2025-07-18 11:19:09] WiFi已重新打开
[2025-07-18 11:19:11] 
开始执行: WiFi测试
[2025-07-18 11:19:11] 执行WiFi测试...
[2025-07-18 11:19:11] 第一步：关闭4G网络...
[2025-07-18 11:19:11] 4G网络已关闭，等待网络切换...
[2025-07-18 11:19:11] WiFi测试出错: cannot access local variable 'time' where it is not associated with a value
[2025-07-18 11:19:11] 第三步：重新打开4G网络...
[2025-07-18 11:19:11] 4G网络已重新打开
[2025-07-18 11:19:11] 重新打开4G网络时出错: cannot access local variable 'time' where it is not associated with a value
[2025-07-18 11:19:12] 
测试完成 - 通过率: 14/17
[2025-07-18 11:19:12] ❌ 存在测试失败项！

