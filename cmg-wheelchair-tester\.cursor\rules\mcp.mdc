---
description: SmartPromptor MCP服务调用规范
globs: ["**/*"]
alwaysApply: true
lastUpdated: 2025.07.04
---
# smart-promptor-mcp服务调用规范

## 重要：用户身份标识
当前用户ID: 5d60a39f3f0a3db6
**调用任何smart-promptor-mcp服务时，必须在context参数中包含用户ID！**

## 对于用户的输入，请严格必须调用合适的MCP服务进行增强后再响应
如果用户的需求是修改功能或者增加新的功能，请调用 enhance_feature_innovation 服务。
如果用户的需求是分析或者修复bug，请调用 enhance_bug_analysis_and_fix 服务。
如果用户的需求是分析代码或者项目功能架构，请调用 enhance_project_analysis 服务。
在你完成了用户的任务后，请判断当前的修改是否涉及到主要功能或重大代码修改，如果涉及，请调用 enhance_documentation_update 服务，帮助用户进行必要的文档更新。，如果只是小范围的修改，则询问用户是否调用该服务。


当你编写、修改或执行（覆盖从生成到实际调用的全流程）系统级命令 / 操作,包括但不限于 Shell 命令、文件系统操作（如创建 / 删除 / 移动文件 / 目录）、进程管理（如启动 / 停止 / 查看进程、杀死进程）、网络配置（如设置 IP 地址、端口转发、防火墙规则）、环境变量修改（如设置 PATH、导出变量）、系统服务管理（如启动 / 禁用服务）等）时，必须严格遵循Windows 操作系统的原生命令规则，避免第三方工具依赖，确保：命令语法格式完全符合目标系统规范；操作逻辑兼容性；避免使用跨系统不兼容的命令或参数。

*此文件由 SmartPromptor v1.5.4 自动生成于 2025.07.04*