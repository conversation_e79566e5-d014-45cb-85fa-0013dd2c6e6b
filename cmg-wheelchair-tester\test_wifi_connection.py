#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WiFi连接和延时测试功能
"""

import subprocess
import re
import time

def test_adb_connection():
    """测试ADB连接"""
    print("=== 测试ADB连接 ===")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        print(f"ADB devices返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("ADB连接正常")
            devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            connected_devices = [line for line in devices if 'device' in line]
            
            if connected_devices:
                print(f"已连接设备: {len(connected_devices)}个")
                for device in connected_devices:
                    print(f"  {device}")
                return True
            else:
                print("❌ 没有连接的设备")
                return False
        else:
            print(f"❌ ADB连接失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ ADB连接测试出错: {str(e)}")
        return False

def test_wifi_connection_commands():
    """测试WiFi连接命令序列"""
    print("\n=== 测试WiFi连接命令序列 ===")
    
    wifi_ssid = "Orion_SZ_5G"
    wifi_password = "Orion@2025"
    
    try:
        # 步骤1：停止现有的wpa_supplicant进程
        print("1. 停止现有的wpa_supplicant进程...")
        cmd1 = "killall wpa_supplicant 2>/dev/null"
        print(f"执行命令: adb shell \"{cmd1}\"")
        result1 = subprocess.run(['adb', 'shell', cmd1], capture_output=True, text=True, timeout=10)
        print(f"返回码: {result1.returncode}")
        
        # 步骤2：清理wpa_supplicant socket文件
        print("\n2. 清理wpa_supplicant socket文件...")
        cmd2 = "rm -f /var/run/wpa_supplicant/wlan0"
        print(f"执行命令: adb shell \"{cmd2}\"")
        result2 = subprocess.run(['adb', 'shell', cmd2], capture_output=True, text=True, timeout=10)
        print(f"返回码: {result2.returncode}")
        
        # 步骤3：关闭wlan0接口
        print("\n3. 关闭wlan0接口...")
        cmd3 = "ip link set wlan0 down"
        print(f"执行命令: adb shell {cmd3}")
        result3 = subprocess.run(['adb', 'shell', cmd3], capture_output=True, text=True, timeout=10)
        print(f"返回码: {result3.returncode}")
        
        # 步骤4：连接WiFi网络
        print("\n4. 连接WiFi网络...")
        wifi_cmd = (
            f"wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && "
            f"wpa_cli -i wlan0 add_network && "
            f"wpa_cli -i wlan0 set_network 0 ssid '\"'{wifi_ssid}'\"' && "
            f"wpa_cli -i wlan0 set_network 0 psk '\"'{wifi_password}'\"' && "
            f"wpa_cli -i wlan0 enable_network 0 && "
            f"udhcpc -i wlan0 && "
            f"iw wlan0 link && "
            f"ip addr show wlan0"
        )
        
        print(f"执行WiFi连接命令...")
        print(f"SSID: {wifi_ssid}")
        print("正在连接...")
        
        wifi_result = subprocess.run(['adb', 'shell', wifi_cmd], 
                                   capture_output=True, text=True, timeout=30)
        
        print(f"WiFi连接返回码: {wifi_result.returncode}")
        
        if wifi_result.returncode == 0:
            print("✅ WiFi连接命令执行成功")
            print("返回数据:")
            wifi_output = wifi_result.stdout.strip()
            if wifi_output:
                lines = wifi_output.split('\n')
                for line in lines:
                    if line.strip():
                        print(f"  {line.strip()}")
            
            # 检查连接状态
            if "Connected to" in wifi_output or "inet " in wifi_output:
                print("✅ WiFi连接成功")
                return True
            else:
                print("⚠️ WiFi连接状态不明确")
                return False
        else:
            print(f"❌ WiFi连接失败: {wifi_result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ WiFi连接测试出错: {str(e)}")
        return False

def test_network_delay():
    """测试网络延时"""
    print("\n=== 测试网络延时 ===")
    
    try:
        print("等待网络稳定...")
        time.sleep(3)
        
        # 执行ping测试
        ping_command = "ping -c 10 www.baidu.com"
        print(f"执行命令: adb shell {ping_command}")
        print("正在进行10秒钟的网络延时测试...")
        
        start_time = time.time()
        result = subprocess.run(['adb', 'shell', ping_command], 
                              capture_output=True, text=True, timeout=20)
        end_time = time.time()
        
        print(f"命令执行时间: {end_time - start_time:.2f} 秒")
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print("ping命令执行成功")
            print("返回数据:")
            
            # 分行显示输出
            lines = output.split('\n')
            for line in lines:
                if line.strip():
                    print(f"  {line.strip()}")
            
            # 解析延时数据
            time_pattern = re.compile(r'time=(\d+\.?\d*)\s*ms')
            delays = []
            
            print("\n解析延时数据:")
            for line in lines:
                if "64 bytes from" in line and "time=" in line:
                    match = time_pattern.search(line)
                    if match:
                        delay = float(match.group(1))
                        delays.append(delay)
                        print(f"  检测到延时: {delay} ms")
            
            if delays:
                # 计算平均延时
                avg_delay = sum(delays) / len(delays)
                min_delay = min(delays)
                max_delay = max(delays)
                
                print(f"\n✅ WiFi延时测试成功")
                print(f"发包数量: {len(delays)} 个")
                print(f"平均延时: {avg_delay:.2f} ms")
                print(f"最小延时: {min_delay:.2f} ms")
                print(f"最大延时: {max_delay:.2f} ms")
                
                # 判断网络质量
                if avg_delay <= 50:
                    quality = "优秀"
                elif avg_delay <= 100:
                    quality = "良好"
                elif avg_delay <= 200:
                    quality = "一般"
                else:
                    quality = "较差"
                
                print(f"网络质量: {quality}")
                print(f"结果显示: 平均延时: {avg_delay:.2f}ms ({quality})")
                return True, avg_delay
            else:
                print("❌ 未检测到有效的延时数据")
                return False, None
        else:
            print(f"❌ ping命令执行失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False, None
            
    except Exception as e:
        print(f"❌ 网络延时测试出错: {str(e)}")
        return False, None

def test_complete_wifi_flow():
    """测试完整的WiFi测试流程"""
    print("\n=== 测试完整的WiFi测试流程 ===")
    
    # 步骤1：连接WiFi
    wifi_success = test_wifi_connection_commands()
    
    if not wifi_success:
        print("❌ WiFi连接失败，跳过延时测试")
        return False
    
    # 步骤2：测试网络延时
    delay_success, avg_delay = test_network_delay()
    
    if delay_success:
        print(f"\n🎉 完整WiFi测试成功！")
        print(f"网络平均延时: {avg_delay:.2f} ms")
        return True
    else:
        print("\n❌ 网络延时测试失败")
        return False

if __name__ == "__main__":
    print("WiFi连接和延时测试功能验证")
    print("=" * 50)
    
    # 测试ADB连接
    if not test_adb_connection():
        print("\n❌ ADB连接失败，无法进行WiFi测试")
        exit(1)
    
    # 测试完整的WiFi流程
    success = test_complete_wifi_flow()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 WiFi连接和延时测试功能验证成功！")
    else:
        print("❌ WiFi连接和延时测试功能验证失败")
    
    print("\n测试完成")
