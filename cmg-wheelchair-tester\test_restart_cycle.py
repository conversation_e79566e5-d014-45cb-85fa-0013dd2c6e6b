#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重新开始测试循环功能
验证：
1. 测试完成后不显示成功/失败状态
2. 直接回到序列号输入弹窗
3. 可以连续测试多个设备
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}}

def center_dialog_on_parent(parent, dialog):
    """将对话框居中显示在父窗口上"""
    try:
        parent.update_idletasks()
        dialog.update_idletasks()
        
        root_x = parent.winfo_x()
        root_y = parent.winfo_y()
        root_width = parent.winfo_width()
        root_height = parent.winfo_height()
        
        dialog_width = dialog.winfo_width()
        dialog_height = dialog.winfo_height()
        
        x = root_x + (root_width - dialog_width) // 2
        y = root_y + (root_height - dialog_height) // 2
        
        screen_width = dialog.winfo_screenwidth()
        screen_height = dialog.winfo_screenheight()
        
        x = max(0, min(x, screen_width - dialog_width))
        y = max(0, min(y, screen_height - dialog_height))
        
        dialog.geometry(f"+{x}+{y}")
    except Exception as e:
        print(f"居中对话框时出错: {e}")

def test_restart_cycle():
    """测试重新开始测试循环"""
    print("=== 测试重新开始测试循环 ===")
    
    config = load_config()
    work_processes = config.get("work_processes", {})
    
    if not work_processes:
        print("❌ 没有找到工序配置")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title("测试循环重启功能")
    root.geometry("900x700")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 900) // 2
    y = (screen_height - 700) // 2
    root.geometry(f"900x700+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="测试循环重启功能演示", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 修改说明
    change_frame = ttk.LabelFrame(main_frame, text="功能修改", padding="15")
    change_frame.pack(fill=tk.X, pady=(0, 20))
    
    change_text = """🔧 测试完成后的行为修改:

修改前:
  • 测试完成后弹出测试完成对话框
  • 显示测试结果统计（成功/失败状态）
  • 显示失败项目详情
  • 提供三个选项：继续测试、更换工序、退出程序

修改后:
  ✅ 测试完成后不显示成功/失败状态
  ✅ 直接弹出序列号输入窗口
  ✅ 可以连续测试多个设备
  ✅ 简化操作流程，提高测试效率

🎯 新的测试流程:
  1. 选择工序 → 输入序列号 → 开始测试
  2. 测试完成 → 自动弹出序列号输入
  3. 输入新序列号 → 自动开始下一轮测试
  4. 循环进行，直到用户选择退出"""
    
    ttk.Label(change_frame, text=change_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 模拟测试区域
    test_frame = ttk.LabelFrame(main_frame, text="模拟测试循环", padding="15")
    test_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 状态显示
    status_var = tk.StringVar(value="准备开始测试循环...")
    status_label = ttk.Label(test_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack(pady=(0, 15))
    
    # 测试计数
    test_count_var = tk.StringVar(value="已完成测试: 0 个设备")
    count_label = ttk.Label(test_frame, textvariable=test_count_var, 
                           font=("Microsoft YaHei UI", 11), 
                           foreground="green")
    count_label.pack(pady=(0, 15))
    
    # 日志显示
    log_frame = ttk.Frame(test_frame)
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
    
    log_text = tk.Text(log_frame, height=15, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        log_text.insert(tk.END, log_entry)
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 测试状态
    test_state = {
        "selected_process": "整机半成品功能测试",
        "test_count": 0,
        "running": False
    }
    
    def show_serial_input_dialog(title="输入序列号", prompt="请输入设备序列号"):
        """显示序列号输入对话框"""
        dialog = tk.Toplevel(root)
        dialog.title(title)
        dialog.geometry("500x280")
        dialog.transient(root)
        dialog.grab_set()
        dialog.resizable(False, False)
        
        # 强制更新窗口尺寸信息
        root.update()
        dialog.update()
        
        # 等待窗口完全创建后居中
        root.after(10, lambda: center_dialog_on_parent(root, dialog))
        
        # 禁止关闭窗口
        dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        result = {"confirmed": False, "serial_number": ""}
        
        main_frame = ttk.Frame(dialog, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text=title, 
                               font=("Microsoft YaHei UI", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 当前工序信息
        process_info = f"当前工序: {test_state['selected_process']}"
        info_label = ttk.Label(main_frame, text=process_info, 
                              font=("Microsoft YaHei UI", 10), 
                              foreground="blue")
        info_label.pack(pady=(0, 20))
        
        # 序列号输入区域
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=(0, 25))
        input_frame.columnconfigure(1, weight=1)
        
        # 输入标签
        ttk.Label(input_frame, text="设备序列号:", 
                 font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)
        
        # 序列号输入框
        serial_var = tk.StringVar()
        # 自动生成序列号
        auto_serial = f"CMG202412{test_state['test_count']+1:04d}"
        serial_var.set(auto_serial)
        
        serial_entry = ttk.Entry(input_frame, textvariable=serial_var, 
                               font=("Microsoft YaHei UI", 11), width=20)
        serial_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
        serial_entry.focus()
        serial_entry.select_range(0, tk.END)  # 选中所有文本
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def confirm_serial():
            """确认序列号并开始测试"""
            serial_number = serial_var.get().strip()
            if serial_number:
                result["confirmed"] = True
                result["serial_number"] = serial_number
                dialog.destroy()
                
                # 开始测试
                start_test_simulation(serial_number)
            else:
                messagebox.showwarning("警告", "请输入序列号")
                serial_entry.focus()
        
        def cancel_serial():
            """取消输入"""
            result["confirmed"] = False
            dialog.destroy()
            log_message("用户取消测试，演示结束")
            status_var.set("演示结束")
        
        # 绑定回车键
        serial_entry.bind("<Return>", lambda e: confirm_serial())
        
        # 按钮区域居中布局
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)
        
        # 按钮布局：开始测试在左边，退出程序在右边
        start_btn = ttk.Button(button_container, text="开始测试", 
                             command=confirm_serial, 
                             style="Accent.TButton",
                             width=15)
        start_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        exit_btn = ttk.Button(button_container, text="退出程序", 
                            command=cancel_serial,
                            width=15)
        exit_btn.pack(side=tk.LEFT)
        
        # 等待对话框关闭
        dialog.wait_window()
        
        return result["confirmed"], result["serial_number"]
    
    def start_test_simulation(serial_number):
        """开始测试模拟"""
        test_state["running"] = True
        test_state["test_count"] += 1
        
        status_var.set(f"正在测试设备: {serial_number}")
        test_count_var.set(f"已完成测试: {test_state['test_count']-1} 个设备，当前测试第 {test_state['test_count']} 个")
        
        log_message("=== 开始新的测试 ===")
        log_message(f"工序: {test_state['selected_process']}")
        log_message(f"序列号: {serial_number}")
        
        def simulate_test_progress():
            test_items = [
                "设备连接检测",
                "版本信息测试", 
                "USB接口测试",
                "CAN通信测试",
                "GPS定位测试",
                "4G通信测试",
                "按键功能测试",
                "LED指示灯测试"
            ]
            
            for i, item in enumerate(test_items, 1):
                if not test_state["running"]:
                    break
                time.sleep(0.3)
                log_message(f"[{i}/{len(test_items)}] {item} - 通过 ✅")
                root.update_idletasks()
            
            if test_state["running"]:
                log_message(f"设备 {serial_number} 测试完成")
                test_count_var.set(f"已完成测试: {test_state['test_count']} 个设备")
                
                # 模拟测试完成后的重启循环
                root.after(1000, restart_test_cycle)
        
        threading.Thread(target=simulate_test_progress, daemon=True).start()
    
    def restart_test_cycle():
        """重新开始测试循环"""
        log_message("测试完成，准备开始下一轮测试")
        status_var.set("等待输入下一个设备序列号...")
        
        test_state["running"] = False
        
        # 直接弹出序列号输入窗口
        confirmed, serial_number = show_serial_input_dialog("输入新序列号", "请输入下一个设备的序列号")
        
        if not confirmed:
            log_message("测试循环结束")
            status_var.set("测试循环已结束")
    
    def start_demo():
        """开始演示"""
        log_message("开始测试循环演示")
        log_message(f"当前工序: {test_state['selected_process']}")
        
        # 显示第一个序列号输入对话框
        confirmed, serial_number = show_serial_input_dialog("输入序列号", "请输入第一个设备的序列号")
        
        if not confirmed:
            log_message("演示取消")
            status_var.set("演示已取消")
    
    def stop_demo():
        """停止演示"""
        test_state["running"] = False
        log_message("演示已停止")
        status_var.set("演示已停止")
    
    def reset_demo():
        """重置演示"""
        test_state["running"] = False
        test_state["test_count"] = 0
        log_text.delete(1.0, tk.END)
        status_var.set("准备开始测试循环...")
        test_count_var.set("已完成测试: 0 个设备")
        log_message("演示已重置")
    
    # 控制按钮
    button_frame = ttk.Frame(test_frame)
    button_frame.pack(fill=tk.X)
    
    ttk.Button(button_frame, text="开始演示", 
              command=start_demo, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="停止演示", 
              command=stop_demo, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="重置演示", 
              command=reset_demo, width=12).pack(side=tk.LEFT, padx=5)
    
    # 流程说明
    flow_frame = ttk.LabelFrame(main_frame, text="新的测试流程", padding="10")
    flow_frame.pack(fill=tk.X)
    
    flow_text = """🔄 连续测试流程:

1️⃣ 输入第一个设备序列号 → 开始测试
2️⃣ 测试完成 → 自动弹出序列号输入窗口
3️⃣ 输入第二个设备序列号 → 自动开始测试
4️⃣ 测试完成 → 自动弹出序列号输入窗口
5️⃣ 继续循环，直到用户选择退出

✨ 优势:
  • 无需显示测试结果状态，节省时间
  • 自动循环，提高测试效率
  • 操作简单，适合批量测试
  • 减少用户操作步骤"""
    
    ttk.Label(flow_frame, text=flow_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 9)).pack(fill=tk.X)
    
    # 初始化日志
    log_message("测试循环重启功能演示已准备就绪")
    log_message("点击'开始演示'体验新的测试流程")
    
    print("测试循环重启功能演示界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("测试循环重启功能演示")
    print("=" * 50)
    
    print("🔧 功能修改:")
    print("  1. 测试完成后不显示成功/失败状态")
    print("  2. 直接回到序列号输入弹窗")
    print("  3. 可以连续测试多个设备")
    print("  4. 简化操作流程，提高效率")
    
    # 创建测试界面
    test_restart_cycle()
    
    print("\n演示完成")
