[2025-07-11 16:05:13] 开始测试 - SN: 111111111111111111
[2025-07-11 16:05:13] 
开始执行: 设备连接状态检测
[2025-07-11 16:05:13] 设备连接测试通过，检测到 1 个设备
[2025-07-11 16:05:13] 设备: ?	device
[2025-07-11 16:05:14] 
开始执行: USB关键器件检测
[2025-07-11 16:05:14] 执行USB设备检测...
[2025-07-11 16:05:14] 执行命令: adb shell lsusb
[2025-07-11 16:05:14] 设备返回数据:
[2025-07-11 16:05:14] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-11 16:05:14] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-11 16:05:14] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-11 16:05:14] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-11 16:05:14] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-11 16:05:14] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-11 16:05:14] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-11 16:05:14] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-11 16:05:14] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-11 16:05:14] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-11 16:05:14] 总共解析到 9 个设备
[2025-07-11 16:05:14] ✅ 所有预期的设备ID都已找到
[2025-07-11 16:05:14] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 16:05:14] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 16:05:14] ✅ USB设备检测通过
[2025-07-11 16:05:15] 
开始执行: CAN0测试
[2025-07-11 16:05:15] 执行CAN0测试流程...
[2025-07-11 16:05:15] 执行命令: adb shell ip link set can0 down
[2025-07-11 16:05:15] CAN0已关闭
[2025-07-11 16:05:15] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-11 16:05:15] CAN0已启动
[2025-07-11 16:05:15] CAN监听线程已启动...
[2025-07-11 16:05:16] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-11 16:05:16] CAN测试数据已发送，等待监听返回...
[2025-07-11 16:05:16] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-11 16:05:18] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-11 16:05:18] 
开始执行: GPS测试
[2025-07-11 16:05:18] 执行GPS测试...
[2025-07-11 16:05:18] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-11 16:05:28] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-11 16:05:29] 
开始执行: 4G模组测试
[2025-07-11 16:05:29] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-11 16:05:29] 监听线程已启动，等待串口数据...
[2025-07-11 16:05:31] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-11 16:05:31] AT+CCID指令已发送，等待串口返回...
[2025-07-11 16:05:31] 串口输出: AT+CCID
[2025-07-11 16:05:31] 串口输出: 
[2025-07-11 16:05:31] 串口输出: 
[2025-07-11 16:05:31] 串口输出: +CCID: 89860114851012535241
[2025-07-11 16:05:33] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-11 16:05:35] 
开始执行: 按键测试
[2025-07-11 16:05:35] 开始按键测试...
[2025-07-11 16:05:36] 执行命令: adb shell evtest /dev/input/event5
[2025-07-11 16:05:57] 原始事件: Event: time 1752221158.879083, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 16:05:57] 解析结果: key_code=662, value=1
[2025-07-11 16:05:57] ✓ 检测到档位减(662)按下
[2025-07-11 16:05:57] 原始事件: Event: time 1752221159.055864, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 16:05:57] 解析结果: key_code=662, value=0
[2025-07-11 16:05:57] ✓ 档位减(662)测试通过
[2025-07-11 16:05:58] 原始事件: Event: time 1752221159.759062, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-11 16:05:58] 解析结果: key_code=658, value=1
[2025-07-11 16:05:58] ✓ 检测到喇叭键(658)按下
[2025-07-11 16:05:58] 原始事件: Event: time 1752221159.929125, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-11 16:05:58] 解析结果: key_code=658, value=0
[2025-07-11 16:05:58] ✓ 喇叭键(658)测试通过
[2025-07-11 16:06:01] 原始事件: Event: time 1752221162.955738, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-11 16:06:01] 解析结果: key_code=661, value=1
[2025-07-11 16:06:01] ✓ 检测到静音键(661)按下
[2025-07-11 16:06:01] 原始事件: Event: time 1752221163.129013, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-11 16:06:01] 解析结果: key_code=661, value=0
[2025-07-11 16:06:01] ✓ 静音键(661)测试通过
[2025-07-11 16:06:02] 原始事件: Event: time 1752221163.979094, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 16:06:02] 解析结果: key_code=663, value=1
[2025-07-11 16:06:02] ✓ 检测到语音键(663)按下
[2025-07-11 16:06:02] 原始事件: Event: time 1752221164.155742, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 16:06:02] 解析结果: key_code=663, value=0
[2025-07-11 16:06:02] ✓ 语音键(663)测试通过
[2025-07-11 16:06:03] 原始事件: Event: time 1752221164.705879, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-11 16:06:03] 解析结果: key_code=661, value=1
[2025-07-11 16:06:03] ✓ 检测到静音键(661)按下
[2025-07-11 16:06:03] 原始事件: Event: time 1752221164.729104, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 16:06:03] 解析结果: key_code=657, value=1
[2025-07-11 16:06:03] ✓ 检测到SOS键(657)按下
[2025-07-11 16:06:03] 原始事件: Event: time 1752221164.905781, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-11 16:06:03] 解析结果: key_code=661, value=0
[2025-07-11 16:06:03] ✓ 静音键(661)测试通过
[2025-07-11 16:06:03] 原始事件: Event: time 1752221164.955744, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 16:06:03] 解析结果: key_code=657, value=0
[2025-07-11 16:06:03] ✓ SOS键(657)测试通过
[2025-07-11 16:06:06] 原始事件: Event: time 1752221167.629201, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-11 16:06:06] 解析结果: key_code=659, value=1
[2025-07-11 16:06:06] ✓ 检测到档位加(659)按下
[2025-07-11 16:06:06] 原始事件: Event: time 1752221167.829072, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-11 16:06:06] 解析结果: key_code=659, value=0
[2025-07-11 16:06:06] ✓ 档位加(659)测试通过
[2025-07-11 16:06:08] 原始事件: Event: time 1752221169.855883, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 16:06:08] 解析结果: key_code=656, value=1
[2025-07-11 16:06:08] ✓ 检测到锁定键(656)按下
[2025-07-11 16:06:08] 原始事件: Event: time 1752221170.082472, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 16:06:08] 解析结果: key_code=656, value=0
[2025-07-11 16:06:08] ✓ 锁定键(656)测试通过
[2025-07-11 16:06:10] 原始事件: Event: time 1752221171.229210, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 16:06:10] 解析结果: key_code=660, value=1
[2025-07-11 16:06:10] ✓ 检测到智驾键(660)按下
[2025-07-11 16:06:10] 原始事件: Event: time 1752221171.505787, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 16:06:10] 解析结果: key_code=660, value=0
[2025-07-11 16:06:10] ✓ 智驾键(660)测试通过
[2025-07-11 16:06:10] 按键测试完成 - 检测到8个按键
[2025-07-11 16:06:13] 
开始执行: 按键灯测试
[2025-07-11 16:06:13] 开始LED背光灯测试...
[2025-07-11 16:06:13] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-11 16:06:13] LED控制命令执行成功
[2025-07-11 16:06:16] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-11 16:06:16] LED灯已关闭
[2025-07-11 16:06:16] LED测试通过 - 背光灯正常点亮
[2025-07-11 16:06:16] 
开始执行: 手电筒测试
[2025-07-11 16:06:16] 开始手电筒LED测试...
[2025-07-11 16:06:16] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-11 16:06:16] 手电筒控制命令执行成功
[2025-07-11 16:06:17] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-11 16:06:17] 手电筒已关闭
[2025-07-11 16:06:17] 手电筒测试通过 - 手电筒正常点亮
[2025-07-11 16:06:18] 
开始执行: 摇杆使能测试
[2025-07-11 16:06:18] 执行摇杆测试...
[2025-07-11 16:06:18] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-11 16:06:18] 命令执行成功
[2025-07-11 16:06:18] 返回数据: 255
[2025-07-11 16:06:18] 摇杆测试通过，值: 255
[2025-07-11 16:06:18] 
开始执行: 前摄像头测试
[2025-07-11 16:06:18] 开始执行前摄像头测试...
[2025-07-11 16:06:18] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-11 16:06:18] 命令执行成功，返回: 无输出
[2025-07-11 16:06:18] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-11 16:06:19] 命令执行失败: ERROR: from element /GstPipeline:pipeline0/GstV4l2Src:v4l2src0: Internal data stream error.
Additional debug info:
../libs/gst/base/gstbasesrc.c(3132): gst_base_src_loop (): /GstPipeline:pipeline0/GstV4l2Src:v4l2src0:
streaming stopped, reason not-negotiated (-4)

[2025-07-11 16:06:20] 
开始执行: 光感测试
[2025-07-11 16:06:20] 执行光感测试...
[2025-07-11 16:06:20] 执行命令: adb shell evtest /dev/input/event1
[2025-07-11 16:06:30] 光感测试完成 - 检测到数值变化
[2025-07-11 16:06:30] 数值从 3 变化到 4
[2025-07-11 16:06:31] 
开始执行: 回充摄像头测试
[2025-07-11 16:06:31] 开始执行回充摄像头测试...
[2025-07-11 16:06:31] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-11 16:06:31] 命令执行成功，返回: 无输出
[2025-07-11 16:06:31] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-11 16:06:32] 命令执行成功，返回: 无输出
[2025-07-11 16:06:32] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-11 16:06:32] 拉取命令执行成功
[2025-07-11 16:06:32] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 13.1 MB/s (48312 bytes in 0.004s)
[2025-07-11 16:06:32] 图片已保存: output.jpg
[2025-07-11 16:06:34] 用户确认结果: 通过
[2025-07-11 16:06:34] 
开始执行: 喇叭测试
[2025-07-11 16:06:34] 执行喇叭测试...
[2025-07-11 16:06:34] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-11 16:06:48] 命令执行成功
[2025-07-11 16:06:48] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-11 16:06:48] 音频播放完成
[2025-07-11 16:06:49] 
开始执行: 蓝牙测试
[2025-07-11 16:06:49] 执行蓝牙测试...
[2025-07-11 16:06:49] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-11 16:06:49] 执行命令: adb shell bluetoothctl show
[2025-07-11 16:06:49] 命令执行成功
[2025-07-11 16:06:49] 返回数据:
[2025-07-11 16:06:49]   Controller 24:21:5E:C0:2F:D9 (public)
[2025-07-11 16:06:49]   Name: CMG-1
[2025-07-11 16:06:49]   Alias: CMG-1
[2025-07-11 16:06:49]   Class: 0x006c0000 (7077888)
[2025-07-11 16:06:49]   Powered: yes
[2025-07-11 16:06:49]   PowerState: on
[2025-07-11 16:06:49]   Discoverable: no
[2025-07-11 16:06:49]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-11 16:06:49]   Pairable: yes
[2025-07-11 16:06:49]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:06:49]   Modalias: usb:v1D6Bp0246d0544
[2025-07-11 16:06:49]   Discovering: no
[2025-07-11 16:06:49]   Roles: central
[2025-07-11 16:06:49]   Roles: peripheral
[2025-07-11 16:06:49]   Advertising Features:
[2025-07-11 16:06:49]   ActiveInstances: 0x00 (0)
[2025-07-11 16:06:49]   SupportedInstances: 0x10 (16)
[2025-07-11 16:06:49]   SupportedIncludes: tx-power
[2025-07-11 16:06:49]   SupportedIncludes: appearance
[2025-07-11 16:06:49]   SupportedIncludes: local-name
[2025-07-11 16:06:49]   SupportedSecondaryChannels: 1M
[2025-07-11 16:06:49]   SupportedSecondaryChannels: 2M
[2025-07-11 16:06:49]   SupportedSecondaryChannels: Coded
[2025-07-11 16:06:49]   SupportedCapabilities Key: MaxAdvLen
[2025-07-11 16:06:49]   SupportedCapabilities Value: 0x1f (31)
[2025-07-11 16:06:49]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-11 16:06:49]   SupportedCapabilities Value: 0x1f (31)
[2025-07-11 16:06:49]   SupportedFeatures: CanSetTxPower
[2025-07-11 16:06:49]   SupportedFeatures: HardwareOffload
[2025-07-11 16:06:49]   Advertisement Monitor Features:
[2025-07-11 16:06:49]   SupportedMonitorTypes: or_patterns
[2025-07-11 16:06:49] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-11 16:06:49] 蓝牙控制器MAC地址: 24:21:5E:C0:2F:D9
[2025-07-11 16:06:49] 
开始执行: WiFi测试
[2025-07-11 16:06:49] 执行WiFi测试...
[2025-07-11 16:06:49] 开始网络发包延时测试...
[2025-07-11 16:06:49] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-11 16:06:49] 正在进行10秒钟的网络延时测试...
[2025-07-11 16:06:59] ping命令执行成功
[2025-07-11 16:06:59] 返回数据:
[2025-07-11 16:06:59]   PING www.baidu.com(2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606)) 56 data bytes
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=1 ttl=52 time=79.7 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=2 ttl=52 time=45.5 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=3 ttl=52 time=38.6 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=4 ttl=52 time=41.7 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=5 ttl=52 time=40.7 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=6 ttl=52 time=42.5 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=7 ttl=52 time=37.9 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=8 ttl=52 time=40.6 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=9 ttl=52 time=36.9 ms
[2025-07-11 16:06:59]   64 bytes from 2408:8756:c52:1a18:0:ff:b030:7606 (2408:8756:c52:1a18:0:ff:b030:7606): icmp_seq=10 ttl=52 time=38.5 ms
[2025-07-11 16:06:59]   --- www.baidu.com ping statistics ---
[2025-07-11 16:06:59]   10 packets transmitted, 10 received, 0% packet loss, time 9001ms
[2025-07-11 16:06:59]   rtt min/avg/max/mdev = 36.887/44.256/79.668/12.044 ms
[2025-07-11 16:06:59] 检测到延时: 79.7 ms
[2025-07-11 16:06:59] 检测到延时: 45.5 ms
[2025-07-11 16:06:59] 检测到延时: 38.6 ms
[2025-07-11 16:06:59] 检测到延时: 41.7 ms
[2025-07-11 16:06:59] 检测到延时: 40.7 ms
[2025-07-11 16:06:59] 检测到延时: 42.5 ms
[2025-07-11 16:06:59] 检测到延时: 37.9 ms
[2025-07-11 16:06:59] 检测到延时: 40.6 ms
[2025-07-11 16:06:59] 检测到延时: 36.9 ms
[2025-07-11 16:06:59] 检测到延时: 38.5 ms
[2025-07-11 16:06:59] ✅ WiFi延时测试成功
[2025-07-11 16:06:59] 发包数量: 10 个
[2025-07-11 16:06:59] 平均延时: 44.26 ms
[2025-07-11 16:06:59] 最小延时: 36.90 ms
[2025-07-11 16:06:59] 最大延时: 79.70 ms
[2025-07-11 16:06:59] 
测试完成 - 通过率: 13/15
[2025-07-11 16:06:59] ❌ 存在测试失败项！
[2025-07-11 16:06:59] 测试记录已保存: records/111111111111111111_20250711_160659.json
[2025-07-11 16:06:59] 测试日志已保存: records/111111111111111111_20250711_160659.log

