[2025-07-05 14:18:13] 开始测试设备: 11111
[2025-07-05 14:18:13] 测试数据已清除
[2025-07-05 14:18:13] 开始执行全部测试...
[2025-07-05 14:18:13] 开始测试: USB关键器件测试
[2025-07-05 14:18:13] 执行USB设备检测...
[2025-07-05 14:18:13] 执行命令: adb shell lsusb
[2025-07-05 14:18:13] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:14] 开始测试: CAN0测试
[2025-07-05 14:18:14] 执行CAN0测试流程...
[2025-07-05 14:18:14] 执行命令: adb shell ip link set can0 down
[2025-07-05 14:18:14] CAN0 down失败: error: no devices/emulators found

[2025-07-05 14:18:15] 开始测试: GPS测试
[2025-07-05 14:18:15] 执行GPS测试...
[2025-07-05 14:18:15] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-05 14:18:15] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:15] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-05 14:18:15] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:15] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-05 14:18:15] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:15] GPS信号检测失败
[2025-07-05 14:18:16] 开始测试: 4G模组测试
[2025-07-05 14:18:16] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-05 14:18:16] 监听线程已启动，等待串口数据...
[2025-07-05 14:18:18] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-05 14:18:18] AT指令发送失败: error: no devices/emulators found

[2025-07-05 14:18:19] 开始测试: 按键测试
[2025-07-05 14:18:19] 执行按键测试...
[2025-07-05 14:18:19] 请按手柄按键进行测试...
[2025-07-05 14:18:20] 开始测试: 按键灯测试
[2025-07-05 14:18:20] 开始LED背光灯测试...
[2025-07-05 14:18:20] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-05 14:18:20] LED控制命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:21] 开始测试: 手电筒测试
[2025-07-05 14:18:21] 开始手电筒LED测试...
[2025-07-05 14:18:21] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-05 14:18:21] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:22] 开始测试: 摇杆使能测试
[2025-07-05 14:18:22] 执行摇杆测试...
[2025-07-05 14:18:22] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-05 14:18:22] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:23] 开始测试: 前摄像头测试
[2025-07-05 14:18:23] 开始执行前摄像头测试...
[2025-07-05 14:18:24] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-05 14:18:24] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:25] 开始测试: 光感测试
[2025-07-05 14:18:25] 执行光感测试...
[2025-07-05 14:18:25] 执行命令: adb shell evtest /dev/input/event1
[2025-07-05 14:18:25] 光感测试失败 - 未检测到数值变化
[2025-07-05 14:18:26] 开始测试: 回充摄像头测试
[2025-07-05 14:18:26] 开始执行回充摄像头测试...
[2025-07-05 14:18:26] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-05 14:18:26] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:27] 开始测试: 喇叭测试
[2025-07-05 14:18:27] 执行喇叭测试...
[2025-07-05 14:18:27] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-05 14:18:27] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:28] 开始测试: 蓝牙测试
[2025-07-05 14:18:28] 执行蓝牙测试...
[2025-07-05 14:18:28] 启动蓝牙服务...
[2025-07-05 14:18:28] 启动蓝牙服务失败: error: no devices/emulators found

[2025-07-05 14:18:28] 开启蓝牙...
[2025-07-05 14:18:28] 开启蓝牙失败: error: no devices/emulators found

[2025-07-05 14:18:28] 执行命令: adb shell bluetoothctl devices
[2025-07-05 14:18:28] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:29] 开始测试: WiFi测试
[2025-07-05 14:18:30] 执行WiFi测试...
[2025-07-05 14:18:30] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-05 14:18:30] 命令执行失败: error: no devices/emulators found

[2025-07-05 14:18:31] 全部测试完成
[2025-07-05 14:18:31] 测试记录已保存: records/11111_20250705_141831.json

