#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试双击事件问题
创建一个简单的测试来验证双击事件是否正常工作
"""

import tkinter as tk
from tkinter import ttk
import json

def test_double_click_debug():
    """调试双击事件"""
    print("=== 调试双击事件 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("双击事件调试")
    root.geometry("800x600")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="双击事件调试测试", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 说明
    info_text = """调试说明:
1. 这个测试用来验证双击事件是否正常工作
2. 尝试双击下面的测试项目
3. 观察控制台和日志输出
4. 如果双击不工作，尝试右键菜单"""
    
    ttk.Label(main_frame, text=info_text, justify=tk.LEFT).pack(pady=(0, 20))
    
    # 创建测试项目树（与主程序相同的配置）
    test_frame = ttk.Frame(main_frame)
    test_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    columns = ("data", "result")
    test_tree = ttk.Treeview(
        test_frame,
        columns=columns,
        show="tree headings",
        selectmode="browse",  # 单选模式
        height=10
    )
    
    test_tree.heading("#0", text="测试项目")
    test_tree.heading("data", text="测试数据")
    test_tree.heading("result", text="测试结果")
    test_tree.column("#0", width=250, minwidth=250)
    test_tree.column("data", width=200, minwidth=200)
    test_tree.column("result", width=100, minwidth=100, anchor="center")
    test_tree.pack(fill=tk.BOTH, expand=True)
    
    # 日志区域
    log_frame = ttk.LabelFrame(main_frame, text="事件日志", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True)
    
    log_text = tk.Text(log_frame, height=8, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S.%f")[:-3]
        log_entry = f"[{timestamp}] {message}\n"
        log_text.insert(tk.END, log_entry)
        log_text.see(tk.END)
        root.update_idletasks()
        print(message)  # 同时输出到控制台
    
    # 添加测试项目
    test_items = [
        "设备连接状态检测",
        "3568版本测试", 
        "USB关键器件检测",
        "CAN0测试",
        "GPS测试"
    ]
    
    for i, item_name in enumerate(test_items):
        item = test_tree.insert("", "end", text=item_name)
        test_tree.set(item, "data", f"测试数据{i+1}")
        test_tree.set(item, "result", "")
    
    # 事件处理函数
    def on_single_click(event):
        """单击事件"""
        log_message("🖱️ 单击事件触发")
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"   选中项目: {test_name}")
    
    def on_double_click(event):
        """双击事件"""
        log_message("🖱️🖱️ 双击事件触发！")
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"   双击项目: {test_name}")
            
            # 模拟测试执行
            test_tree.set(item[0], "result", "测试中")
            test_tree.set(item[0], "data", "正在测试...")
            
            def finish_test():
                test_tree.set(item[0], "result", "PASS")
                test_tree.set(item[0], "data", "测试通过")
                log_message(f"✅ {test_name} - 测试完成")
            
            root.after(2000, finish_test)
        else:
            log_message("   ❌ 没有选中的项目")
    
    def on_button_press(event):
        """按钮按下事件"""
        log_message("🖱️⬇️ 按钮按下事件")
    
    def on_button_release(event):
        """按钮释放事件"""
        log_message("🖱️⬆️ 按钮释放事件")
    
    def show_test_menu(event):
        """显示右键菜单"""
        log_message("🖱️➡️ 右键菜单事件触发")
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"   右键项目: {test_name}")
            test_menu.post(event.x_root, event.y_root)
    
    def run_menu_test():
        """从菜单运行测试"""
        log_message("📋 菜单测试触发")
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            log_message(f"   菜单测试项目: {test_name}")
            on_double_click(None)  # 复用双击逻辑
    
    def show_test_details():
        """显示测试详情"""
        log_message("📋 查看详情触发")
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            from tkinter import messagebox
            messagebox.showinfo("测试详情", f"测试项目: {test_name}")
    
    # 创建右键菜单
    test_menu = tk.Menu(root, tearoff=0)
    test_menu.add_command(label="运行测试", command=run_menu_test)
    test_menu.add_command(label="查看详情", command=show_test_details)
    
    # 绑定所有相关事件
    log_message("绑定事件...")
    
    test_tree.bind("<Button-1>", on_button_press)
    test_tree.bind("<ButtonRelease-1>", on_button_release)
    test_tree.bind("<<TreeviewSelect>>", on_single_click)
    test_tree.bind("<Double-Button-1>", on_double_click)
    test_tree.bind("<Button-3>", show_test_menu)
    
    log_message("事件绑定完成")
    
    # 测试按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(10, 0))
    
    def test_selection():
        """测试选择功能"""
        items = test_tree.get_children()
        if items:
            test_tree.selection_set(items[0])
            test_tree.focus(items[0])
            log_message("✅ 已选择第一个项目")
    
    def simulate_double_click():
        """模拟双击事件"""
        log_message("🔧 模拟双击事件")
        on_double_click(None)
    
    def clear_log():
        """清空日志"""
        log_text.delete(1.0, tk.END)
        log_message("日志已清空")
    
    def test_event_binding():
        """测试事件绑定"""
        log_message("🔧 测试事件绑定...")
        bindings = test_tree.bind()
        log_message(f"当前绑定的事件: {bindings}")
        
        # 检查特定事件
        double_click_binding = test_tree.bind("<Double-Button-1>")
        log_message(f"双击事件绑定: {double_click_binding}")
    
    ttk.Button(button_frame, text="选择第一项", command=test_selection).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="模拟双击", command=simulate_double_click).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="检查绑定", command=test_event_binding).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="清空日志", command=clear_log).pack(side=tk.RIGHT, padx=5)
    
    # 初始化日志
    log_message("双击事件调试测试已准备就绪")
    log_message("请尝试以下操作:")
    log_message("1. 单击选择测试项目")
    log_message("2. 双击测试项目")
    log_message("3. 右键点击显示菜单")
    log_message("4. 观察事件日志输出")
    
    # 自动选择第一项
    root.after(1000, test_selection)
    
    print("双击事件调试窗口已创建")
    print("请在窗口中进行测试，观察控制台和日志输出")
    
    root.mainloop()

if __name__ == "__main__":
    print("双击事件调试测试")
    print("=" * 50)
    
    test_double_click_debug()
    
    print("\n调试测试完成")
