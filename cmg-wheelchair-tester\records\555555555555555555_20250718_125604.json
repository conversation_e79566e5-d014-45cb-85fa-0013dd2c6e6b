{"sn": "555555555555555555", "timestamp": "2025-07-18T12:56:04.739326", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-18T12:54:23.301467"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-18T12:54:23.740951"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-18T12:54:24.600777"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-18T12:54:25.213555"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-18T12:54:29.008142"}, "4g_test": {"test_name": "4G模组版本测试", "status": "通过", "message": "EG912UGLAAR03A15M08", "timestamp": "2025-07-18T12:54:39.810792"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-18T12:54:44.663047"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-18T12:55:05.682838"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-18T12:55:08.088826"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-18T12:55:10.019742"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T12:55:10.539640"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-18T12:55:13.624664"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T12:55:19.628800"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-18T12:55:23.760917"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 24:21:5E:C0:30:4A", "timestamp": "2025-07-18T12:55:38.358714"}, "4g_network_test": {"test_name": "4G网络测试", "status": "失败", "message": "4G网络不通", "timestamp": "2025-07-18T12:55:38.940048"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "平均延时: 10.78ms (优秀)", "timestamp": "2025-07-18T12:55:44.879687"}}}