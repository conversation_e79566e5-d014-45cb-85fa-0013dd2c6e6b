import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import paramiko
import time
import os
from pathlib import Path
import threading
import socket
import subprocess

class ElevatorUpgradeGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("梯控升级工具")
        
        # 设置统一字体大小
        self.default_font = ('Microsoft YaHei UI', 12)  # 统一字体和大小
        
        # 配置默认字体
        style = ttk.Style()
        style.configure('TLabelframe', font=self.default_font)
        style.configure('TLabelframe.Label', font=self.default_font)
        style.configure('TLabel', font=self.default_font)
        style.configure('TButton', font=self.default_font)
        style.configure('TEntry', font=self.default_font)
        
        # 设置固定窗口大小
        window_width = 600
        window_height = 600  # 增加窗口高度
        self.root.geometry(f"{window_width}x{window_height}")
        self.root.resizable(False, False)  # 禁止调整窗口大小
        
        # 创建升级实例
        self.upgrade = None
        self.is_connected = False
        self.version_animation_id = None  # 用于存储动画ID
        
        self.create_widgets()
        # 启动自动连接
        self.auto_connect()
        
    def create_widgets(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 连接状态框架
        status_frame = ttk.LabelFrame(main_frame, text="连接状态", padding="5")
        status_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.status_label = ttk.Label(status_frame, text="正在连接设备...")
        self.status_label.grid(row=0, column=0, padx=5)
        
        self.connect_button = ttk.Button(status_frame, text="断开连接", command=self.disconnect_device, state=tk.DISABLED)
        self.connect_button.grid(row=0, column=1, padx=5)
        
        # 版本信息框架
        version_frame = ttk.LabelFrame(main_frame, text="版本信息", padding="5")
        version_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.version_label = ttk.Label(version_frame, text="当前版本: 未连接")
        self.version_label.grid(row=0, column=0, padx=5, pady=5)
        
        # 文件选择框架
        file_frame = ttk.LabelFrame(main_frame, text="升级文件", padding="5")
        file_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        self.file_path = tk.StringVar()
        self.file_entry = ttk.Entry(file_frame, textvariable=self.file_path, width=35)
        self.file_entry.grid(row=0, column=0, padx=5, pady=5, sticky=(tk.W, tk.E))
        
        self.browse_button = ttk.Button(file_frame, text="浏览", command=self.browse_file)
        self.browse_button.grid(row=0, column=1, padx=5)
        
        self.upgrade_button = ttk.Button(file_frame, text="开始升级", command=self.start_upgrade, state=tk.DISABLED)
        self.upgrade_button.grid(row=0, column=2, padx=5)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, length=580, mode='determinate')
        self.progress.grid(row=3, column=0, columnspan=2, pady=5, sticky=(tk.W, tk.E))
        
        # 日志框架
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        
        # 创建日志文本框的滚动条
        scrollbar = ttk.Scrollbar(log_frame)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        self.log_text = tk.Text(log_frame, height=12, width=50, font=self.default_font, yscrollcommand=scrollbar.set)
        self.log_text.grid(row=0, column=0, padx=5, pady=5, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.config(command=self.log_text.yview)
        
        # 配置grid权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        file_frame.columnconfigure(0, weight=1)  # 让文件输入框可以扩展
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)  # 让日志框架可以垂直扩展
        
    def check_host_available(self, host, port=22, timeout=1):
        """检查主机是否可访问"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(timeout)
            result = sock.connect_ex((host, port))
            sock.close()
            return result == 0
        except:
            return False

    def wait_for_host(self, host, port=22, timeout=300, interval=2):
        """等待主机可访问"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            if self.check_host_available(host, port):
                return True
            time.sleep(interval)
        return False

    def log(self, message):
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        
    def animate_version_update(self, new_version, count=0):
        """版本更新动画效果"""
        if count >= 6:  # 动画执行3次（6次颜色变化）
            self.version_label.config(text=f"当前版本: {new_version}", foreground='black')
            return
            
        # 交替改变颜色
        color = 'red' if count % 2 == 0 else 'black'
        self.version_label.config(text=f"当前版本: {new_version}", foreground=color)
        
        # 安排下一次颜色变化
        self.version_animation_id = self.root.after(500, lambda: self.animate_version_update(new_version, count + 1))

    def update_version_display(self, version):
        """更新版本显示（带动画效果）"""
        if self.version_animation_id:  # 如果已有动画在运行，先取消
            self.root.after_cancel(self.version_animation_id)
        self.animate_version_update(version)

    def auto_connect(self):
        """自动连接设备"""
        def try_connect():
            try:
                self.upgrade = ElevatorUpgrade("************", 22, "root", "slamware123")
                if self.upgrade.connect():
                    self.is_connected = True
                    self.status_label.config(text="已连接")
                    self.connect_button.config(state=tk.NORMAL)
                    self.upgrade_button.config(state=tk.NORMAL)
                    self.log("设备连接成功")
                    
                    # 获取版本信息
                    version = self.upgrade.check_version()
                    if version:
                        self.update_version_display(version)
                else:
                    self.log("设备连接失败，5秒后重试...")
                    self.root.after(5000, try_connect)
            except Exception as e:
                self.log(f"连接错误: {str(e)}，5秒后重试...")
                self.root.after(5000, try_connect)
        
        # 开始尝试连接
        try_connect()

    def disconnect_device(self):
        """断开设备连接"""
        if self.upgrade:
            self.upgrade.close()
        self.is_connected = False
        self.status_label.config(text="正在连接设备...")
        self.connect_button.config(state=tk.DISABLED)
        self.upgrade_button.config(state=tk.DISABLED)
        self.version_label.config(text="当前版本: 未连接")
        self.log("已断开连接")
        # 重新开始自动连接
        self.auto_connect()

    def browse_file(self):
        filename = filedialog.askopenfilename(
            title="选择OTA文件",
            filetypes=[("Bin files", "*.bin"), ("All files", "*.*")]
        )
        if filename:
            self.file_path.set(filename)
            
    def start_upgrade(self):
        if not self.is_connected:
            messagebox.showerror("错误", "请先连接设备")
            return
            
        ota_file = self.file_path.get()
        if not ota_file:
            messagebox.showerror("错误", "请选择OTA文件")
            return
            
        if not os.path.exists(ota_file):
            messagebox.showerror("错误", "OTA文件不存在")
            return
            
        # 禁用按钮
        self.upgrade_button.config(state=tk.DISABLED)
        self.connect_button.config(state=tk.DISABLED)
        
        # 开始升级线程
        threading.Thread(target=self.upgrade_thread, args=(ota_file,), daemon=True).start()
        
    def upgrade_thread(self, ota_file):
        try:
            self.log("开始升级流程...")
            self.progress['value'] = 0
            
            # 获取文件名
            file_name = os.path.basename(ota_file)
            remote_path = f"/mnt/sdcard/{file_name}"
            
            # 上传文件
            self.log(f"正在上传文件 {file_name}...")
            if not self.upgrade.upload_file(ota_file, remote_path):
                raise Exception("文件上传失败")
            self.progress['value'] = 30
            
            # 执行升级命令
            self.log("执行升级命令...")
            success, output = self.upgrade.execute_command(f"sysupgrade.sh {remote_path}")
            if not success:
                # 检查是否是设备重启的情况
                if "+ exec" in output:
                    self.log("设备正在重启，请等待...")
                    self.progress['value'] = 50
                else:
                    raise Exception(f"升级命令执行失败: {output}")
            
            # 等待升级完成
            self.log("升级进行中，请等待约5分钟...")
            for i in range(50, 90, 2):
                time.sleep(6)
                self.progress['value'] = i
                
            # 等待设备重启
            self.log("等待设备重启...")
            self.upgrade.ssh.close()
            
            # 等待设备网络恢复
            self.log("等待设备网络恢复...")
            if not self.wait_for_host("************"):
                self.log("设备重启超时，请检查设备状态")
                return
            
            # 重新连接
            self.log("正在重新连接设备...")
            retry_count = 0
            while retry_count < 3:
                try:
                    if self.upgrade.connect():
                        break
                except:
                    retry_count += 1
                    time.sleep(5)
                    self.log(f"重连尝试 {retry_count}/3...")
            
            if not self.upgrade.ssh:
                self.log("无法重新连接设备，请手动检查设备状态")
                return
                
            self.progress['value'] = 95
            
            # 检查版本
            version = self.upgrade.check_version()
            if version:
                self.update_version_display(version)  # 使用带动画效果的版本更新
                self.log("升级完成")
                messagebox.showinfo("成功", "升级完成")
            else:
                self.log("无法获取版本信息，请手动检查设备状态")
                
            self.progress['value'] = 100
            
        except Exception as e:
            self.log(f"升级过程出现异常: {str(e)}")
        finally:
            # 恢复按钮状态
            self.upgrade_button.config(state=tk.NORMAL)
            self.connect_button.config(state=tk.NORMAL)

class ElevatorUpgrade:
    def __init__(self, host, port, username, password):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        self.ssh = None
        self.sftp = None

    def connect(self):
        """建立SSH连接"""
        try:
            self.ssh = paramiko.SSHClient()
            self.ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh.connect(self.host, self.port, self.username, self.password)
            self.sftp = self.ssh.open_sftp()
            return True
        except Exception as e:
            raise Exception(f"SSH连接失败: {str(e)}")

    def upload_file(self, local_path, remote_path):
        """上传文件到远程设备"""
        try:
            self.sftp.put(local_path, remote_path)
            return True
        except Exception as e:
            raise Exception(f"文件上传失败: {str(e)}")

    def execute_command(self, command):
        """执行远程命令"""
        try:
            stdin, stdout, stderr = self.ssh.exec_command(command)
            output = stdout.read().decode()
            error = stderr.read().decode()
            if error:
                return False, error
            return True, output
        except Exception as e:
            return False, str(e)

    def check_version(self):
        """检查版本信息"""
        success, output = self.execute_command("cat /etc/slamware_release")
        if success:
            # 解析版本信息，只提取ELEVATORVERSION字段
            for line in output.splitlines():
                if line.startswith('ELEVATORVERSION='):
                    return line.split('=')[1].strip('"')
        return None

    def close(self):
        """关闭连接"""
        if self.sftp:
            self.sftp.close()
        if self.ssh:
            self.ssh.close()

def main():
    root = tk.Tk()
    app = ElevatorUpgradeGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main() 