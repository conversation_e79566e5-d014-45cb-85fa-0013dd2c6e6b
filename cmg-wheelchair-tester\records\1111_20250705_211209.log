[2025-07-05 21:11:50] 开始测试设备: 1111
[2025-07-05 21:11:50] 测试数据已清除
[2025-07-05 21:11:50] 开始执行全部测试...
[2025-07-05 21:11:50] 开始测试: USB关键器件测试
[2025-07-05 21:11:50] 执行USB设备检测...
[2025-07-05 21:11:50] 执行命令: adb shell lsusb
[2025-07-05 21:11:51] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:11:52] 开始测试: CAN0测试
[2025-07-05 21:11:52] 执行CAN0测试流程...
[2025-07-05 21:11:52] 执行命令: adb shell ip link set can0 down
[2025-07-05 21:11:52] CAN0 down失败: error: no devices/emulators found

[2025-07-05 21:11:53] 开始测试: GPS测试
[2025-07-05 21:11:53] 执行GPS测试...
[2025-07-05 21:11:53] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-05 21:11:53] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:11:53] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-05 21:11:53] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:11:53] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-05 21:11:53] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:11:53] GPS信号检测失败
[2025-07-05 21:11:54] 开始测试: 4G模组测试
[2025-07-05 21:11:54] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-05 21:11:54] 监听线程已启动，等待串口数据...
[2025-07-05 21:11:56] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-05 21:11:56] AT指令发送失败: error: no devices/emulators found

[2025-07-05 21:11:57] 开始测试: 按键测试
[2025-07-05 21:11:57] 执行按键测试...
[2025-07-05 21:11:57] 请按手柄按键进行测试...
[2025-07-05 21:11:58] 开始测试: 按键灯测试
[2025-07-05 21:11:58] 开始LED背光灯测试...
[2025-07-05 21:11:58] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-05 21:11:58] LED控制命令执行失败: error: no devices/emulators found

[2025-07-05 21:11:59] 开始测试: 手电筒测试
[2025-07-05 21:11:59] 开始手电筒LED测试...
[2025-07-05 21:11:59] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-05 21:12:00] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-05 21:12:01] 开始测试: 摇杆使能测试
[2025-07-05 21:12:01] 执行摇杆测试...
[2025-07-05 21:12:01] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-05 21:12:01] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:12:02] 开始测试: 前摄像头测试
[2025-07-05 21:12:02] 开始执行前摄像头测试...
[2025-07-05 21:12:02] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-05 21:12:02] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:12:03] 开始测试: 光感测试
[2025-07-05 21:12:03] 执行光感测试...
[2025-07-05 21:12:03] 执行命令: adb shell evtest /dev/input/event1
[2025-07-05 21:12:03] 光感测试失败 - 未检测到数值变化
[2025-07-05 21:12:04] 开始测试: 回充摄像头测试
[2025-07-05 21:12:04] 开始执行回充摄像头测试...
[2025-07-05 21:12:04] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-05 21:12:05] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:12:06] 开始测试: 喇叭测试
[2025-07-05 21:12:06] 执行喇叭测试...
[2025-07-05 21:12:06] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-05 21:12:06] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:12:07] 开始测试: 蓝牙测试
[2025-07-05 21:12:07] 执行蓝牙测试...
[2025-07-05 21:12:07] 启动蓝牙服务...
[2025-07-05 21:12:07] 启动蓝牙服务失败: error: no devices/emulators found

[2025-07-05 21:12:07] 开启蓝牙...
[2025-07-05 21:12:07] 开启蓝牙失败: error: no devices/emulators found

[2025-07-05 21:12:07] 执行命令: adb shell bluetoothctl devices
[2025-07-05 21:12:07] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:12:08] 开始测试: WiFi测试
[2025-07-05 21:12:08] 执行WiFi测试...
[2025-07-05 21:12:08] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-05 21:12:08] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:12:09] 全部测试完成
[2025-07-05 21:12:09] 测试记录已保存: records/1111_20250705_211209.json

