#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的WiFi延时测试功能
"""

import subprocess
import re
import time

def test_ping_parsing():
    """测试ping输出解析功能"""
    print("=== 测试ping输出解析功能 ===")
    
    # 模拟ping输出数据
    test_output = """PING www.baidu.com (**************): 56 data bytes
64 bytes from ************** (**************): icmp_seq=1 ttl=54 time=10.9 ms
64 bytes from ************** (**************): icmp_seq=2 ttl=54 time=12.3 ms
64 bytes from ************** (**************): icmp_seq=3 ttl=54 time=8.7 ms
64 bytes from ************** (**************): icmp_seq=4 ttl=54 time=15.2 ms
64 bytes from ************** (**************): icmp_seq=5 ttl=54 time=9.8 ms
64 bytes from ************** (**************): icmp_seq=6 ttl=54 time=11.5 ms
64 bytes from ************** (**************): icmp_seq=7 ttl=54 time=13.1 ms
64 bytes from ************** (**************): icmp_seq=8 ttl=54 time=10.4 ms
64 bytes from ************** (**************): icmp_seq=9 ttl=54 time=14.6 ms
64 bytes from ************** (**************): icmp_seq=10 ttl=54 time=12.8 ms

--- www.baidu.com ping statistics ---
10 packets transmitted, 10 received, 0% packet loss
round-trip min/avg/max = 8.7/11.93/15.2 ms"""
    
    print("模拟ping输出:")
    lines = test_output.split('\n')
    for line in lines:
        if line.strip():
            print(f"  {line}")
    
    # 解析延时数据
    time_pattern = re.compile(r'time=(\d+\.?\d*)\s*ms')
    delays = []
    
    print("\n解析延时数据:")
    for line in lines:
        if "64 bytes from" in line and "time=" in line:
            match = time_pattern.search(line)
            if match:
                delay = float(match.group(1))
                delays.append(delay)
                print(f"  检测到延时: {delay} ms")
    
    if delays:
        # 计算统计数据
        avg_delay = sum(delays) / len(delays)
        min_delay = min(delays)
        max_delay = max(delays)
        
        print(f"\n统计结果:")
        print(f"  发包数量: {len(delays)} 个")
        print(f"  平均延时: {avg_delay:.2f} ms")
        print(f"  最小延时: {min_delay:.2f} ms")
        print(f"  最大延时: {max_delay:.2f} ms")
        
        # 判断网络质量
        if avg_delay <= 50:
            quality = "优秀"
        elif avg_delay <= 100:
            quality = "良好"
        elif avg_delay <= 200:
            quality = "一般"
        else:
            quality = "较差"
        
        print(f"  网络质量: {quality}")
        print(f"  结果显示: 平均延时: {avg_delay:.2f}ms ({quality})")
        
        return True
    else:
        print("❌ 未检测到有效的延时数据")
        return False

def test_real_ping():
    """测试真实的ping命令"""
    print("\n=== 测试真实ping命令 ===")
    
    try:
        print("执行命令: adb shell ping -c 10 www.baidu.com")
        print("正在进行网络延时测试...")
        
        start_time = time.time()
        result = subprocess.run(['adb', 'shell', 'ping', '-c', '10', 'www.baidu.com'], 
                              capture_output=True, text=True, timeout=20)
        end_time = time.time()
        
        print(f"命令执行时间: {end_time - start_time:.2f} 秒")
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print("ping命令执行成功")
            print("返回数据:")
            
            # 分行显示输出
            lines = output.split('\n')
            for line in lines:
                if line.strip():
                    print(f"  {line.strip()}")
            
            # 解析延时数据
            time_pattern = re.compile(r'time=(\d+\.?\d*)\s*ms')
            delays = []
            
            print("\n解析延时数据:")
            for line in lines:
                if "64 bytes from" in line and "time=" in line:
                    match = time_pattern.search(line)
                    if match:
                        delay = float(match.group(1))
                        delays.append(delay)
                        print(f"  检测到延时: {delay} ms")
            
            if delays:
                # 计算平均延时
                avg_delay = sum(delays) / len(delays)
                min_delay = min(delays)
                max_delay = max(delays)
                
                print(f"\n✅ WiFi延时测试成功")
                print(f"发包数量: {len(delays)} 个")
                print(f"平均延时: {avg_delay:.2f} ms")
                print(f"最小延时: {min_delay:.2f} ms")
                print(f"最大延时: {max_delay:.2f} ms")
                
                # 判断网络质量
                if avg_delay <= 50:
                    quality = "优秀"
                elif avg_delay <= 100:
                    quality = "良好"
                elif avg_delay <= 200:
                    quality = "一般"
                else:
                    quality = "较差"
                
                print(f"网络质量: {quality}")
                print(f"结果显示: 平均延时: {avg_delay:.2f}ms ({quality})")
                return True, avg_delay
            else:
                print("❌ 未检测到有效的延时数据")
                return False, None
        else:
            print(f"❌ ping命令执行失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False, None
            
    except Exception as e:
        print(f"❌ 测试出错: {str(e)}")
        return False, None

def test_network_quality_rating():
    """测试网络质量评级功能"""
    print("\n=== 测试网络质量评级功能 ===")
    
    test_delays = [25.5, 75.3, 150.8, 250.2]
    expected_qualities = ["优秀", "良好", "一般", "较差"]
    
    for delay, expected in zip(test_delays, expected_qualities):
        if delay <= 50:
            quality = "优秀"
        elif delay <= 100:
            quality = "良好"
        elif delay <= 200:
            quality = "一般"
        else:
            quality = "较差"
        
        result = "✅" if quality == expected else "❌"
        print(f"  {result} 延时 {delay}ms -> {quality} (期望: {expected})")

def test_adb_connection():
    """测试ADB连接"""
    print("\n=== 测试ADB连接 ===")
    
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        print(f"ADB devices返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("ADB连接正常")
            devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            connected_devices = [line for line in devices if 'device' in line]
            
            if connected_devices:
                print(f"已连接设备: {len(connected_devices)}个")
                for device in connected_devices:
                    print(f"  {device}")
                return True
            else:
                print("❌ 没有连接的设备")
                return False
        else:
            print(f"❌ ADB连接失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ ADB连接测试出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("WiFi延时测试功能验证")
    print("=" * 50)
    
    # 测试ping输出解析功能
    if not test_ping_parsing():
        print("\n❌ ping输出解析功能测试失败")
        exit(1)
    
    # 测试网络质量评级功能
    test_network_quality_rating()
    
    # 测试ADB连接
    if not test_adb_connection():
        print("\n❌ ADB连接失败，无法进行真实ping测试")
        exit(1)
    
    # 测试真实的ping命令
    success, avg_delay = test_real_ping()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 WiFi延时测试功能验证成功！")
        print(f"网络平均延时: {avg_delay:.2f} ms")
    else:
        print("❌ WiFi延时测试功能验证失败")
    
    print("\n测试完成")
