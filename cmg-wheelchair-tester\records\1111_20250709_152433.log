[2025-07-09 15:23:16] 开始测试 - SN: 1111
[2025-07-09 15:23:17] 
开始执行: 设备连接状态检测
[2025-07-09 15:23:17] 设备连接测试通过，检测到 1 个设备
[2025-07-09 15:23:17] 设备: ?	device
[2025-07-09 15:23:17] 
开始执行: USB关键器件检测
[2025-07-09 15:23:17] 执行USB设备检测...
[2025-07-09 15:23:17] 执行命令: adb shell lsusb
[2025-07-09 15:23:17] 设备返回数据:
[2025-07-09 15:23:17] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-09 15:23:17] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-09 15:23:17] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-09 15:23:17] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-09 15:23:17] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-09 15:23:17] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-09 15:23:17] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-09 15:23:17] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-09 15:23:17] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-09 15:23:17] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-09 15:23:17] 总共解析到 9 个设备
[2025-07-09 15:23:17] ✅ 所有预期的设备ID都已找到
[2025-07-09 15:23:18] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-09 15:23:18] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-09 15:23:18] ✅ USB设备检测通过
[2025-07-09 15:23:18] 
开始执行: CAN0测试
[2025-07-09 15:23:18] 执行CAN0测试流程...
[2025-07-09 15:23:18] 执行命令: adb shell ip link set can0 down
[2025-07-09 15:23:18] CAN0已关闭
[2025-07-09 15:23:18] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-09 15:23:18] CAN0已启动
[2025-07-09 15:23:18] CAN监听线程已启动...
[2025-07-09 15:23:19] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-09 15:23:20] CAN测试数据已发送，等待监听返回...
[2025-07-09 15:23:20] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-09 15:23:22] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-09 15:23:22] 
开始执行: GPS测试
[2025-07-09 15:23:22] 执行GPS测试...
[2025-07-09 15:23:22] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-09 15:23:32] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-09 15:23:33] 
开始执行: 4G模组测试
[2025-07-09 15:23:33] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-09 15:23:33] 监听线程已启动，等待串口数据...
[2025-07-09 15:23:35] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-09 15:23:35] AT+CCID指令已发送，等待串口返回...
[2025-07-09 15:23:35] 串口输出: AT+CCID
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 13
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: AT+C
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:35] 串口输出: +CME ERROR: 58
[2025-07-09 15:23:35] 串口输出: 
[2025-07-09 15:23:47] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-09 15:23:48] 
开始执行: 按键测试
[2025-07-09 15:23:48] 执行按键测试...
[2025-07-09 15:23:48] 请按手柄按键进行测试...
[2025-07-09 15:23:48] 
开始执行: 按键灯测试
[2025-07-09 15:23:48] 开始LED背光灯测试...
[2025-07-09 15:23:48] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-09 15:23:48] LED控制命令执行成功
[2025-07-09 15:23:53] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-09 15:23:53] LED灯已关闭
[2025-07-09 15:23:53] LED测试通过 - 背光灯正常点亮
[2025-07-09 15:23:53] 
开始执行: 手电筒测试
[2025-07-09 15:23:53] 开始手电筒LED测试...
[2025-07-09 15:23:53] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-09 15:23:54] 手电筒控制命令执行成功
[2025-07-09 15:23:55] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-09 15:23:55] 手电筒已关闭
[2025-07-09 15:23:55] 手电筒测试通过 - 手电筒正常点亮
[2025-07-09 15:23:55] 
开始执行: 摇杆使能测试
[2025-07-09 15:23:55] 执行摇杆测试...
[2025-07-09 15:23:55] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-09 15:23:55] 命令执行成功
[2025-07-09 15:23:55] 返回数据: 255
[2025-07-09 15:23:55] 摇杆测试通过，值: 255
[2025-07-09 15:23:56] 
开始执行: 前摄像头测试
[2025-07-09 15:23:56] 开始执行前摄像头测试...
[2025-07-09 15:23:56] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-09 15:23:56] 命令执行成功，返回: 无输出
[2025-07-09 15:23:56] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-09 15:23:57] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.322087908
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-09 15:23:57] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-09 15:23:57] 拉取命令执行成功
[2025-07-09 15:23:57] 返回数据: [ 37%] /data/camera/cam0_3840x2160.jpg
[ 74%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 15.6 MB/s (176591 bytes in 0.011s)
[2025-07-09 15:23:57] 图片已保存: cam0_3840x2160.jpg
[2025-07-09 15:24:00] 用户确认结果: 通过
[2025-07-09 15:24:01] 
开始执行: 光感测试
[2025-07-09 15:24:01] 执行光感测试...
[2025-07-09 15:24:01] 执行命令: adb shell evtest /dev/input/event1
[2025-07-09 15:24:08] 光感测试完成 - 检测到数值变化
[2025-07-09 15:24:08] 数值从 4 变化到 2
[2025-07-09 15:24:08] 
开始执行: 回充摄像头测试
[2025-07-09 15:24:08] 开始执行回充摄像头测试...
[2025-07-09 15:24:08] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-09 15:24:09] 命令执行成功，返回: 无输出
[2025-07-09 15:24:09] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-09 15:24:10] 命令执行成功，返回: 无输出
[2025-07-09 15:24:10] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-09 15:24:10] 拉取命令执行成功
[2025-07-09 15:24:10] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 2.4 MB/s (33576 bytes in 0.014s)
[2025-07-09 15:24:10] 图片已保存: output.jpg
[2025-07-09 15:24:11] 用户确认结果: 通过
[2025-07-09 15:24:11] 
开始执行: 喇叭测试
[2025-07-09 15:24:11] 执行喇叭测试...
[2025-07-09 15:24:12] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-09 15:24:26] 命令执行成功
[2025-07-09 15:24:26] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-09 15:24:26] 音频播放完成
[2025-07-09 15:24:26] 
开始执行: 蓝牙测试
[2025-07-09 15:24:26] 执行蓝牙测试...
[2025-07-09 15:24:26] 启动蓝牙服务...
[2025-07-09 15:24:26] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-09 15:24:26] 开启蓝牙...
[2025-07-09 15:24:26] 执行命令: adb shell bluetoothctl devices
[2025-07-09 15:24:26] 命令执行成功
[2025-07-09 15:24:26] 无输出数据
[2025-07-09 15:24:27] 
开始执行: WiFi测试
[2025-07-09 15:24:27] 执行WiFi测试...
[2025-07-09 15:24:27] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-09 15:24:27] 命令执行成功，无输出
[2025-07-09 15:24:27] 执行命令 2: adb shell rm -f /var/run/wpa_supplicant/wlan0
[2025-07-09 15:24:27] 命令执行成功，无输出
[2025-07-09 15:24:27] 执行命令 3: adb shell ip link set wlan0 down
[2025-07-09 15:24:27] 命令执行成功，无输出
[2025-07-09 15:24:27] 执行命令 4: adb shell wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && wpa_cli -i wlan0 add_network && wpa_cli -i wlan0 set_network 0 ssid '"Orion_SZ_5G"' && wpa_cli -i wlan0 set_network 0 psk '"Orion@2025"' && wpa_cli -i wlan0 enable_network 0 && udhcpc -i wlan0 && iw wlan0 link && ip addr show wlan0
[2025-07-09 15:24:30] 命令执行成功，返回数据:
[2025-07-09 15:24:30]   Successfully initialized wpa_supplicant
[2025-07-09 15:24:30]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-09 15:24:30]   1
[2025-07-09 15:24:30]   OK
[2025-07-09 15:24:30]   OK
[2025-07-09 15:24:30]   OK
[2025-07-09 15:24:30]   deleting routers
[2025-07-09 15:24:30]   adding dns ************
[2025-07-09 15:24:30]   adding dns ***********
[2025-07-09 15:24:31]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-09 15:24:31]   SSID: Orion_SZ_5G
[2025-07-09 15:24:31]   freq: 5300
[2025-07-09 15:24:31]   RX: 1484 bytes (4 packets)
[2025-07-09 15:24:31]   TX: 1140 bytes (7 packets)
[2025-07-09 15:24:31]   signal: -44 dBm
[2025-07-09 15:24:31]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-09 15:24:31]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-09 15:24:31]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-09 15:24:31]   link/ether 24:21:5e:c0:3b:8c brd ff:ff:ff:ff:ff:ff
[2025-07-09 15:24:31]   inet 192.168.20.178/24 brd 192.168.20.255 scope global wlan0
[2025-07-09 15:24:31]   valid_lft forever preferred_lft forever
[2025-07-09 15:24:31]   inet6 fe80::2e3b:e13a:6b9e:5fa3/64 scope link tentative
[2025-07-09 15:24:31]   valid_lft forever preferred_lft forever
[2025-07-09 15:24:31] 执行命令 5: adb shell ping -c 3 www.baidu.com
[2025-07-09 15:24:33] 命令执行成功，返回数据:
[2025-07-09 15:24:33]   PING www.a.shifen.com (157.148.69.151) 56(84) bytes of data.
[2025-07-09 15:24:33]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=1 ttl=54 time=10.8 ms
[2025-07-09 15:24:33]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=2 ttl=54 time=12.3 ms
[2025-07-09 15:24:33]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=3 ttl=54 time=25.2 ms
[2025-07-09 15:24:33]   --- www.a.shifen.com ping statistics ---
[2025-07-09 15:24:33]   3 packets transmitted, 3 received, 0% packet loss, time 2003ms
[2025-07-09 15:24:33]   rtt min/avg/max/mdev = 10.763/16.099/25.223/6.482 ms
[2025-07-09 15:24:33] ✅ ping测试成功，网络连通性正常
[2025-07-09 15:24:33] WiFi连接成功
[2025-07-09 15:24:33] 
测试完成 - 通过率: 12/15
[2025-07-09 15:24:33] ❌ 存在测试失败项！
[2025-07-09 15:24:33] 测试记录已保存: records/1111_20250709_152433.json

