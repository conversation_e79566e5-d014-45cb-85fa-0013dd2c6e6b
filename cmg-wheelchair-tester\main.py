import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import subprocess
import threading
import json
import datetime
import re
import os
import time
import sys
from typing import List, Dict, Tuple
from PIL import Image, ImageTk
import queue

class TestResult:
    def __init__(self, test_id: str, test_name: str, status: str = "未测试", message: str = ""):
        self.test_id = test_id
        self.test_name = test_name
        self.status = status  # 未测试, 测试中, 通过, 失败
        self.message = message
        self.timestamp = datetime.datetime.now()

class WheelchairTester:
    def __init__(self, root):
        self.root = root
        self.root.title("CMG整机功能测试软件")
        self.root.geometry("1080x900")  # 调整窗口尺寸
        
        # 设置默认字体和样式
        self.setup_styles()
        
        # 加载配置文件
        self.config = self.load_config()
        self.test_projects = self.config.get("test_projects", [])
        self.adb_settings = self.config.get("adb_settings", {})
        self.wifi_settings = self.config.get("wifi_settings", {})
        
        # 测试结果存储
        self.test_results = {}
        for project in self.test_projects:
            self.test_results[project["id"]] = TestResult(
                project["id"], 
                project["name"]
            )
        
        # 当前测试状态
        self.current_test = None
        self.test_running = False
        self.current_sn = None
        
        # 测试时间相关
        self.start_time = None
        self.end_time = None
        self.timer_running = False
        self.timer_id = None
        
        self.setup_ui()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        
        # 设置默认字体
        default_font = ("Microsoft YaHei UI", 10)
        self.root.option_add("*Font", default_font)
        
        # 设置全局背景色和前景色
        style.configure(".", 
            background="#ffffff",    # 白色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置标签框架样式
        style.configure("TLabelframe", 
            background="#ffffff",    # 白色背景
            darkcolor="#cccccc",     # 中灰色边框
            lightcolor="#cccccc"     # 中灰色边框
        )
        style.configure("TLabelframe.Label", 
            font=("Microsoft YaHei UI", 11, "bold"),
            background="#ffffff",    # 白色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置按钮基础样式
        style.configure("TButton", 
            padding=(10, 5), 
            font=("Microsoft YaHei UI", 10, "bold"),  # 加粗按钮文字
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        
        # 设置不同类型按钮的样式
        style.configure("Primary.TButton", 
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        style.map("Primary.TButton",
            relief=[('pressed', 'flat')]         # 按下时保持扁平
        )
        
        style.configure("Success.TButton", 
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        style.map("Success.TButton",
            relief=[('pressed', 'flat')]         # 按下时保持扁平
        )
        
        style.configure("Warning.TButton", 
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        style.map("Warning.TButton",
            relief=[('pressed', 'flat')]         # 按下时保持扁平
        )
        
        style.configure("Danger.TButton", 
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        style.map("Danger.TButton",
            relief=[('pressed', 'flat')]         # 按下时保持扁平
        )
        
        # 设置树形视图样式
        style.configure("Treeview", 
            font=("Microsoft YaHei UI", 10),
            background="#ffffff",    # 白色背景
            fieldbackground="#ffffff",# 白色背景
            foreground="#333333"     # 深灰色文字
        )
        style.configure("Treeview.Heading", 
            font=("Microsoft YaHei UI", 10, "bold"),
            background="#e9ecef",    # 浅灰色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置进度条样式
        style.configure("TProgressbar", 
            thickness=8,
            background="#0d6efd"     # Bootstrap蓝色
        )
        
        # 设置普通标签样式
        style.configure("TLabel", 
            font=("Microsoft YaHei UI", 10),
            background="#ffffff",    # 白色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置状态标签样式
        style.configure("Status.TLabel", 
            font=("Microsoft YaHei UI", 11),
            background="#ffffff",    # 白色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置框架样式
        style.configure("TFrame", 
            background="#ffffff"     # 白色背景
        )
        
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20", style="TFrame")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=0)  # 测试项目列表不扩展，自适应内容高度
        main_frame.rowconfigure(1, weight=0)  # 日志区域不扩展
        main_frame.rowconfigure(2, weight=0)  # 按钮区域不扩展
        
        # 测试项目列表
        test_frame = ttk.LabelFrame(main_frame, text="测试项目", padding="10")
        test_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))  # 移除N, S粘性
        test_frame.columnconfigure(0, weight=1)
        
        # 创建测试项目列表
        self.test_tree = ttk.Treeview(
            test_frame,
            columns=("data", "result"),
            show="tree headings",
            selectmode="browse",  # 单选模式
            height=15  # 设置固定高度为15行
        )
        
        self.test_tree.heading("#0", text="测试项目")
        self.test_tree.heading("data", text="测试数据")
        self.test_tree.heading("result", text="测试结果")
        self.test_tree.column("#0", width=250, minwidth=250)
        self.test_tree.column("data", width=200, minwidth=200)
        self.test_tree.column("result", width=100, minwidth=100, anchor="center")  # 结果列居中
        self.test_tree.grid(row=0, column=0, sticky=(tk.W, tk.E))  # 移除N, S粘性
        
        # 滚动条
        test_scrollbar = ttk.Scrollbar(test_frame, orient="vertical", command=self.test_tree.yview)
        test_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.test_tree.configure(yscrollcommand=test_scrollbar.set)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
        log_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 5))  # 移除N, S粘性
        log_frame.columnconfigure(0, weight=1)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=20,  # 减小日志区域高度
            font=("Microsoft YaHei UI", 9)
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E))  # 移除N, S粘性
        
        # 底部按钮区域（包含时间显示和按钮）
        button_frame = ttk.Frame(main_frame, padding=(0, 0))
        button_frame.grid(row=2, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(1, weight=1)  # 中间空白区域可扩展
        
        # 左侧时间显示区域
        time_frame = ttk.Frame(button_frame)
        time_frame.grid(row=0, column=0, sticky=tk.W)
        
        # 开始时间标签
        ttk.Label(time_frame, text="开始时间: ").grid(row=0, column=0, padx=(0, 5))
        self.start_time_label = ttk.Label(time_frame, text="--:--:--")
        self.start_time_label.grid(row=0, column=1, padx=(0, 15))
        
        # 结束时间标签
        ttk.Label(time_frame, text="结束时间: ").grid(row=0, column=2, padx=(0, 5))
        self.end_time_label = ttk.Label(time_frame, text="--:--:--")
        self.end_time_label.grid(row=0, column=3, padx=(0, 15))
        
        # 总用时标签
        ttk.Label(time_frame, text="总用时: ").grid(row=0, column=4, padx=(0, 5))
        self.total_time_label = ttk.Label(time_frame, text="--:--:--")
        self.total_time_label.grid(row=0, column=5, padx=(0, 15))
        
        # 右侧按钮区域
        control_frame = ttk.Frame(button_frame)
        control_frame.grid(row=0, column=2, sticky=tk.E)
        
        # 开始测试按钮
        self.start_all_btn = ttk.Button(
            control_frame,
            text="开始测试",
            command=self.start_all_tests,
            style="Success.TButton",
            width=10,
            padding=(10, 2)
        )
        self.start_all_btn.pack(side=tk.LEFT, padx=5)
        
        # 停止测试按钮
        self.stop_btn = ttk.Button(
            control_frame,
            text="停止测试",
            command=self.stop_tests,
            state="disabled",
            style="Danger.TButton",
            width=10,
            padding=(10, 2)
        )
        self.stop_btn.pack(side=tk.LEFT, padx=(5, 0))
        
        # 初始化测试项目列表
        self.init_test_tree()
        
        # 创建右键菜单
        self.test_menu = tk.Menu(self.root, tearoff=0)
        self.test_menu.add_command(label="运行测试", command=self.run_single_test)
        self.test_menu.add_command(label="查看详情", command=self.show_test_details)
        
        # 绑定右键菜单
        self.test_tree.bind("<Button-3>", self.show_test_menu)  # 右键点击
        self.test_tree.bind("<Double-Button-1>", self.run_single_test)  # 双击运行测试
        
        # 状态标签（移到类属性中，供其他方法使用）
        self.status_label = ttk.Label(
            self.root,
            text="未连接",
            style="Status.TLabel"
        )
        
        # 更新状态标签的颜色设置方法
        def update_status_color(self, is_connected=False):
            if is_connected:
                self.status_label.configure(foreground="#198754")  # Bootstrap绿色
            else:
                self.status_label.configure(foreground="#dc3545")  # Bootstrap红色
        
        # 将更新方法添加到类中
        self.update_status_color = update_status_color.__get__(self)
        
        # 初始状态为未连接
        self.update_status_color(False)
        
    def init_test_tree(self):
        """初始化测试项目列表"""
        # 设置结果列的标签样式
        self.test_tree.tag_configure("pass_result", foreground="#28a745", font=("Microsoft YaHei UI", 10, "bold"))  # 深绿色加粗
        self.test_tree.tag_configure("fail_result", foreground="#dc3545", font=("Microsoft YaHei UI", 10, "bold"))  # 深红色加粗
        self.test_tree.tag_configure("testing_result", foreground="#ffc107")  # 深黄色
        
        # 获取现有项目
        existing_items = self.test_tree.get_children()
        
        # 创建或更新测试项目列表
        for i, project in enumerate(self.test_projects):
            result = self.test_results[project["id"]]
            
            # 如果项目已存在，更新它
            if i < len(existing_items):
                item = existing_items[i]
                self.test_tree.item(item, text=project["name"])
            else:
                # 如果是新项目，创建它
                item = self.test_tree.insert("", "end", text=project["name"])
            
            # 更新测试数据和结果
            current_data = self.test_tree.set(item, "data")
            current_result = self.test_tree.set(item, "result")
            current_tags = self.test_tree.item(item, "tags")
            
            # 准备新的数据
            new_data = result.message if result.message else ""
            new_result = ""
            new_tags = ()
            
            if result.status == "通过":
                new_result = "PASS"
                new_tags = ("pass_result",)
            elif result.status == "失败":
                new_result = "FAIL"
                new_tags = ("fail_result",)
            elif result.status == "测试中":
                new_result = "测试中"
                new_tags = ("testing_result",)
            
            # 只在值发生变化时更新
            if current_data != new_data:
                self.test_tree.set(item, "data", new_data)
            if current_result != new_result:
                self.test_tree.set(item, "result", new_result)
            if current_tags != new_tags:
                self.test_tree.item(item, tags=new_tags)
        
        # 删除多余的项目
        if len(existing_items) > len(self.test_projects):
            for item in existing_items[len(self.test_projects):]:
                self.test_tree.delete(item)
                
    def update_test_item(self, test_id: str, status: str = None, message: str = None):
        """更新单个测试项目的状态"""
        # 查找对应的测试项目
        for item in self.test_tree.get_children():
            project_name = self.test_tree.item(item, "text")
            for project in self.test_projects:
                if project["name"] == project_name and project["id"] == test_id:
                    result = self.test_results[test_id]
                    
                    # 更新状态
                    if status is not None:
                        result.status = status
                    if message is not None:
                        result.message = message
                    
                    # 更新显示
                    if message is not None:
                        self.test_tree.set(item, "data", message)
                    
                    if status is not None:
                        if status == "通过":
                            self.test_tree.set(item, "result", "PASS")
                            self.test_tree.item(item, tags=("pass_result",))
                        elif status == "失败":
                            self.test_tree.set(item, "result", "FAIL")
                            self.test_tree.item(item, tags=("fail_result",))
                        elif status == "测试中":
                            self.test_tree.set(item, "result", "测试中")
                            self.test_tree.item(item, tags=("testing_result",))
                        else:
                            self.test_tree.set(item, "result", "")
                            self.test_tree.item(item, tags=())
                    break
                    
    def show_sn_input_dialog(self):
        """显示SN输入对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("扫描序列号")
        dialog.geometry("450x250")  # 调整窗口大小
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # 创建主框架
        main_frame = ttk.Frame(dialog, padding="25")  # 增加内边距
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 提示标签
        ttk.Label(
            main_frame,
            text="请扫描产品序列号：",
            font=("Microsoft YaHei UI", 12, "bold")  # 加粗字体
        ).pack(pady=(0, 15))
        
        # SN输入框
        sn_var = tk.StringVar()
        sn_entry = ttk.Entry(
            main_frame,
            textvariable=sn_var,
            font=("Microsoft YaHei UI", 12),
            width=30
        )
        sn_entry.pack(pady=(0, 25))  # 增加间距
        sn_entry.focus()
        
        # 错误提示标签
        error_var = tk.StringVar()
        error_label = ttk.Label(
            main_frame,
            textvariable=error_var,
            foreground="#dc3545",  # 使用Bootstrap的危险色
            font=("Microsoft YaHei UI", 10)
        )
        error_label.pack(pady=(0, 15))
        
        # 结果变量
        result = {"sn": None}
        
        def validate_and_save():
            sn = sn_var.get().strip()
            if not sn:
                error_var.set("SN不能为空！")
                return
            if not sn.isalnum():
                error_var.set("SN只能包含字母和数字！")
                return
            if len(sn) < 4:
                error_var.set("SN长度不能小于4位！")
                return
            result["sn"] = sn
            dialog.destroy()
            
        def on_cancel():
            dialog.destroy()
            
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(0, 10))
        
        # 确定按钮
        ttk.Button(
            button_frame,
            text="确定",
            command=validate_and_save,
            style="Success.TButton",
            width=12
        ).pack(side=tk.LEFT, padx=10)
        
        # 取消按钮
        ttk.Button(
            button_frame,
            text="取消",
            command=on_cancel,
            style="Danger.TButton",
            width=12
        ).pack(side=tk.LEFT, padx=10)
        
        # 绑定回车键
        dialog.bind("<Return>", lambda e: validate_and_save())
        dialog.bind("<Escape>", lambda e: on_cancel())

        # 设置1分钟超时
        timeout_id = dialog.after(60000, lambda: self.handle_dialog_timeout(dialog, result, "SN输入"))

        # 等待窗口关闭
        dialog.wait_window()

        # 取消超时定时器
        try:
            dialog.after_cancel(timeout_id)
        except:
            pass

        return result["sn"]

    def handle_dialog_timeout(self, dialog, result, test_name):
        """处理弹窗超时"""
        self.log_message(f"⏰ {test_name}超时 - 1分钟内无响应，自动关闭")
        if hasattr(result, '__setitem__'):
            result["timeout"] = True
        dialog.destroy()

    def show_timeout_confirmation(self, title, message):
        """显示带超时的确认对话框"""
        self.log_message(f"🔧 显示确认对话框: {title}")

        # 创建自定义对话框
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("500x300")  # 增加窗口大小
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        self.log_message("🔧 对话框窗口已创建")

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        result = {"answer": None, "timeout": False}

        # 主框架
        main_frame = ttk.Frame(dialog, padding="30")  # 增加内边距
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 消息文本
        message_label = ttk.Label(
            main_frame,
            text=message,
            justify=tk.CENTER,
            wraplength=400,
            font=("Microsoft YaHei UI", 11)  # 设置字体
        )
        message_label.pack(pady=(0, 25))

        # 倒计时显示
        countdown_var = tk.StringVar(value="剩余时间: 60秒")
        countdown_label = ttk.Label(
            main_frame,
            textvariable=countdown_var,
            foreground="red",
            font=("Microsoft YaHei UI", 12, "bold")  # 加粗字体
        )
        countdown_label.pack(pady=(0, 25))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)

        def on_yes():
            result["answer"] = "yes"
            self.log_message("👤 用户选择: 是 (测试通过)")
            dialog.destroy()

        def on_no():
            result["answer"] = "no"
            self.log_message("👤 用户选择: 否 (测试失败)")
            dialog.destroy()

        def on_timeout():
            result["timeout"] = True
            result["answer"] = "timeout"  # 超时状态，区别于用户主动选择
            self.log_message("⏰ 确认对话框超时 - 1分钟内无响应，自动判定为失败")
            dialog.destroy()

        # 按钮 - 增加按钮大小和样式
        yes_button = ttk.Button(
            button_frame,
            text="是 (通过)",
            command=on_yes,
            width=15
        )
        yes_button.pack(side=tk.LEFT, padx=(0, 20))
        self.log_message("🔧 '是'按钮已创建")

        no_button = ttk.Button(
            button_frame,
            text="否 (失败)",
            command=on_no,
            width=15
        )
        no_button.pack(side=tk.LEFT)
        self.log_message("🔧 '否'按钮已创建")

        # 倒计时更新
        remaining_time = [60]  # 使用列表以便在嵌套函数中修改

        def update_countdown():
            if remaining_time[0] > 0:
                countdown_var.set(f"剩余时间: {remaining_time[0]}秒")
                remaining_time[0] -= 1
                dialog.after(1000, update_countdown)
            else:
                on_timeout()

        # 开始倒计时
        update_countdown()

        # 绑定键盘事件
        dialog.bind("<Return>", lambda e: on_yes())
        dialog.bind("<Escape>", lambda e: on_no())

        # 强制更新显示
        dialog.update()
        self.log_message("🔧 对话框显示完成，等待用户响应...")

        # 等待窗口关闭
        dialog.wait_window()

        self.log_message(f"🔧 对话框关闭，用户响应: {result['answer']}")
        return result["answer"]

    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            messagebox.showerror("配置错误", "配置文件config.json不存在")
            return {}
        except Exception as e:
            messagebox.showerror("配置错误", f"加载配置文件失败: {str(e)}")
            return {}
        
    def show_test_menu(self, event):
        """显示测试项目右键菜单"""
        item = self.test_tree.selection()
        if item:
            self.test_menu.post(event.x_root, event.y_root)
            
    def run_single_test(self, event=None):
        """运行单个测试"""
        item = self.test_tree.selection()
        if not item:
            return
            
        test_name = self.test_tree.item(item[0], "text")
        test_project = None
        for project in self.test_projects:
            if project["name"] == test_name:
                test_project = project
                break
                
        if test_project:
            # 更新UI状态为测试中
            self.test_tree.set(item[0], "data", "")
            self.test_tree.set(item[0], "result", "测试中")
            self.test_tree.item(item[0], tags=("testing_result",))  # 设置测试中样式
            
            # 执行测试
            result = self.run_test(test_project)
            
            # 更新结果
            self.test_results[test_project["id"]].status = "通过" if result else "失败"
            self.test_results[test_project["id"]].timestamp = datetime.datetime.now()
            
            # 更新UI
            result_text = "PASS" if result else "FAIL"
            self.test_tree.set(item[0], "data", self.test_results[test_project["id"]].message)
            self.test_tree.set(item[0], "result", result_text)
            
            # 设置结果列的标签和背景色
            if result:
                self.test_tree.item(item[0], tags=("pass_result",))  # 设置通过样式
            else:
                self.test_tree.item(item[0], tags=("fail_result",))  # 设置失败样式
                
    def show_test_details(self):
        """显示测试详情"""
        item = self.test_tree.selection()
        if not item:
            return
            
        test_name = self.test_tree.item(item[0], "text")
        test_project = None
        for project in self.test_projects:
            if project["name"] == test_name:
                test_project = project
                break
                
        if test_project:
            self.show_test_info(test_project)
            
    def show_test_info(self, test_project):
        """显示测试项目信息"""
        info_window = tk.Toplevel(self.root)
        info_window.title(f"测试详情 - {test_project['name']}")
        info_window.geometry("600x400")
        
        text_widget = scrolledtext.ScrolledText(info_window)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        info_text = f"""测试项目: {test_project['name']}
描述: {test_project['description']}
类型: {test_project.get('type', '未知')}

"""
        
        if 'command' in test_project:
            info_text += f"命令: {test_project['command']}\n"
        elif 'commands' in test_project:
            info_text += "命令列表:\n"
            for i, cmd in enumerate(test_project['commands']):
                info_text += f"  {i+1}. {cmd}\n"
                
        if 'expected_devices' in test_project:
            info_text += "\n预期设备:\n"
            for device in test_project['expected_devices']:
                info_text += f"  Bus {device['bus']} Device {device['device']}: ID {device['id']}\n"
                
        if 'expected_patterns' in test_project:
            info_text += f"\n预期模式: {', '.join(test_project['expected_patterns'])}\n"
            
        if 'expected_value' in test_project:
            info_text += f"\n预期值: {test_project['expected_value']}\n"
            
        text_widget.insert(tk.END, info_text)
        text_widget.config(state=tk.DISABLED)
        
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def save_log(self):
        """保存日志到文件"""
        try:
            # 创建logs目录（如果不存在）
            if not os.path.exists("logs"):
                os.makedirs("logs")
            
            # 生成日志文件名（使用当前时间和SN号）
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            sn = self.current_sn if self.current_sn else "unknown"
            filename = f"logs/test_log_{sn}_{timestamp}.txt"
            
            # 保存日志
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            
            self.log_message(f"日志已保存到: {filename}")
            
        except Exception as e:
            self.log_message(f"保存日志失败: {str(e)}")
        
    def export_results(self):
        """导出测试结果"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                results = {}
                for test_id, result in self.test_results.items():
                    results[test_id] = {
                        "test_name": result.test_name,
                        "status": result.status,
                        "message": result.message,
                        "timestamp": result.timestamp.isoformat()
                    }
                    
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=4, ensure_ascii=False)
                messagebox.showinfo("成功", f"测试结果已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出结果失败: {str(e)}")
        
    def check_connection(self):
        """检测ADB连接状态"""
        def check():
            try:
                self.log_message("正在检测ADB连接...")
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                    connected_devices = [d for d in devices if d.strip() and 'device' in d]
                    
                    if connected_devices:
                        self.status_label.config(text=f"已连接 ({len(connected_devices)} 个设备)")
                        self.update_status_color(True)  # 使用统一的颜色设置方法
                        self.log_message(f"ADB连接成功，检测到 {len(connected_devices)} 个设备")
                        for device in connected_devices:
                            self.log_message(f"设备: {device}")
                    else:
                        self.status_label.config(text="未连接设备")
                        self.update_status_color(False)  # 使用统一的颜色设置方法
                        self.log_message("ADB连接成功，但未检测到设备")
                else:
                    self.status_label.config(text="连接失败")
                    self.update_status_color(False)  # 使用统一的颜色设置方法
                    self.log_message(f"ADB连接失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                self.status_label.config(text="连接超时")
                self.update_status_color(False)  # 使用统一的颜色设置方法
                self.log_message("ADB连接超时")
            except FileNotFoundError:
                self.status_label.config(text="ADB未安装")
                self.update_status_color(False)  # 使用统一的颜色设置方法
                self.log_message("错误: ADB未安装或不在PATH中")
            except Exception as e:
                self.status_label.config(text="连接错误")
                self.update_status_color(False)  # 使用统一的颜色设置方法
                self.log_message(f"连接检测出错: {str(e)}")
                
        threading.Thread(target=check, daemon=True).start()
        
    def start_all_tests(self):
        """开始所有测试"""
        if not self.test_running:
            # 自动清除之前的测试数据和日志
            self.clear_test_data()
            self.clear_log()
            
            self.test_running = True
            self.start_all_btn.config(state="disabled")
            self.stop_btn.config(state="normal")
            
            # 获取SN号
            sn = self.show_sn_input_dialog()
            if not sn:
                self.test_running = False
                self.start_all_btn.config(state="normal")
                self.stop_btn.config(state="disabled")
                return
                
            self.current_sn = sn
            self.log_message(f"开始测试 - SN: {sn}")
            
            # 记录开始时间
            self.start_time = datetime.datetime.now()
            self.start_time_label.config(text=self.start_time.strftime("%H:%M:%S"))
            self.end_time_label.config(text="--:--:--")
            self.total_time_label.config(text="00:00:00")
            
            # 启动计时器
            self.timer_running = True
            self.update_total_time()
            
            # 在新线程中运行测试
            threading.Thread(target=self.run_all_tests, daemon=True).start()
            
    def run_all_tests(self):
        """运行所有测试的具体实现"""
        try:
            total_tests = len(self.test_projects)
            passed_tests = 0
            
            for project in self.test_projects:
                if not self.test_running:
                    break
                    
                self.current_test = project
                self.test_results[project["id"]].status = "测试中"
                self.test_results[project["id"]].timestamp = datetime.datetime.now()
                self.init_test_tree()  # 更新显示
                
                self.log_message(f"\n开始执行: {project['name']}")
                result = self.run_test(project)
                
                if result:
                    self.test_results[project["id"]].status = "通过"
                    passed_tests += 1
                else:
                    self.test_results[project["id"]].status = "失败"
                
                self.init_test_tree()  # 更新显示
                
            if self.test_running:
                self.log_message(f"\n测试完成 - 通过率: {passed_tests}/{total_tests}")
                if passed_tests == total_tests:
                    self.log_message("✅ 所有测试通过！")
                else:
                    self.log_message("❌ 存在测试失败项！")
                
                # 记录结束时间
                self.timer_running = False
                if self.timer_id:
                    self.root.after_cancel(self.timer_id)
                    self.timer_id = None
                self.end_time = datetime.datetime.now()
                self.end_time_label.config(text=self.end_time.strftime("%H:%M:%S"))
                self.update_total_time()
                
                # 保存测试记录和日志
                self.save_test_record()
                self.save_log()
                
        except Exception as e:
            self.log_message(f"测试过程出错: {str(e)}")
            
        finally:
            self.test_running = False
            self.current_test = None
            self.start_all_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
        
    def save_test_record(self):
        """保存测试记录"""
        if not self.current_sn:
            return
            
        try:
            # 创建records目录（如果不存在）
            os.makedirs("records", exist_ok=True)
            
            # 生成文件名：SN_日期时间
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"records/{self.current_sn}_{timestamp}.json"
            
            # 准备测试结果数据
            results = {
                "sn": self.current_sn,
                "timestamp": datetime.datetime.now().isoformat(),
                "results": {}
            }
            
            for test_id, result in self.test_results.items():
                results["results"][test_id] = {
                    "test_name": result.test_name,
                    "status": result.status,
                    "message": result.message,
                    "timestamp": result.timestamp.isoformat()
                }
                
            # 保存测试记录
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=4, ensure_ascii=False)
                
            self.log_message(f"测试记录已保存: {filename}")
            
            # 同时保存日志文件
            log_filename = f"records/{self.current_sn}_{timestamp}.log"
            with open(log_filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
                
            self.log_message(f"测试日志已保存: {log_filename}")
            
        except Exception as e:
            self.log_message(f"保存测试记录失败: {str(e)}")
            messagebox.showerror("错误", f"保存测试记录失败: {str(e)}")
        
    def clear_test_data(self):
        """清除所有测试数据"""
        # 重置测试结果
        self.test_results = {}
        for project in self.test_projects:
            self.test_results[project["id"]] = TestResult(
                test_id=project["id"],
                test_name=project["name"]
            )
            
        # 更新显示
        self.init_test_tree()
        
        # 清除日志
        self.clear_log()
        
        # 重置时间显示
        self.start_time = None
        self.end_time = None
        self.timer_running = False
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
        self.start_time_label.config(text="--:--:--")
        self.end_time_label.config(text="--:--:--")
        self.total_time_label.config(text="--:--:--")
        
        # 重置按钮状态
        self.start_all_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        self.log_message("测试数据已清除")
        
    def stop_tests(self):
        """停止测试"""
        self.test_running = False
        self.timer_running = False
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
        self.log_message("正在停止测试...")
        
        # 记录结束时间
        self.end_time = datetime.datetime.now()
        self.end_time_label.config(text=self.end_time.strftime("%H:%M:%S"))
        self.update_total_time()
        
    def update_total_time(self):
        """更新总用时显示"""
        if self.start_time:
            current_time = datetime.datetime.now() if self.timer_running else self.end_time
            if current_time:
                elapsed = current_time - self.start_time
                hours = elapsed.seconds // 3600
                minutes = (elapsed.seconds % 3600) // 60
                seconds = elapsed.seconds % 60
                self.total_time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
                
                if self.timer_running:
                    self.timer_id = self.root.after(1000, self.update_total_time)
        
    def run_test(self, test_project):
        """执行测试项目"""
        try:
            # 更新状态为测试中
            self.update_test_item(test_project["id"], "测试中")
            
            if test_project["type"] == "connection_test":
                # 执行连接状态测试
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                    connected_devices = [d for d in devices if d.strip() and 'device' in d]
                    
                    if connected_devices:
                        message = f"已连接 {len(connected_devices)} 个设备"
                        self.update_test_item(test_project["id"], "通过", message)
                        self.log_message(f"设备连接测试通过，检测到 {len(connected_devices)} 个设备")
                        for device in connected_devices:
                            self.log_message(f"设备: {device}")
                        return True
                    else:
                        self.update_test_item(test_project["id"], "失败", "未连接设备")
                        self.log_message("设备连接测试失败：未检测到设备")
                        return False
                else:
                    self.update_test_item(test_project["id"], "失败", "ADB连接失败")
                    self.log_message(f"设备连接测试失败：{result.stderr}")
                    return False
                    
            elif test_project["type"] == "rom_version_test":
                return self.run_rom_version_test(test_project)
            elif test_project["type"] == "usb_test":
                return self.run_usb_test(test_project)
            elif test_project["type"] == "can_test":
                return self.run_can_test(test_project)
            elif test_project["type"] == "gps_test":
                return self.run_gps_test(test_project)
            elif test_project["type"] == "4g_test":
                return self.run_4g_test(test_project)
            elif test_project["type"] == "key_test":
                return self.run_key_test(test_project)
            elif test_project["type"] == "led_test":
                return self.run_led_test(test_project)
            elif test_project["type"] == "joystick_test":
                return self.run_joystick_test(test_project)
            elif test_project["type"] == "camera_test":
                return self.run_camera_test(test_project)
            elif test_project["type"] == "light_sensor_test":
                return self.run_light_sensor_test(test_project)
            elif test_project["type"] == "speaker_test":
                return self.run_speaker_test(test_project)
            elif test_project["type"] == "bluetooth_test":
                return self.run_bluetooth_test(test_project)
            elif test_project["type"] == "wifi_test":
                return self.run_wifi_test(test_project)
            elif test_project["type"] == "torch_test":
                return self.run_torch_test(test_project)
            else:
                self.update_test_item(test_project["id"], "失败", "未知的测试类型")
                self.log_message(f"未知的测试类型：{test_project['type']}")
                return False
                
        except Exception as e:
            self.update_test_item(test_project["id"], "失败", f"测试出错: {str(e)}")
            self.log_message(f"测试执行出错: {str(e)}")
            return False
            
    def run_usb_test(self, test_project):
        """USB设备测试"""
        try:
            self.log_message("执行USB设备检测...")
            command = "lsusb"
            self.log_message(f"执行命令: adb shell {command}")
            
            result = subprocess.run(['adb', 'shell', command], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log_message("设备返回数据:")
                self.log_message(result.stdout)
                
                # 解析设备
                devices = self.parse_lsusb_output(result.stdout)
                expected_devices = test_project.get("expected_devices", [])
                
                # 比较设备
                passed, issues = self.compare_devices(devices, expected_devices)
                
                if passed:
                    device_count = len(devices)
                    self.log_message("✅ USB设备检测通过")
                    self.test_results[test_project["id"]].message = f"检测到{device_count}个USB设备"
                    return True
                else:
                    device_count = len(devices)
                    self.log_message("❌ USB设备检测失败")
                    for issue in issues:
                        self.log_message(f"问题: {issue}")
                    self.test_results[test_project["id"]].message = f"检测到{device_count}个设备; " + "; ".join(issues)
                    return False
            else:
                self.log_message(f"命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "命令执行失败"
                return False
                
        except Exception as e:
            self.log_message(f"USB测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False

    def run_rom_version_test(self, test_project):
        """3568版本测试 - 读取ROM版本号"""
        try:
            self.log_message("执行3568版本测试...")
            self.log_message("读取ROM版本号...")

            # 执行uname -a命令获取系统信息
            command = "uname -a"
            self.log_message(f"执行命令: adb shell {command}")

            result = subprocess.run(['adb', 'shell', command],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                output = result.stdout.strip()
                self.log_message("uname命令执行成功")
                self.log_message(f"返回数据: {output}")

                # 解析版本信息
                # 示例输出: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
                # 需要截取: Jul 9 12:01:12
                import re

                # 匹配日期时间模式: 月份 日期 时:分:秒
                # 支持格式: Wed Jul 9 12:01:12 CST 2025 或 Jul 9 12:01:12
                date_pattern = re.compile(r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})')
                match = date_pattern.search(output)

                if match:
                    version_info = match.group(1)
                    self.log_message(f"✅ 3568版本测试成功")
                    self.log_message(f"ROM版本号: {version_info}")

                    # 检查是否包含rk3568标识
                    if "rk3568" in output.lower():
                        self.log_message("✅ 确认为RK3568平台")
                        platform_info = "RK3568"
                    else:
                        self.log_message("⚠️ 未检测到RK3568标识")
                        platform_info = "未知平台"

                    self.test_results[test_project["id"]].message = f"{platform_info} - {version_info}"
                    return True
                else:
                    self.log_message("❌ 无法解析版本信息")
                    self.log_message("未找到有效的日期时间格式")
                    self.test_results[test_project["id"]].message = "版本信息解析失败"
                    return False
            else:
                self.log_message(f"❌ uname命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "命令执行失败"
                return False

        except Exception as e:
            self.log_message(f"3568版本测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False

    def parse_lsusb_output(self, output: str) -> List[Dict]:
        """解析lsusb输出"""
        devices = []
        lines = output.strip().split('\n')
        
        for line in lines:
            if line.strip():
                # 处理可能带时间戳的行，如: [2025-07-03 16:19:28] Bus 005 Device 001: ID 1d6b:0001
                # 先尝试匹配带时间戳的格式
                match = re.match(r'\[.*?\]\s*Bus (\d+) Device (\d+): ID ([a-f0-9:]+)', line)
                if not match:
                    # 如果没有时间戳，匹配标准格式
                    match = re.match(r'Bus (\d+) Device (\d+): ID ([a-f0-9:]+)', line)
                
                if match:
                    devices.append({
                        "bus": match.group(1),
                        "device": match.group(2),
                        "id": match.group(3)
                    })
                    self.log_message(f"解析到设备: Bus {match.group(1)} Device {match.group(2)} ID {match.group(3)}")
                    
        self.log_message(f"总共解析到 {len(devices)} 个设备")
        return devices
        
    def compare_devices(self, detected_devices: List[Dict], expected_devices: List[Dict]) -> Tuple[bool, List[str]]:
        """比较检测到的设备与预期设备"""
        issues = []
        passed = True
        
        # 只比较设备ID，不严格比较设备号（因为设备号可能会变化）
        detected_ids = [device["id"] for device in detected_devices]
        expected_ids = [device["id"] for device in expected_devices]
        
        # 检查是否有缺失的设备ID
        missing_ids = []
        for expected_id in expected_ids:
            if expected_id not in detected_ids:
                missing_ids.append(expected_id)
                issues.append(f"缺少设备ID: {expected_id}")
                passed = False
        
        # 检查是否有额外的设备ID
        extra_ids = []
        for detected_id in detected_ids:
            if detected_id not in expected_ids:
                extra_ids.append(detected_id)
        
        # 记录详细信息
        if missing_ids:
            self.log_message(f"缺失的设备ID: {missing_ids}")
        if extra_ids:
            self.log_message(f"额外的设备ID: {extra_ids}")
            
        # 如果所有预期的设备ID都找到了，就认为测试通过
        if not missing_ids:
            self.log_message(f"✅ 所有预期的设备ID都已找到")
            self.log_message(f"检测到的设备: {detected_ids}")
            self.log_message(f"预期的设备: {expected_ids}")
                
        return passed, issues
        
    def run_can_test(self, test_project):
        """CAN0测试，严格按照手动流程自动化"""
        import queue
        self.log_message("执行CAN0测试流程...")
        output_queue = queue.Queue()
        listen_timeout = 5  # 监听最大秒数
        send_frame = "1234ABCD#aa.00.cc.aa.55.66.aa.ff"
        send_id = "1234ABCD"
        send_data = "AA 00 CC AA 55 66 AA FF"
        can_pattern = re.compile(r'can0\s+([A-Fa-f0-9]+)\s+\[\d+\]\s+([A-Fa-f0-9 ]+)')

        # 1. 关闭CAN接口
        down_cmd = ["adb", "shell", "ip", "link", "set", "can0", "down"]
        self.log_message(f"执行命令: {' '.join(down_cmd)}")
        result = subprocess.run(down_cmd, capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            self.log_message(f"CAN0 down失败: {result.stderr}")
            self.test_results[test_project["id"]].message = f"CAN0 down失败: {result.stderr}"
            return False
        self.log_message("CAN0已关闭")

        # 2. 启动CAN接口
        up_cmd = ["adb", "shell", "ip", "link", "set", "can0", "up", "type", "can", "bitrate", "500000", "loopback", "on"]
        self.log_message(f"执行命令: {' '.join(up_cmd)}")
        result = subprocess.run(up_cmd, capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            self.log_message(f"CAN0 up失败: {result.stderr}")
            self.test_results[test_project["id"]].message = f"CAN0 up失败: {result.stderr}"
            return False
        self.log_message("CAN0已启动")

        # 3. 启动监听线程
        def listen_can(q, stop_event):
            try:
                proc = subprocess.Popen(["adb", "shell", "candump", "can0"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                while not stop_event.is_set():
                    line = proc.stdout.readline()
                    if not line:
                        break
                    q.put(line)
                proc.terminate()
            except Exception as e:
                self.log_message(f"CAN监听线程异常: {str(e)}")
                q.put(f"[ERROR]{str(e)}")

        stop_event = threading.Event()
        listener = threading.Thread(target=listen_can, args=(output_queue, stop_event), daemon=True)
        listener.start()
        self.log_message("CAN监听线程已启动...")
        time.sleep(1)  # 等待监听就绪

        # 4. 发送测试数据
        send_cmd = ["adb", "shell", "cansend", "can0", send_frame]
        self.log_message(f"执行命令: {' '.join(send_cmd)}")
        send_result = subprocess.run(send_cmd, capture_output=True, text=True, timeout=5)
        if send_result.returncode != 0:
            self.log_message(f"CAN数据发送失败: {send_result.stderr}")
            self.test_results[test_project["id"]].message = f"CAN数据发送失败: {send_result.stderr}"
            stop_event.set()
            listener.join(timeout=2)
            return False
        self.log_message("CAN测试数据已发送，等待监听返回...")

        # 5. 采集监听数据
        output = ""
        can_data = None
        start_time = time.time()
        while time.time() - start_time < listen_timeout:
            while not output_queue.empty():
                line = output_queue.get()
                output += line
                self.log_message(f"CAN监听输出: {line.strip()}")
                match = can_pattern.search(line)
                if match:
                    can_id = match.group(1).upper()
                    can_payload = ' '.join(match.group(2).split()).upper()
                    can_data = f"ID: {can_id} DATA: {can_payload}"
                    # 判断是否与发送内容一致
                    if can_id == send_id and can_payload == send_data:
                        stop_event.set()
                        listener.join(timeout=2)
                        self.log_message(f"✅ CAN测试成功，接收到数据: {can_data}")
                        self.test_results[test_project["id"]].message = f"接收到: {can_data}"
                        return True
            time.sleep(0.1)
        stop_event.set()
        listener.join(timeout=2)
        # 未匹配到
        if can_data:
            self.log_message(f"❌ CAN测试数据不一致，接收到: {can_data}")
            self.test_results[test_project["id"]].message = f"接收到: {can_data}"
        else:
            self.log_message("❌ 未检测到CAN数据")
            self.test_results[test_project["id"]].message = "未检测到CAN数据"
        return False
            
    def run_gps_test(self, test_project):
        """GPS测试"""
        try:
            self.log_message("执行GPS测试...")
            
            for i, cmd in enumerate(test_project["commands"]):
                self.log_message(f"执行命令 {i+1}: adb shell {cmd}")
                result = subprocess.run(['adb', 'shell', cmd], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    if result.stdout.strip():
                        self.log_message(f"GPS信号检测成功")
                        self.log_message(f"返回数据: {result.stdout.strip()}")
                        self.test_results[test_project["id"]].message = "GPS信号正常"
                        return True
                    else:
                        self.log_message(f"命令执行成功，但无GPS信号数据")
                else:
                    self.log_message(f"命令执行失败: {result.stderr}")
                    
            self.log_message("GPS信号检测失败")
            self.test_results[test_project["id"]].message = "无GPS信号"
            return False
            
        except Exception as e:
            self.log_message(f"GPS测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_4g_test(self, test_project):
        """4G模组测试（自动化，严格模拟手动流程）"""
        self.log_message("执行4G模组测试...（自动化监听+指令发送）")
        output_queue = queue.Queue()
        listen_timeout = 10  # 监听最大秒数
        ccid_pattern = re.compile(r'(\d{19,20})')
        
        def listen_serial(q, stop_event):
            try:
                proc = subprocess.Popen(['adb', 'shell', 'cat', '/dev/ttyUSB0'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                while not stop_event.is_set():
                    line = proc.stdout.readline()
                    if not line:
                        break
                    q.put(line)
                proc.terminate()
            except Exception as e:
                self.log_message(f"监听线程异常: {str(e)}")
                q.put(f"[ERROR]{str(e)}")

        # 1. 启动监听线程
        stop_event = threading.Event()
        listener = threading.Thread(target=listen_serial, args=(output_queue, stop_event), daemon=True)
        listener.start()
        self.log_message("监听线程已启动，等待串口数据...")
        time.sleep(2)  # 等待监听就绪

        # 2. 发送AT+CCID指令
        send_cmd = 'adb shell "echo -e \'AT+CCID\\r\' > /dev/ttyUSB0"'
        self.log_message(f"执行命令: {send_cmd}")
        try:
            send_result = subprocess.run(send_cmd, shell=True, capture_output=True, text=True, timeout=5)
            if send_result.returncode != 0:
                self.log_message(f"AT指令发送失败: {send_result.stderr}")
                self.test_results[test_project["id"]].message = f"AT指令发送失败: {send_result.stderr}"
                stop_event.set()
                return False
            else:
                self.log_message("AT+CCID指令已发送，等待串口返回...")
        except Exception as e:
            self.log_message(f"AT指令发送异常: {str(e)}")
            self.test_results[test_project["id"]].message = f"AT指令发送异常: {str(e)}"
            stop_event.set()
            return False

        # 3. 主进程等待串口输出，提取CCID
        output = ""
        ccid = None
        start_time = time.time()
        while time.time() - start_time < listen_timeout:
            try:
                while not output_queue.empty():
                    line = output_queue.get()
                    output += line
                    self.log_message(f"串口输出: {line.strip()}")
                    match = ccid_pattern.search(line)
                    if match:
                        ccid = match.group(1)
                        break
                if ccid:
                    break
                time.sleep(0.2)
            except Exception as e:
                self.log_message(f"串口读取异常: {str(e)}")
                break
        stop_event.set()
        listener.join(timeout=2)

        # 4. 结果处理
        if ccid:
            self.log_message(f"✅ 4G模组测试成功，CCID: {ccid}")
            self.test_results[test_project["id"]].message = f"CCID: {ccid}"
            return True
        else:
            self.log_message("❌ 未检测到CCID，请检查4G模组或手动测试")
            self.test_results[test_project["id"]].message = "未检测到CCID"
            return False
            
    def run_key_test(self, test_project):
        """按键测试 - 简化版本，专注于UI更新
        测试流程：
        1. 执行 adb shell evtest /dev/input/event5
        2. 监听按键事件，检测8个按键的状态变化
        3. 按键事件码：656, 657, 658, 659, 660, 661, 662, 663
        4. 每个按键需要检测到 value 1->0 的变化
        """
        try:
            # 记录这是按键测试，已经在本函数中进行了手动确认
            test_project["_manual_check_done"] = True

            self.log_message("开始按键测试...")

            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title("按键测试")
            test_window.geometry("600x400")
            test_window.resizable(False, False)
            test_window.transient(self.root)
            test_window.grab_set()

            # 主框架
            main_frame = ttk.Frame(test_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(main_frame, text="按键测试", font=("Arial", 16, "bold"))
            title_label.pack(pady=(0, 20))

            # 状态显示
            status_var = tk.StringVar(value="正在初始化...")
            status_label = ttk.Label(main_frame, textvariable=status_var, font=("Arial", 12))
            status_label.pack(pady=10)

            # 按键状态显示
            expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
            # 按键名称映射
            key_names = {
                656: "锁定键",
                657: "SOS键",
                658: "喇叭键",
                659: "档位加",
                660: "智驾键",
                661: "静音键",
                662: "档位减",
                663: "语音键"
            }
            key_vars = {}
            key_labels = {}

            keys_frame = ttk.LabelFrame(main_frame, text="按键状态", padding="10")
            keys_frame.pack(fill=tk.BOTH, expand=True, pady=10)

            for i, key_code in enumerate(expected_keys):
                row = i // 4
                col = i % 4

                key_frame = ttk.Frame(keys_frame)
                key_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")

                key_vars[key_code] = tk.StringVar(value="未测试")

                ttk.Label(key_frame, text=f"{key_names[key_code]}:", font=("Arial", 10)).pack(side=tk.LEFT)

                status_label = ttk.Label(
                    key_frame,
                    textvariable=key_vars[key_code],
                    font=("Arial", 10, "bold"),
                    foreground="gray"
                )
                status_label.pack(side=tk.LEFT, padx=(5, 0))
                key_labels[key_code] = status_label

            for i in range(4):
                keys_frame.columnconfigure(i, weight=1)

            # 提示
            hint_label = ttk.Label(
                main_frame,
                text="请依次按下手柄上的8个功能按键，每个按键按下并松开即可\n包括：锁定键、SOS键、喇叭键、档位加、智驾键、静音键、档位减、语音键",
                font=("Arial", 11),
                justify=tk.CENTER
            )
            hint_label.pack(pady=10)

            test_window.update()

            # 启动evtest
            command = "evtest /dev/input/event5"
            self.log_message(f"执行命令: adb shell {command}")

            process = subprocess.Popen(
                ['adb', 'shell', command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            time.sleep(2)  # 等待初始化
            status_var.set("请开始按键测试...")
            test_window.update()

            # 测试变量
            detected_keys = set()
            key_press_states = {}
            start_time = time.time()
            timeout = 60

            # 输出队列
            output_queue = queue.Queue()

            def read_output():
                try:
                    while True:
                        line = process.stdout.readline()
                        if not line:
                            break
                        output_queue.put(line.strip())
                except Exception as e:
                    output_queue.put(f"ERROR: {str(e)}")

            # 启动读取线程
            threading.Thread(target=read_output, daemon=True).start()

            # 主循环 - 直接在主线程中处理
            while len(detected_keys) < 8 and (time.time() - start_time) < timeout and process.poll() is None:
                # 处理事件队列
                events_processed = 0
                while events_processed < 5:  # 每次处理最多5个事件
                    try:
                        line = output_queue.get_nowait()
                        events_processed += 1
                    except queue.Empty:
                        break

                    if not line or line.startswith("ERROR:"):
                        continue

                    # 解析按键事件
                    if "EV_KEY" in line and "code " in line:
                        try:
                            # 记录原始事件用于调试
                            self.log_message(f"原始事件: {line}")

                            key_code = None
                            value = None

                            # 解析格式: Event: time xxx, type 1 (EV_KEY), code 659 (?), value 0
                            parts = line.split(", ")
                            for part in parts:
                                part = part.strip()
                                if "code " in part:
                                    # 提取 "code 659 (?)" 中的 659
                                    code_match = part.split("code ")[1].split(" ")[0]
                                    key_code = int(code_match)
                                elif "value " in part:
                                    # 提取 "value 0" 中的 0
                                    value = int(part.split("value ")[1])

                            self.log_message(f"解析结果: key_code={key_code}, value={value}")

                            if key_code in expected_keys and value is not None:
                                key_name = key_names.get(key_code, f"按键{key_code}")
                                if value == 1:
                                    # 按键按下
                                    key_press_states[key_code] = True
                                    key_vars[key_code].set("按下")
                                    key_labels[key_code].configure(foreground="orange")
                                    self.log_message(f"✓ 检测到{key_name}({key_code})按下")
                                    # 立即更新UI
                                    test_window.update_idletasks()
                                elif value == 0 and key_press_states.get(key_code, False):
                                    # 按键松开
                                    detected_keys.add(key_code)
                                    key_vars[key_code].set("PASS")
                                    key_labels[key_code].configure(foreground="green")
                                    self.log_message(f"✓ {key_name}({key_code})测试通过")
                                    status_var.set(f"已完成 {len(detected_keys)}/8 个按键测试")
                                    # 立即更新UI
                                    test_window.update_idletasks()
                                elif value == 0:
                                    self.log_message(f"{key_name}({key_code})松开，但未记录按下状态")
                            else:
                                if key_code not in expected_keys:
                                    self.log_message(f"忽略按键{key_code}（不在预期列表中）")

                        except (ValueError, IndexError) as e:
                            self.log_message(f"解析按键事件失败: {str(e)}, 原始行: {line}")
                            continue

                # 更新UI
                test_window.update()
                time.sleep(0.1)

            # 停止进程
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()

            # 显示最终结果
            if len(detected_keys) >= 8:
                status_var.set("测试完成!")
                self.log_message(f"按键测试完成 - 检测到{len(detected_keys)}个按键")
                result = True
            elif (time.time() - start_time) >= timeout:
                status_var.set("测试超时!")
                self.log_message(f"⏰ 按键测试超时 - 1分钟内只检测到{len(detected_keys)}个按键")
                result = False
            else:
                status_var.set(f"测试失败 - 只检测到{len(detected_keys)}/8个按键")
                self.log_message(f"按键测试失败 - 只检测到{len(detected_keys)}个按键")
                result = False

            test_window.update()
            time.sleep(2)  # 显示结果2秒
            test_window.destroy()

            # 设置测试结果
            if result:
                self.test_results[test_project["id"]].message = f"按键测试完成，检测到{len(detected_keys)}个按键"
            else:
                self.test_results[test_project["id"]].message = f"按键测试失败，只检测到{len(detected_keys)}/8个按键"

            return result

        except Exception as e:
            self.log_message(f"按键测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_led_test(self, test_project):
        """LED灯测试
        简化后的LED测试流程：
        1. 执行点亮背光灯命令
        2. 弹出人工确认窗口
        3. 关闭背光灯
        4. 根据确认结果返回测试状态
        """
        try:
            # 记录这是LED灯测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message("开始LED背光灯测试...")
            
            # 执行点亮背光灯命令
            cmd_on = "echo 255 > /sys/class/leds/lock_led/brightness"
            self.log_message(f"执行命令: adb shell {cmd_on}")
            
            result = subprocess.run(['adb', 'shell', cmd_on], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                error_msg = f"LED控制命令执行失败: {result.stderr}"
                self.log_message(error_msg)
                self.test_results[test_project["id"]].message = "LED控制失败"
                return False
                
            self.log_message("LED控制命令执行成功")
            
            # 创建确认对话框 - 使用自定义超时对话框
            confirm = self.show_timeout_confirmation(
                "LED测试确认",
                "请确认：\n1. 背光灯是否已点亮？\n2. 背光灯颜色是否为蓝色？\n\n注意：1分钟内无响应将自动判定为失败"
            )
            
            # 执行关闭背光灯命令
            cmd_off = "echo 0 > /sys/class/leds/lock_led/brightness"
            self.log_message(f"执行命令: adb shell {cmd_off}")

            try:
                off_result = subprocess.run(['adb', 'shell', cmd_off], capture_output=True, text=True, timeout=10)
                if off_result.returncode != 0:
                    self.log_message(f"关闭LED命令执行失败: {off_result.stderr}")
                else:
                    self.log_message("LED灯已关闭")
            except Exception as e:
                self.log_message(f"关闭LED灯时出错: {str(e)}")

            # 处理不同的响应结果，保留人工判断逻辑
            if confirm == "yes":
                self.log_message("✅ LED测试通过 - 用户确认背光灯正常")
                self.test_results[test_project["id"]].message = "有背光"
                return True
            elif confirm == "no":
                self.log_message("❌ LED测试失败 - 用户确认背光灯异常")
                self.test_results[test_project["id"]].message = "无背光"
                return False
            elif confirm == "timeout":
                self.log_message("⏰ LED测试超时失败 - 1分钟内无人工确认")
                self.test_results[test_project["id"]].message = "测试超时，无人工确认"
                return False
            else:
                self.log_message("❌ LED测试失败 - 未知响应")
                self.test_results[test_project["id"]].message = "未知响应"
                return False
                
        except subprocess.TimeoutExpired:
            error_msg = "LED测试超时"
            self.log_message(error_msg)
            self.test_results[test_project["id"]].message = error_msg
            return False
        except Exception as e:
            error_msg = f"LED测试出错: {str(e)}"
            self.log_message(error_msg)
            self.test_results[test_project["id"]].message = error_msg
            return False
            
    def run_joystick_test(self, test_project):
        """摇杆测试"""
        try:
            self.log_message("执行摇杆测试...")
            command = test_project["command"]
            self.log_message(f"执行命令: adb shell {command}")
            
            result = subprocess.run(['adb', 'shell', command], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                value = result.stdout.strip()
                expected = test_project["expected_value"]
                
                self.log_message(f"命令执行成功")
                self.log_message(f"返回数据: {value}")
                
                if value == expected:
                    self.log_message(f"摇杆测试通过，值: {value}")
                    self.test_results[test_project["id"]].message = f"值: {value}"
                    return True
                else:
                    self.log_message(f"摇杆测试失败，期望: {expected}，实际: {value}")
                    self.test_results[test_project["id"]].message = f"期望{expected}，实际{value}"
                    return False
            else:
                self.log_message(f"命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "命令执行失败"
                return False
                
        except Exception as e:
            self.log_message(f"摇杆测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_camera_test(self, test_project):
        """摄像头测试
        优化后的摄像头测试流程：
        1. 显示测试窗口
        2. 执行拍照命令
        3. 拉取图片并在同一窗口中预览
        4. 用户确认测试结果
        """
        try:
            # 记录这是摄像头测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message(f"开始执行{test_project['name']}...")
            
            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title(f"{test_project['name']}")
            test_window.geometry("900x700")
            test_window.attributes('-topmost', True)
            test_window.resizable(False, False)
            
            # 设置窗口居中
            test_window.update_idletasks()
            x = (test_window.winfo_screenwidth() // 2) - (test_window.winfo_width() // 2)
            y = (test_window.winfo_screenheight() // 2) - (test_window.winfo_height() // 2)
            test_window.geometry(f"+{x}+{y}")
            
            # 创建主框架
            main_frame = ttk.Frame(test_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(
                main_frame, 
                text=f"{test_project['name']}", 
                font=("Arial", 16, "bold")
            )
            title_label.pack(pady=(0, 15))
            
            # 状态框架
            status_frame = ttk.LabelFrame(main_frame, text="测试状态", padding="10")
            status_frame.pack(fill=tk.X, pady=(0, 15))
            
            # 状态变量
            status_var = tk.StringVar(value="正在初始化...")
            status_label = ttk.Label(status_frame, textvariable=status_var, font=("Arial", 11))
            status_label.pack(pady=5)
            
            # 进度条
            progress = ttk.Progressbar(status_frame, mode='indeterminate', length=800)
            progress.pack(pady=5, fill=tk.X)
            progress.start()
            
            # 图片显示区域（初始隐藏）
            image_frame = ttk.LabelFrame(main_frame, text="图片预览", padding="10")
            image_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
            image_frame.pack_forget()  # 初始隐藏
            
            # 图片信息变量
            image_info_var = tk.StringVar(value="")
            
            # 结果变量
            result_var = tk.BooleanVar(value=False)
            
            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=(0, 10), fill=tk.X)
            
            # 确认和取消按钮（初始隐藏）
            def confirm_ok():
                result_var.set(True)
                test_window.destroy()
                
            def confirm_fail():
                result_var.set(False)
                test_window.destroy()
            
            # 按钮样式
            style = ttk.Style()
            style.configure("Green.TButton", foreground="dark green")
            style.configure("Red.TButton", foreground="dark red")
            
            # 创建按钮（初始不显示）
            ok_btn = ttk.Button(
                button_frame, 
                text="图片质量正常 (通过)", 
                command=confirm_ok,
                style="Green.TButton"
            )
            
            fail_btn = ttk.Button(
                button_frame, 
                text="图片质量异常 (不通过)", 
                command=confirm_fail,
                style="Red.TButton"
            )
            
            # 更新UI
            test_window.update()
            
            # 执行命令
            for i, cmd in enumerate(test_project["commands"]):
                status_var.set(f"执行命令 {i+1}/{len(test_project['commands'])}...")
                test_window.update()
                
                self.log_message(f"执行命令 {i+1}: adb shell {cmd}")
                result = subprocess.run(['adb', 'shell', cmd], capture_output=True, text=True, timeout=60)
                if result.returncode != 0:
                    test_window.destroy()
                    self.log_message(f"命令执行失败: {result.stderr}")
                    self.test_results[test_project["id"]].message = "拍照失败"
                    return False
                else:
                    self.log_message(f"命令执行成功，返回: {result.stdout.strip() if result.stdout.strip() else '无输出'}")
                    
            # 拉取图片
            output_file = test_project["output_file"]
            
            # 根据配置文件确定正确的路径
            if test_project["id"] == "front_camera_test":
                # 前摄像头图片在 /data/camera/ 目录下
                remote_path = f"/data/camera/{output_file}"
                status_var.set(f"正在获取前摄像头图片...")
            else:
                # 回充摄像头图片在 /data/ 目录下
                remote_path = f"/data/{output_file}"
                status_var.set(f"正在获取回充摄像头图片...")
                
            test_window.update()
            self.log_message(f"执行拉取命令: adb pull {remote_path} .")
            result = subprocess.run(['adb', 'pull', remote_path, '.'], capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                test_window.destroy()
                self.log_message(f"拉取命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "图片拉取失败"
                return False
                
            self.log_message(f"拉取命令执行成功")
            self.log_message(f"返回数据: {result.stdout.strip()}")
            self.log_message(f"图片已保存: {output_file}")
            
            # 停止进度条
            progress.stop()
            
            # 显示图片预览
            status_var.set("图片拉取成功，请确认图片质量")
            
            # 显示图片框架
            image_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
            
            # 添加说明文本
            if test_project["id"] == "front_camera_test":
                instruction_text = "请确认前摄像头拍摄的图片是否清晰、色彩是否正常，无明显失真或模糊。"
            else:
                instruction_text = "请确认回充摄像头拍摄的图片是否清晰、色彩是否正常，无明显失真或模糊。"
                
            instruction_label = ttk.Label(
                image_frame, 
                text=instruction_text,
                font=("Arial", 11),
                wraplength=850
            )
            instruction_label.pack(pady=(0, 10))
            
            # 显示图片
            try:
                # 使用PIL加载图片
                from PIL import Image, ImageTk
                
                # 打开图片
                img = Image.open(output_file)
                
                # 获取图片尺寸
                img_width, img_height = img.size
                
                # 计算缩放比例，适应窗口大小
                window_width = 850
                window_height = 400
                
                # 计算缩放比例
                scale_x = window_width / img_width
                scale_y = window_height / img_height
                scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
                
                # 缩放图片
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)
                img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 转换为PhotoImage
                photo = ImageTk.PhotoImage(img_resized)
                
                # 创建标签显示图片
                image_label = ttk.Label(image_frame, image=photo)
                image_label.image = photo  # 保持引用
                image_label.pack(pady=10)
                
                # 更新图片信息
                image_info_var.set(f"图片信息: {img_width} x {img_height} 像素 | 文件: {output_file}")
                
            except ImportError:
                # 如果没有PIL，显示文件信息
                error_label = ttk.Label(
                    image_frame, 
                    text=f"无法预览图片\n请手动打开文件: {output_file}", 
                    font=("Arial", 12), 
                    foreground="red"
                )
                error_label.pack(expand=True, pady=50)
                
            except Exception as e:
                # 图片加载失败
                error_label = ttk.Label(
                    image_frame, 
                    text=f"图片加载失败: {str(e)}\n文件: {output_file}", 
                    font=("Arial", 12), 
                    foreground="red"
                )
                error_label.pack(expand=True, pady=50)
            
            # 图片信息标签
            info_label = ttk.Label(image_frame, textvariable=image_info_var, font=("Arial", 10))
            info_label.pack(pady=(5, 0))
            
            # 打开文件按钮
            def open_file():
                try:
                    import platform
                    
                    system = platform.system()
                    if system == "Windows":
                        os.startfile(output_file)
                    elif system == "Darwin":  # macOS
                        subprocess.run(["open", output_file])
                    else:  # Linux
                        subprocess.run(["xdg-open", output_file])
                        
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开文件: {str(e)}")
            
            # 显示按钮
            open_btn = ttk.Button(button_frame, text="在默认程序中打开", command=open_file)
            open_btn.pack(side=tk.LEFT, padx=(0, 10))
            
            ok_btn.pack(side=tk.RIGHT)
            fail_btn.pack(side=tk.RIGHT, padx=(0, 10))
            
            # 设置窗口关闭事件 - 默认为不通过
            test_window.protocol("WM_DELETE_WINDOW", confirm_fail)

            # 添加超时处理
            def on_timeout():
                self.log_message("⏰ 摄像头测试超时 - 1分钟内无响应，自动判定为失败")
                result_var.set(False)
                test_window.destroy()

            # 设置1分钟超时
            timeout_id = test_window.after(60000, on_timeout)

            # 等待窗口关闭
            test_window.wait_window()

            # 取消超时定时器
            try:
                test_window.after_cancel(timeout_id)
            except:
                pass
            
            # 返回结果
            test_result = result_var.get()
            self.log_message(f"用户确认结果: {'通过' if test_result else '不通过'}")
            
            if test_result:
                self.test_results[test_project["id"]].message = f"图片质量正常"
                return True
            else:
                self.test_results[test_project["id"]].message = f"图片质量异常"
                return False
                
        except Exception as e:
            self.log_message(f"摄像头测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_light_sensor_test(self, test_project):
        """光感测试
        测试流程：
        1. 启动evtest监听光感数据
        2. 提示用户遮挡光感
        3. 检测光感数值变化
        4. 只要检测到数值变化就判定为正常
        """
        try:
            # 记录这是光感测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message("执行光感测试...")
            
            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title("光感测试")
            test_window.geometry("600x400")
            test_window.attributes('-topmost', True)
            
            # 设置窗口居中
            test_window.update_idletasks()
            x = (test_window.winfo_screenwidth() // 2) - (test_window.winfo_width() // 2)
            y = (test_window.winfo_screenheight() // 2) - (test_window.winfo_height() // 2)
            test_window.geometry(f"+{x}+{y}")
            
            # 创建主框架
            main_frame = ttk.Frame(test_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(
                main_frame, 
                text="光感测试", 
                font=("Arial", 16, "bold")
            )
            title_label.pack(pady=(0, 15))
            
            # 状态框架
            status_frame = ttk.LabelFrame(main_frame, text="测试状态", padding="10")
            status_frame.pack(fill=tk.X, pady=(0, 15))
            
            # 状态变量
            status_var = tk.StringVar(value="正在初始化...")
            status_label = ttk.Label(
                status_frame,
                textvariable=status_var,
                font=("Arial", 12)
            )
            status_label.pack(fill=tk.X, padx=5, pady=5)
            
            # 数值显示框架
            value_frame = ttk.LabelFrame(main_frame, text="光感数值", padding="10")
            value_frame.pack(fill=tk.X, pady=(0, 15))
            
            # 数值变量
            value_var = tk.StringVar(value="等待数据...")
            value_label = ttk.Label(
                value_frame,
                textvariable=value_var,
                font=("Arial", 12)
            )
            value_label.pack(fill=tk.X, padx=5, pady=5)
            
            # 提示框架
            hint_frame = ttk.LabelFrame(main_frame, text="操作提示", padding="10")
            hint_frame.pack(fill=tk.X, pady=(0, 15))
            
            hint_label = ttk.Label(
                hint_frame,
                text="请用手遮挡光感传感器，观察数值变化\n数值变化即表示光感功能正常",
                font=("Arial", 12)
            )
            hint_label.pack(fill=tk.X, padx=5, pady=5)
            
            # 更新界面
            test_window.update()
            
            # 启动evtest监听
            status_var.set("正在启动光感监听...")
            test_window.update()
            
            command = test_project["command"]
            self.log_message(f"执行命令: adb shell {command}")
            
            # 使用subprocess.Popen启动命令，这样可以实时读取输出
            process = subprocess.Popen(
                ['adb', 'shell', command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 初始化变量
            last_value = None
            has_change = False
            start_time = time.time()
            timeout = 60  # 测试超时时间（秒） - 统一为1分钟
            
            # 更新状态
            status_var.set("正在检测光感数据...")
            test_window.update()
            
            # 读取并解析输出
            while True:
                # 检查是否超时
                if time.time() - start_time > timeout:
                    process.terminate()
                    status_var.set("测试超时！")
                    test_window.update()
                    test_window.after(2000, test_window.destroy)
                    self.log_message("⏰ 光感测试超时 - 1分钟内无响应，自动判定为失败")
                    self.test_results[test_project["id"]].message = "测试超时"
                    return False
                
                # 读取一行输出
                line = process.stdout.readline()
                if not line:
                    break
                
                # 解析ABS_MISC事件
                if "ABS_MISC" in line:
                    try:
                        # 提取value值
                        value = int(line.split("value")[-1].strip())
                        
                        # 更新显示
                        value_var.set(f"当前值: {value}")
                        test_window.update()
                        
                        # 检测值变化
                        if last_value is not None and value != last_value:
                            has_change = True
                            process.terminate()
                            status_var.set("检测完成！")
                            test_window.update()
                            test_window.after(2000, test_window.destroy)
                            
                            self.log_message(f"光感测试完成 - 检测到数值变化")
                            self.log_message(f"数值从 {last_value} 变化到 {value}")
                            self.test_results[test_project["id"]].message = "光感正常"
                            return True
                            
                        last_value = value
                            
                    except ValueError:
                        continue
            
            # 如果没有检测到变化
            process.terminate()
            status_var.set("未检测到数值变化！")
            test_window.update()
            test_window.after(2000, test_window.destroy)
            
            self.log_message("光感测试失败 - 未检测到数值变化")
            self.test_results[test_project["id"]].message = "光感异常"
            return False
            
        except Exception as e:
            self.log_message(f"光感测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_speaker_test(self, test_project):
        """喇叭测试"""
        try:
            # 记录这是喇叭测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message("执行喇叭测试...")
            command = test_project["command"]
            self.log_message(f"执行命令: adb shell {command}")
            
            result = subprocess.run(['adb', 'shell', command], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log_message("命令执行成功")
                self.log_message(f"返回数据: {result.stdout.strip() if result.stdout.strip() else '无输出'}")
                self.log_message("音频播放完成")
                self.test_results[test_project["id"]].message = "音频播放完成"
                return True
            else:
                self.log_message(f"命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "播放失败"
                return False
                
        except Exception as e:
            self.log_message(f"喇叭测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_bluetooth_test(self, test_project):
        """蓝牙测试 - 使用bluetoothctl show命令获取Controller信息"""
        try:
            self.log_message("执行蓝牙测试...")
            self.log_message("使用bluetoothctl show命令获取蓝牙控制器信息...")

            # 使用bluetoothctl show命令获取蓝牙控制器信息
            self.log_message("执行命令: adb shell bluetoothctl show")

            result = subprocess.run(['adb', 'shell', 'bluetoothctl', 'show'],
                                  capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                self.log_message("命令执行成功")
                output = result.stdout.strip()

                if output:
                    self.log_message("返回数据:")
                    lines = output.split('\n')
                    for line in lines:
                        if line.strip():
                            self.log_message(f"  {line.strip()}")

                    # 查找Controller行并提取MAC地址
                    # 格式: Controller 24:21:5E:C0:30:F3 (public)
                    controller_mac = None
                    for line in lines:
                        line = line.strip()
                        if line.startswith("Controller "):
                            # 提取MAC地址
                            parts = line.split()
                            if len(parts) >= 2:
                                mac_address = parts[1]
                                # 验证MAC地址格式 (XX:XX:XX:XX:XX:XX)
                                if len(mac_address) == 17 and mac_address.count(':') == 5:
                                    controller_mac = mac_address
                                    break

                    if controller_mac:
                        self.log_message(f"✅ 蓝牙测试成功，检测到蓝牙控制器")
                        self.log_message(f"蓝牙控制器MAC地址: {controller_mac}")
                        self.test_results[test_project["id"]].message = f"MAC: {controller_mac}"
                        return True
                    else:
                        self.log_message("❌ 蓝牙测试失败，未找到有效的蓝牙控制器")
                        self.test_results[test_project["id"]].message = "未找到蓝牙控制器"
                        return False
                else:
                    self.log_message("❌ 蓝牙测试失败，无输出数据")
                    self.test_results[test_project["id"]].message = "无输出数据"
                    return False
            else:
                self.log_message(f"❌ 命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "命令执行失败"
                return False

        except Exception as e:
            self.log_message(f"蓝牙测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_wifi_test(self, test_project):
        """WiFi测试 - 连接WiFi并进行网络发包延时测试"""
        try:
            self.log_message("执行WiFi测试...")
            self.log_message("第一步：连接WiFi网络...")

            # 步骤1：停止现有的wpa_supplicant进程
            self.log_message("停止现有的wpa_supplicant进程...")
            cmd1 = "killall wpa_supplicant 2>/dev/null"
            self.log_message(f"执行命令: adb shell \"{cmd1}\"")
            subprocess.run(['adb', 'shell', cmd1], capture_output=True, text=True, timeout=10)

            # 步骤2：清理wpa_supplicant socket文件
            self.log_message("清理wpa_supplicant socket文件...")
            cmd2 = "rm -f /var/run/wpa_supplicant/wlan0"
            self.log_message(f"执行命令: adb shell \"{cmd2}\"")
            subprocess.run(['adb', 'shell', cmd2], capture_output=True, text=True, timeout=10)

            # 步骤3：关闭wlan0接口
            self.log_message("关闭wlan0接口...")
            cmd3 = "ip link set wlan0 down"
            self.log_message(f"执行命令: adb shell {cmd3}")
            subprocess.run(['adb', 'shell', cmd3], capture_output=True, text=True, timeout=10)

            # 步骤4：连接WiFi网络
            self.log_message("连接WiFi网络...")
            # 从配置中获取WiFi信息，如果没有则使用默认值
            wifi_ssid = self.wifi_settings.get("ssid", "Orion_SZ_5G")
            wifi_password = self.wifi_settings.get("password", "Orion@2025")

            wifi_cmd = (
                f"wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && "
                f"wpa_cli -i wlan0 add_network && "
                f"wpa_cli -i wlan0 set_network 0 ssid '\"'{wifi_ssid}'\"' && "
                f"wpa_cli -i wlan0 set_network 0 psk '\"'{wifi_password}'\"' && "
                f"wpa_cli -i wlan0 enable_network 0 && "
                f"udhcpc -i wlan0 && "
                f"iw wlan0 link && "
                f"ip addr show wlan0"
            )

            self.log_message(f"执行WiFi连接命令...")
            self.log_message(f"SSID: {wifi_ssid}")

            wifi_result = subprocess.run(['adb', 'shell', wifi_cmd],
                                       capture_output=True, text=True, timeout=30)

            if wifi_result.returncode != 0:
                self.log_message(f"❌ WiFi连接失败: {wifi_result.stderr}")
                self.test_results[test_project["id"]].message = "WiFi连接失败"
                return False

            # 显示WiFi连接结果
            self.log_message("WiFi连接命令执行完成，返回数据:")
            wifi_output = wifi_result.stdout.strip()
            if wifi_output:
                lines = wifi_output.split('\n')
                for line in lines:
                    if line.strip():
                        self.log_message(f"  {line.strip()}")

            # 检查连接状态
            if "Connected to" in wifi_output or "inet " in wifi_output:
                self.log_message("✅ WiFi连接成功")
            else:
                self.log_message("⚠️ WiFi连接状态不明确，继续进行网络测试...")

            # 等待网络稳定
            import time
            self.log_message("等待网络稳定...")
            time.sleep(3)

            # 步骤5：进行网络延时测试
            self.log_message("第二步：开始网络发包延时测试...")

            # 执行10秒钟的ping测试来计算平均延时
            ping_command = "ping -c 10 www.baidu.com"
            self.log_message(f"执行命令: adb shell {ping_command}")
            self.log_message("正在进行10秒钟的网络延时测试...")

            ping_result = subprocess.run(['adb', 'shell', ping_command],
                                       capture_output=True, text=True, timeout=20)

            if ping_result.returncode == 0:
                output = ping_result.stdout.strip()
                self.log_message("ping命令执行成功")
                self.log_message("返回数据:")

                # 分行显示输出
                lines = output.split('\n')
                for line in lines:
                    if line.strip():
                        self.log_message(f"  {line.strip()}")

                # 解析延时数据
                import re
                time_pattern = re.compile(r'time=(\d+\.?\d*)\s*ms')
                delays = []

                for line in lines:
                    if "64 bytes from" in line and "time=" in line:
                        match = time_pattern.search(line)
                        if match:
                            delay = float(match.group(1))
                            delays.append(delay)
                            self.log_message(f"检测到延时: {delay} ms")

                if delays:
                    # 计算平均延时
                    avg_delay = sum(delays) / len(delays)
                    min_delay = min(delays)
                    max_delay = max(delays)

                    self.log_message(f"✅ WiFi延时测试成功")
                    self.log_message(f"发包数量: {len(delays)} 个")
                    self.log_message(f"平均延时: {avg_delay:.2f} ms")
                    self.log_message(f"最小延时: {min_delay:.2f} ms")
                    self.log_message(f"最大延时: {max_delay:.2f} ms")

                    # 判断网络质量
                    if avg_delay <= 50:
                        quality = "优秀"
                    elif avg_delay <= 100:
                        quality = "良好"
                    elif avg_delay <= 200:
                        quality = "一般"
                    else:
                        quality = "较差"

                    self.test_results[test_project["id"]].message = f"平均延时: {avg_delay:.2f}ms ({quality})"
                    return True
                else:
                    self.log_message("❌ 未检测到有效的延时数据")
                    self.test_results[test_project["id"]].message = "未检测到延时数据"
                    return False
            else:
                self.log_message(f"❌ ping命令执行失败: {ping_result.stderr}")
                self.test_results[test_project["id"]].message = "网络连接失败"
                return False

        except Exception as e:
            self.log_message(f"WiFi测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False

    def run_torch_test(self, test_project):
        """手电筒LED测试
        测试流程：
        1. 执行点亮手电筒命令
        2. 弹出人工确认窗口
        3. 关闭手电筒
        4. 根据确认结果返回测试状态
        """
        try:
            # 记录这是手电筒测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message("开始手电筒LED测试...")
            
            # 执行点亮手电筒命令
            cmd_on = "echo 255 > /sys/class/leds/torch/brightness"
            self.log_message(f"执行命令: adb shell {cmd_on}")
            
            result = subprocess.run(['adb', 'shell', cmd_on], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                error_msg = f"手电筒控制命令执行失败: {result.stderr}"
                self.log_message(error_msg)
                self.test_results[test_project["id"]].message = "手电筒控制失败"
                return False
                
            self.log_message("手电筒控制命令执行成功")
            
            # 创建确认对话框 - 使用自定义超时对话框
            confirm = self.show_timeout_confirmation(
                "手电筒测试确认",
                "请确认：\n1. 手电筒是否已点亮？\n2. 亮度是否正常？\n\n注意：1分钟内无响应将自动判定为失败"
            )
            
            # 执行关闭手电筒命令
            cmd_off = "echo 0 > /sys/class/leds/torch/brightness"
            self.log_message(f"执行命令: adb shell {cmd_off}")

            try:
                off_result = subprocess.run(['adb', 'shell', cmd_off], capture_output=True, text=True, timeout=10)
                if off_result.returncode != 0:
                    self.log_message(f"关闭手电筒命令执行失败: {off_result.stderr}")
                else:
                    self.log_message("手电筒已关闭")
            except Exception as e:
                self.log_message(f"关闭手电筒时出错: {str(e)}")

            # 处理不同的响应结果，保留人工判断逻辑
            if confirm == "yes":
                self.log_message("✅ 手电筒测试通过 - 用户确认手电筒正常")
                self.test_results[test_project["id"]].message = "手电筒正常"
                return True
            elif confirm == "no":
                self.log_message("❌ 手电筒测试失败 - 用户确认手电筒异常")
                self.test_results[test_project["id"]].message = "手电筒异常"
                return False
            elif confirm == "timeout":
                self.log_message("⏰ 手电筒测试超时失败 - 1分钟内无人工确认")
                self.test_results[test_project["id"]].message = "测试超时，无人工确认"
                return False
            else:
                self.log_message("❌ 手电筒测试失败 - 未知响应")
                self.test_results[test_project["id"]].message = "未知响应"
                return False
                
        except subprocess.TimeoutExpired:
            error_msg = "手电筒测试超时"
            self.log_message(error_msg)
            self.test_results[test_project["id"]].message = error_msg
            return False
        except Exception as e:
            error_msg = f"手电筒测试出错: {str(e)}"
            self.log_message(error_msg)
            self.test_results[test_project["id"]].message = error_msg
            return False

def main():
    root = tk.Tk()
    app = WheelchairTester(root)
    root.mainloop()

if __name__ == "__main__":
    main() 