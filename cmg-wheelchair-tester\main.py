import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
import subprocess
import threading
import json
import datetime
import re
import os
import time
import sys
from typing import List, Dict, Tuple
from PIL import Image, ImageTk
import queue

class TestResult:
    def __init__(self, test_id: str, test_name: str, status: str = "未测试", message: str = ""):
        self.test_id = test_id
        self.test_name = test_name
        self.status = status  # 未测试, 测试中, 通过, 失败
        self.message = message
        self.timestamp = datetime.datetime.now()

class WheelchairTester:
    def __init__(self, root):
        self.root = root
        self.root.title("CMG整机功能测试软件")
        self.root.geometry("1200x950")  # 增加窗口尺寸确保按钮可见
        self.root.minsize(1000, 800)  # 设置最小窗口尺寸

        # 创建菜单栏
        self.create_menu()

        # 设置默认字体和样式
        self.setup_styles()

        # 加载配置文件
        self.config = self.load_config()
        self.test_projects = self.config.get("test_projects", [])
        self.work_processes = self.config.get("work_processes", {})
        self.adb_settings = self.config.get("adb_settings", {})
        self.wifi_settings = self.config.get("wifi_settings", {})

        # 当前选择的工序和序列号
        self.selected_process = None
        self.current_serial_number = None

        # 第一步：选择工序
        if not self.select_work_process():
            self.root.quit()
            return
        
        # 测试结果存储（初始化为空，稍后根据工序填充）
        self.test_results = {}
        
        # 当前测试状态
        self.current_test = None
        self.test_running = False
        self.current_sn = None
        
        # 测试时间相关
        self.start_time = None
        self.end_time = None
        self.timer_running = False
        self.timer_id = None
        
        self.setup_ui()
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        
        # 设置默认字体
        default_font = ("Microsoft YaHei UI", 10)
        self.root.option_add("*Font", default_font)
        
        # 设置全局背景色和前景色
        style.configure(".", 
            background="#ffffff",    # 白色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置标签框架样式
        style.configure("TLabelframe", 
            background="#ffffff",    # 白色背景
            darkcolor="#cccccc",     # 中灰色边框
            lightcolor="#cccccc"     # 中灰色边框
        )
        style.configure("TLabelframe.Label", 
            font=("Microsoft YaHei UI", 11, "bold"),
            background="#ffffff",    # 白色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置按钮基础样式
        style.configure("TButton", 
            padding=(10, 5), 
            font=("Microsoft YaHei UI", 10, "bold"),  # 加粗按钮文字
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        
        # 设置不同类型按钮的样式
        style.configure("Primary.TButton", 
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        style.map("Primary.TButton",
            relief=[('pressed', 'flat')]         # 按下时保持扁平
        )
        
        style.configure("Success.TButton", 
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        style.map("Success.TButton",
            relief=[('pressed', 'flat')]         # 按下时保持扁平
        )
        
        style.configure("Warning.TButton", 
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        style.map("Warning.TButton",
            relief=[('pressed', 'flat')]         # 按下时保持扁平
        )
        
        style.configure("Danger.TButton", 
            borderwidth=0,           # 无边框
            relief="flat"            # 扁平样式
        )
        style.map("Danger.TButton",
            relief=[('pressed', 'flat')]         # 按下时保持扁平
        )
        
        # 设置树形视图样式
        style.configure("Treeview", 
            font=("Microsoft YaHei UI", 10),
            background="#ffffff",    # 白色背景
            fieldbackground="#ffffff",# 白色背景
            foreground="#333333"     # 深灰色文字
        )
        style.configure("Treeview.Heading", 
            font=("Microsoft YaHei UI", 10, "bold"),
            background="#e9ecef",    # 浅灰色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置进度条样式
        style.configure("TProgressbar", 
            thickness=8,
            background="#0d6efd"     # Bootstrap蓝色
        )
        
        # 设置普通标签样式
        style.configure("TLabel", 
            font=("Microsoft YaHei UI", 10),
            background="#ffffff",    # 白色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置状态标签样式
        style.configure("Status.TLabel", 
            font=("Microsoft YaHei UI", 11),
            background="#ffffff",    # 白色背景
            foreground="#333333"     # 深灰色文字
        )
        
        # 设置框架样式
        style.configure("TFrame", 
            background="#ffffff"     # 白色背景
        )
        
    def setup_ui(self):
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20", style="TFrame")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(0, weight=0)  # 工序选择区域
        main_frame.rowconfigure(1, weight=1)  # 测试项目列表可扩展
        main_frame.rowconfigure(2, weight=0)  # 日志区域固定高度
        main_frame.rowconfigure(3, weight=0)  # 按钮区域不扩展

        # 测试信息显示区域
        info_frame = ttk.LabelFrame(main_frame, text="测试信息", padding="10")
        info_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(1, weight=1)

        # 当前工序信息
        ttk.Label(info_frame, text="当前工序:", font=("Microsoft YaHei UI", 10, "bold")).grid(row=0, column=0, padx=(0, 10), sticky=tk.W)

        process_info = f"{self.selected_process}"
        if self.selected_process in self.work_processes:
            desc = self.work_processes[self.selected_process].get("description", "")
            test_count = len(self.work_processes[self.selected_process].get("test_ids", []))
            process_info += f" - {desc} ({test_count}个测试项目)"

        ttk.Label(info_frame, text=process_info, foreground="blue",
                 font=("Microsoft YaHei UI", 10)).grid(row=0, column=1, sticky=tk.W)

        # 当前序列号信息
        ttk.Label(info_frame, text="设备序列号:", font=("Microsoft YaHei UI", 10, "bold")).grid(row=1, column=0, padx=(0, 10), sticky=tk.W, pady=(5, 0))
        ttk.Label(info_frame, text=self.current_serial_number, foreground="green",
                 font=("Microsoft YaHei UI", 10, "bold")).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))

        # 测试项目列表
        test_frame = ttk.LabelFrame(main_frame, text="测试项目", padding="10")
        test_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 5))  # 添加N, S粘性使其可扩展
        test_frame.columnconfigure(0, weight=1)
        test_frame.rowconfigure(0, weight=1)  # 让测试树可以扩展
        
        # 创建测试项目列表
        self.test_tree = ttk.Treeview(
            test_frame,
            columns=("data", "result"),
            show="tree headings",
            selectmode="browse",  # 单选模式
            height=15  # 设置固定高度为15行
        )
        
        self.test_tree.heading("#0", text="测试项目")
        self.test_tree.heading("data", text="测试数据")
        self.test_tree.heading("result", text="测试结果")
        self.test_tree.column("#0", width=250, minwidth=250)
        self.test_tree.column("data", width=200, minwidth=200)
        self.test_tree.column("result", width=100, minwidth=100, anchor="center")  # 结果列居中
        self.test_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))  # 添加N, S粘性使其可扩展
        
        # 滚动条
        test_scrollbar = ttk.Scrollbar(test_frame, orient="vertical", command=self.test_tree.yview)
        test_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.test_tree.configure(yscrollcommand=test_scrollbar.set)
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 5))  # 移除N, S粘性
        log_frame.columnconfigure(0, weight=1)
        
        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=12,  # 进一步减小日志区域高度
            font=("Microsoft YaHei UI", 9)
        )
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E))  # 移除N, S粘性
        
        # 底部按钮区域（包含时间显示和按钮）
        button_frame = ttk.Frame(main_frame, padding=(0, 0))
        button_frame.grid(row=3, column=0, sticky=(tk.W, tk.E))
        button_frame.columnconfigure(1, weight=1)  # 中间空白区域可扩展
        
        # 左侧时间显示区域
        time_frame = ttk.Frame(button_frame)
        time_frame.grid(row=0, column=0, sticky=tk.W)
        
        # 开始时间标签
        ttk.Label(time_frame, text="开始时间: ").grid(row=0, column=0, padx=(0, 5))
        self.start_time_label = ttk.Label(time_frame, text="--:--:--")
        self.start_time_label.grid(row=0, column=1, padx=(0, 15))
        
        # 结束时间标签
        ttk.Label(time_frame, text="结束时间: ").grid(row=0, column=2, padx=(0, 5))
        self.end_time_label = ttk.Label(time_frame, text="--:--:--")
        self.end_time_label.grid(row=0, column=3, padx=(0, 15))
        
        # 总用时标签
        ttk.Label(time_frame, text="总用时: ").grid(row=0, column=4, padx=(0, 5))
        self.total_time_label = ttk.Label(time_frame, text="--:--:--")
        self.total_time_label.grid(row=0, column=5, padx=(0, 15))
        
        # 右侧按钮区域
        control_frame = ttk.Frame(button_frame)
        control_frame.grid(row=0, column=2, sticky=tk.E)
        
        # 开始测试按钮
        self.start_all_btn = ttk.Button(
            control_frame,
            text="开始测试",
            command=self.start_all_tests,
            style="Success.TButton",
            width=10,
            padding=(10, 2)
        )
        self.start_all_btn.pack(side=tk.LEFT, padx=5)
        
        # 停止测试按钮
        self.stop_btn = ttk.Button(
            control_frame,
            text="停止测试",
            command=self.stop_tests,
            state="disabled",
            style="Danger.TButton",
            width=10,
            padding=(10, 2)
        )
        self.stop_btn.pack(side=tk.LEFT, padx=(5, 5))

        # 配置管理按钮
        self.config_btn = ttk.Button(
            control_frame,
            text="配置管理",
            command=self.open_config_manager,
            style="Info.TButton",
            width=10,
            padding=(10, 2)
        )
        self.config_btn.pack(side=tk.LEFT, padx=(0, 0))

        # 初始化测试项目和结果
        self.init_test_projects()
        self.init_test_tree()

        # 程序初始化完成
        self.log_message("程序初始化完成")

        # 第二步：输入序列号（输入后会自动开始测试）
        if not self.input_serial_number():
            self.root.quit()
            return

        # 创建右键菜单
        self.test_menu = tk.Menu(self.root, tearoff=0)
        self.test_menu.add_command(label="运行测试", command=self.run_single_test)
        self.test_menu.add_command(label="查看详情", command=self.show_test_details)
        
        # 绑定右键菜单
        self.test_tree.bind("<Button-3>", self.show_test_menu)  # 右键点击
        self.test_tree.bind("<Double-Button-1>", self.run_single_test)  # 双击运行测试

        # 添加单击事件（可选，用于调试）
        # self.test_tree.bind("<Button-1>", self.on_tree_click)  # 单击事件
        
        # 状态标签（移到类属性中，供其他方法使用）
        self.status_label = ttk.Label(
            self.root,
            text="未连接",
            style="Status.TLabel"
        )
        
        # 更新状态标签的颜色设置方法
        def update_status_color(self, is_connected=False):
            if is_connected:
                self.status_label.configure(foreground="#198754")  # Bootstrap绿色
            else:
                self.status_label.configure(foreground="#dc3545")  # Bootstrap红色
        
        # 将更新方法添加到类中
        self.update_status_color = update_status_color.__get__(self)
        
        # 初始状态为未连接
        self.update_status_color(False)

    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导出测试结果", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)

        # 配置菜单
        config_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="配置", menu=config_menu)
        config_menu.add_command(label="选择工序", command=self.reselect_work_process)
        config_menu.add_separator()
        config_menu.add_command(label="配置管理器", command=self.open_config_manager)
        config_menu.add_command(label="快速配置", command=self.open_quick_config)
        config_menu.add_separator()
        config_menu.add_command(label="重新加载配置", command=self.reload_config_from_menu)
        config_menu.add_command(label="验证配置", command=self.validate_config)

        # 工具菜单
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="工具", menu=tools_menu)
        tools_menu.add_command(label="ADB设备检查", command=self.check_adb_devices)
        tools_menu.add_command(label="清空日志", command=self.clear_log)
        tools_menu.add_separator()
        tools_menu.add_command(label="测试连接", command=self.test_connection)

        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="配置管理帮助", command=self.show_config_help)
        help_menu.add_separator()
        help_menu.add_command(label="关于", command=self.show_about)

    def init_test_tree(self):
        """初始化测试项目列表"""
        # 设置结果列的标签样式
        self.test_tree.tag_configure("pass_result", foreground="#28a745", font=("Microsoft YaHei UI", 10, "bold"))  # 深绿色加粗
        self.test_tree.tag_configure("fail_result", foreground="#dc3545", font=("Microsoft YaHei UI", 10, "bold"))  # 深红色加粗
        self.test_tree.tag_configure("testing_result", foreground="#ffc107")  # 深黄色
        
        # 获取现有项目
        existing_items = self.test_tree.get_children()
        
        # 获取过滤后的测试项目
        filtered_projects = self.get_filtered_test_projects()

        # 创建或更新测试项目列表
        for i, project in enumerate(filtered_projects):
            result = self.test_results[project["id"]]
            
            # 如果项目已存在，更新它
            if i < len(existing_items):
                item = existing_items[i]
                self.test_tree.item(item, text=project["name"])
            else:
                # 如果是新项目，创建它
                item = self.test_tree.insert("", "end", text=project["name"])
            
            # 更新测试数据和结果
            current_data = self.test_tree.set(item, "data")
            current_result = self.test_tree.set(item, "result")
            current_tags = self.test_tree.item(item, "tags")
            
            # 准备新的数据
            new_data = result.message if result.message else ""
            new_result = ""
            new_tags = ()
            
            if result.status == "通过":
                new_result = "PASS"
                new_tags = ("pass_result",)
            elif result.status == "失败":
                new_result = "FAIL"
                new_tags = ("fail_result",)
            elif result.status == "测试中":
                new_result = "测试中"
                new_tags = ("testing_result",)
            
            # 只在值发生变化时更新
            if current_data != new_data:
                self.test_tree.set(item, "data", new_data)
            if current_result != new_result:
                self.test_tree.set(item, "result", new_result)
            if current_tags != new_tags:
                self.test_tree.item(item, tags=new_tags)
        
        # 删除多余的项目
        if len(existing_items) > len(filtered_projects):
            for item in existing_items[len(filtered_projects):]:
                self.test_tree.delete(item)



    def get_filtered_test_projects(self):
        """根据选择的工序过滤测试项目"""
        if self.selected_process not in self.work_processes:
            return self.test_projects

        selected_test_ids = self.work_processes[self.selected_process]["test_ids"]
        filtered_projects = []

        for project in self.test_projects:
            if project["id"] in selected_test_ids:
                filtered_projects.append(project)

        return filtered_projects

    def init_test_projects(self):
        """初始化测试项目结果（基于当前选择的工序）"""
        # 获取过滤后的测试项目
        filtered_projects = self.get_filtered_test_projects()

        # 清空现有测试结果
        self.test_results.clear()

        # 为过滤后的项目创建测试结果
        for project in filtered_projects:
            self.test_results[project["id"]] = TestResult(
                project["id"],
                project["name"]
            )

        self.log_message(f"已初始化 {len(filtered_projects)} 个测试项目")

    def update_test_item(self, test_id: str, status: str = None, message: str = None):
        """更新单个测试项目的状态（线程安全）"""
        # 检查是否在主线程中
        try:
            self._update_test_item_main_thread(test_id, status, message)
        except RuntimeError:
            # 如果在子线程中，使用after方法调度到主线程
            self.root.after(0, lambda: self._update_test_item_main_thread(test_id, status, message))

    def _update_test_item_main_thread(self, test_id: str, status: str = None, message: str = None):
        """在主线程中更新测试项目状态"""
        # 查找对应的测试项目
        for item in self.test_tree.get_children():
            project_name = self.test_tree.item(item, "text")
            for project in self.test_projects:
                if project["name"] == project_name and project["id"] == test_id:
                    result = self.test_results[test_id]

                    # 更新状态
                    if status is not None:
                        result.status = status
                    if message is not None:
                        result.message = message

                    # 更新显示
                    if message is not None:
                        self.test_tree.set(item, "data", message)

                    if status is not None:
                        if status == "通过":
                            self.test_tree.set(item, "result", "PASS")
                            self.test_tree.item(item, tags=("pass_result",))
                        elif status == "失败":
                            self.test_tree.set(item, "result", "FAIL")
                            self.test_tree.item(item, tags=("fail_result",))
                        elif status == "测试中":
                            self.test_tree.set(item, "result", "测试中")
                            self.test_tree.item(item, tags=("testing_result",))
                        else:
                            self.test_tree.set(item, "result", "")
                            self.test_tree.item(item, tags=())
                    break
                    
    def show_sn_input_dialog(self):
        """显示SN输入对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("扫描序列号")
        dialog.geometry("450x250")  # 调整窗口大小
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        # 创建主框架
        main_frame = ttk.Frame(dialog, padding="25")  # 增加内边距
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 提示标签
        ttk.Label(
            main_frame,
            text="请扫描产品序列号：",
            font=("Microsoft YaHei UI", 12, "bold")  # 加粗字体
        ).pack(pady=(0, 15))
        
        # SN输入框
        sn_var = tk.StringVar()
        sn_entry = ttk.Entry(
            main_frame,
            textvariable=sn_var,
            font=("Microsoft YaHei UI", 12),
            width=30
        )
        sn_entry.pack(pady=(0, 25))  # 增加间距
        sn_entry.focus()
        
        # 错误提示标签
        error_var = tk.StringVar()
        error_label = ttk.Label(
            main_frame,
            textvariable=error_var,
            foreground="#dc3545",  # 使用Bootstrap的危险色
            font=("Microsoft YaHei UI", 10)
        )
        error_label.pack(pady=(0, 15))
        
        # 结果变量
        result = {"sn": None}
        
        def validate_and_save():
            sn = sn_var.get().strip()
            if not sn:
                error_var.set("SN不能为空！")
                return
            if not sn.isalnum():
                error_var.set("SN只能包含字母和数字！")
                return
            if len(sn) < 4:
                error_var.set("SN长度不能小于4位！")
                return
            result["sn"] = sn
            dialog.destroy()
            
        def on_cancel():
            dialog.destroy()
            
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=(0, 10))
        
        # 确定按钮
        ttk.Button(
            button_frame,
            text="确定",
            command=validate_and_save,
            style="Success.TButton",
            width=12
        ).pack(side=tk.LEFT, padx=10)
        
        # 取消按钮
        ttk.Button(
            button_frame,
            text="取消",
            command=on_cancel,
            style="Danger.TButton",
            width=12
        ).pack(side=tk.LEFT, padx=10)
        
        # 绑定回车键
        dialog.bind("<Return>", lambda e: validate_and_save())
        dialog.bind("<Escape>", lambda e: on_cancel())

        # 设置1分钟超时
        timeout_id = dialog.after(60000, lambda: self.handle_dialog_timeout(dialog, result, "SN输入"))

        # 等待窗口关闭
        dialog.wait_window()

        # 取消超时定时器
        try:
            dialog.after_cancel(timeout_id)
        except:
            pass

        return result["sn"]

    def handle_dialog_timeout(self, dialog, result, test_name):
        """处理弹窗超时"""
        self.log_message(f"⏰ {test_name}超时 - 1分钟内无响应，自动关闭")
        if hasattr(result, '__setitem__'):
            result["timeout"] = True
        dialog.destroy()

    def show_timeout_confirmation(self, title, message):
        """显示带超时的确认对话框"""
        self.log_message(f"🔧 显示确认对话框: {title}")

        # 创建自定义对话框
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("500x300")  # 增加窗口大小
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        self.log_message("🔧 对话框窗口已创建")

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        result = {"answer": None, "timeout": False}

        # 主框架
        main_frame = ttk.Frame(dialog, padding="30")  # 增加内边距
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 消息文本
        message_label = ttk.Label(
            main_frame,
            text=message,
            justify=tk.CENTER,
            wraplength=400,
            font=("Microsoft YaHei UI", 11)  # 设置字体
        )
        message_label.pack(pady=(0, 25))

        # 倒计时显示
        countdown_var = tk.StringVar(value="剩余时间: 60秒")
        countdown_label = ttk.Label(
            main_frame,
            textvariable=countdown_var,
            foreground="red",
            font=("Microsoft YaHei UI", 12, "bold")  # 加粗字体
        )
        countdown_label.pack(pady=(0, 25))

        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(pady=10)

        def on_yes():
            result["answer"] = "yes"
            self.log_message("👤 用户选择: 是 (测试通过)")
            dialog.destroy()

        def on_no():
            result["answer"] = "no"
            self.log_message("👤 用户选择: 否 (测试失败)")
            dialog.destroy()

        def on_timeout():
            result["timeout"] = True
            result["answer"] = "timeout"  # 超时状态，区别于用户主动选择
            self.log_message("⏰ 确认对话框超时 - 1分钟内无响应，自动判定为失败")
            dialog.destroy()

        # 按钮 - 增加按钮大小和样式
        yes_button = ttk.Button(
            button_frame,
            text="是 (通过)",
            command=on_yes,
            width=15
        )
        yes_button.pack(side=tk.LEFT, padx=(0, 20))
        self.log_message("🔧 '是'按钮已创建")

        no_button = ttk.Button(
            button_frame,
            text="否 (失败)",
            command=on_no,
            width=15
        )
        no_button.pack(side=tk.LEFT)
        self.log_message("🔧 '否'按钮已创建")

        # 倒计时更新
        remaining_time = [60]  # 使用列表以便在嵌套函数中修改

        def update_countdown():
            if remaining_time[0] > 0:
                countdown_var.set(f"剩余时间: {remaining_time[0]}秒")
                remaining_time[0] -= 1
                dialog.after(1000, update_countdown)
            else:
                on_timeout()

        # 开始倒计时
        update_countdown()

        # 绑定键盘事件
        dialog.bind("<Return>", lambda e: on_yes())
        dialog.bind("<Escape>", lambda e: on_no())

        # 强制更新显示
        dialog.update()
        self.log_message("🔧 对话框显示完成，等待用户响应...")

        # 等待窗口关闭
        dialog.wait_window()

        self.log_message(f"🔧 对话框关闭，用户响应: {result['answer']}")
        return result["answer"]

    def load_config(self) -> Dict:
        """加载配置文件"""
        try:
            with open('config.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            messagebox.showerror("配置错误", "配置文件config.json不存在")
            return {}
        except Exception as e:
            messagebox.showerror("配置错误", f"加载配置文件失败: {str(e)}")
            return {}
        
    def show_test_menu(self, event):
        """显示测试项目右键菜单"""
        item = self.test_tree.selection()
        if item:
            self.test_menu.post(event.x_root, event.y_root)

    def on_tree_click(self, event):
        """测试树单击事件（用于调试）"""
        self.log_message("🔧 单击事件触发")
        item = self.test_tree.selection()
        if item:
            test_name = self.test_tree.item(item[0], "text")
            self.log_message(f"🔧 单击选中项目: {test_name}")
        else:
            self.log_message("🔧 单击但没有选中项目")

    def debug_test_tree_state(self):
        """调试并修复测试树状态"""
        try:
            # 检查事件绑定状态
            if hasattr(self, 'test_tree'):
                bindings = self.test_tree.bind()
                if not bindings:  # 如果事件绑定丢失
                    self.log_message("🔧 检测到事件绑定丢失，正在修复...")
                    # 重新绑定关键事件
                    self.test_tree.bind("<Button-3>", self.show_test_menu)
                    self.test_tree.bind("<Double-Button-1>", self.run_single_test)
                    self.log_message("🔧 事件绑定已修复")

        except Exception as e:
            self.log_message(f"🔧 修复测试树状态时出错: {str(e)}")

    def run_single_test(self, event=None):
        """运行单个测试"""

        item = self.test_tree.selection()
        if not item:
            return

        test_name = self.test_tree.item(item[0], "text")
        self.log_message(f"开始单项测试: {test_name}")

        test_project = None
        for project in self.test_projects:
            if project["name"] == test_name:
                test_project = project
                break

        if test_project:
            # 确保test_results中有对应的项目
            if test_project["id"] not in self.test_results:
                self.test_results[test_project["id"]] = TestResult(
                    test_project["id"],
                    test_project["name"]
                )

            # 更新UI状态为测试中
            self.test_tree.set(item[0], "data", "")
            self.test_tree.set(item[0], "result", "测试中")
            self.test_tree.item(item[0], tags=("testing_result",))  # 设置测试中样式

            # 执行测试
            result = self.run_test(test_project)

            # 更新结果
            self.test_results[test_project["id"]].status = "通过" if result else "失败"
            self.test_results[test_project["id"]].timestamp = datetime.datetime.now()

            # 更新UI
            result_text = "PASS" if result else "FAIL"
            self.test_tree.set(item[0], "data", self.test_results[test_project["id"]].message)
            self.test_tree.set(item[0], "result", result_text)

            # 设置结果列的标签和背景色
            if result:
                self.test_tree.item(item[0], tags=("pass_result",))  # 设置通过样式
                self.log_message(f"{test_name} - 单项测试通过 ✅")
            else:
                self.test_tree.item(item[0], tags=("fail_result",))  # 设置失败样式
                self.log_message(f"{test_name} - 单项测试失败 ❌")
        else:
            self.log_message(f"❌ 未找到测试项目: {test_name}")
                
    def show_test_details(self):
        """显示测试详情"""
        item = self.test_tree.selection()
        if not item:
            return
            
        test_name = self.test_tree.item(item[0], "text")
        test_project = None
        for project in self.test_projects:
            if project["name"] == test_name:
                test_project = project
                break
                
        if test_project:
            self.show_test_info(test_project)
            
    def show_test_info(self, test_project):
        """显示测试项目信息"""
        info_window = tk.Toplevel(self.root)
        info_window.title(f"测试详情 - {test_project['name']}")
        info_window.geometry("600x400")
        
        text_widget = scrolledtext.ScrolledText(info_window)
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        info_text = f"""测试项目: {test_project['name']}
描述: {test_project['description']}
类型: {test_project.get('type', '未知')}

"""
        
        if 'command' in test_project:
            info_text += f"命令: {test_project['command']}\n"
        elif 'commands' in test_project:
            info_text += "命令列表:\n"
            for i, cmd in enumerate(test_project['commands']):
                info_text += f"  {i+1}. {cmd}\n"
                
        if 'expected_devices' in test_project:
            info_text += "\n预期设备:\n"
            for device in test_project['expected_devices']:
                info_text += f"  Bus {device['bus']} Device {device['device']}: ID {device['id']}\n"
                
        if 'expected_patterns' in test_project:
            info_text += f"\n预期模式: {', '.join(test_project['expected_patterns'])}\n"
            
        if 'expected_value' in test_project:
            info_text += f"\n预期值: {test_project['expected_value']}\n"
            
        text_widget.insert(tk.END, info_text)
        text_widget.config(state=tk.DISABLED)
        
    def log_message(self, message: str):
        """添加日志消息（线程安全）"""
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 检查是否在主线程中
        try:
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            self.root.update_idletasks()
        except RuntimeError:
            # 如果在子线程中，使用after方法调度到主线程
            self.root.after(0, lambda: self._log_message_main_thread(log_entry))

    def _log_message_main_thread(self, log_entry):
        """在主线程中执行日志消息添加"""
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def _clear_log_silent(self):
        """静默清空日志（无确认弹窗）"""
        self.log_text.delete(1.0, tk.END)
        self.log_message("=== 开始新的测试 ===")

    def _clear_test_data_only(self):
        """只清空测试数据，不清空日志"""
        # 重置测试结果
        for test_id in self.test_results:
            self.test_results[test_id].status = "未测试"
            self.test_results[test_id].message = ""
            self.test_results[test_id].details = ""

        # 更新显示
        self.init_test_tree()

        # 重置时间显示
        self.start_time = None
        self.end_time = None
        self.timer_running = False
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
        self.start_time_label.config(text="--:--:--")
        self.end_time_label.config(text="--:--:--")
        self.total_time_label.config(text="00:00:00")
        
    def save_log(self):
        """保存日志到文件"""
        try:
            # 创建logs目录（如果不存在）
            if not os.path.exists("logs"):
                os.makedirs("logs")
            
            # 生成日志文件名（使用当前时间和SN号）
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            sn = self.current_sn if self.current_sn else "unknown"
            filename = f"logs/test_log_{sn}_{timestamp}.txt"
            
            # 保存日志
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
            
            self.log_message(f"日志已保存到: {filename}")
            
        except Exception as e:
            self.log_message(f"保存日志失败: {str(e)}")
        
    def export_results(self):
        """导出测试结果"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("JSON文件", "*.json"), ("所有文件", "*.*")]
        )
        if filename:
            try:
                results = {}
                for test_id, result in self.test_results.items():
                    results[test_id] = {
                        "test_name": result.test_name,
                        "status": result.status,
                        "message": result.message,
                        "timestamp": result.timestamp.isoformat()
                    }
                    
                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(results, f, indent=4, ensure_ascii=False)
                messagebox.showinfo("成功", f"测试结果已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出结果失败: {str(e)}")
        
    def check_connection(self):
        """检测ADB连接状态"""
        def check():
            try:
                self.log_message("正在检测ADB连接...")
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                    connected_devices = [d for d in devices if d.strip() and 'device' in d]
                    
                    if connected_devices:
                        self.status_label.config(text=f"已连接 ({len(connected_devices)} 个设备)")
                        self.update_status_color(True)  # 使用统一的颜色设置方法
                        self.log_message(f"ADB连接成功，检测到 {len(connected_devices)} 个设备")
                        for device in connected_devices:
                            self.log_message(f"设备: {device}")
                    else:
                        self.status_label.config(text="未连接设备")
                        self.update_status_color(False)  # 使用统一的颜色设置方法
                        self.log_message("ADB连接成功，但未检测到设备")
                else:
                    self.status_label.config(text="连接失败")
                    self.update_status_color(False)  # 使用统一的颜色设置方法
                    self.log_message(f"ADB连接失败: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                self.status_label.config(text="连接超时")
                self.update_status_color(False)  # 使用统一的颜色设置方法
                self.log_message("ADB连接超时")
            except FileNotFoundError:
                self.status_label.config(text="ADB未安装")
                self.update_status_color(False)  # 使用统一的颜色设置方法
                self.log_message("错误: ADB未安装或不在PATH中")
            except Exception as e:
                self.status_label.config(text="连接错误")
                self.update_status_color(False)  # 使用统一的颜色设置方法
                self.log_message(f"连接检测出错: {str(e)}")
                
        threading.Thread(target=check, daemon=True).start()
        
    def start_all_tests(self):
        """开始所有测试"""
        if not self.test_running:
            # 检查是否已选择工序
            if not self.selected_process:
                self.log_message("请先选择工序")
                messagebox.showwarning("提示", "请先通过菜单选择工序")
                return

            # 检查是否已输入序列号，如果没有则弹出输入对话框
            if not self.current_serial_number:
                self.log_message("需要输入序列号，弹出序列号输入对话框")
                if not self.input_serial_number():
                    # 用户取消输入序列号
                    self.log_message("用户取消序列号输入")
                    return
                # 序列号输入成功后会自动调用start_all_tests，所以这里直接返回
                return

            # 自动清除之前的测试数据和日志（无确认弹窗）
            self.clear_test_data()
            self._clear_log_silent()  # 使用静默清空日志

            self.test_running = True
            self.start_all_btn.config(state="disabled")
            self.stop_btn.config(state="normal")

            # 使用已经输入的序列号
            self.current_sn = self.current_serial_number
            self.log_message(f"开始测试")
            self.log_message(f"工序: {self.selected_process}")
            self.log_message(f"序列号: {self.current_serial_number}")

            # 记录开始时间
            self.start_time = datetime.datetime.now()
            self.start_time_label.config(text=self.start_time.strftime("%H:%M:%S"))
            self.end_time_label.config(text="--:--:--")
            self.total_time_label.config(text="00:00:00")

            # 启动计时器
            self.timer_running = True
            self.update_total_time()

            # 在新线程中运行测试
            threading.Thread(target=self.run_all_tests, daemon=True).start()
            
    def run_all_tests(self):
        """运行所有测试的具体实现"""
        try:
            # 获取过滤后的测试项目
            filtered_projects = self.get_filtered_test_projects()
            total_tests = len(filtered_projects)
            passed_tests = 0

            self.log_message(f"开始运行 {self.selected_process} 工序测试，共 {total_tests} 个项目")

            for project in filtered_projects:
                if not self.test_running:
                    break
                    
                self.current_test = project
                self.test_results[project["id"]].status = "测试中"
                self.test_results[project["id"]].timestamp = datetime.datetime.now()
                self.init_test_tree()  # 更新显示
                
                self.log_message(f"\n开始执行: {project['name']}")
                result = self.run_test(project)
                
                if result:
                    self.test_results[project["id"]].status = "通过"
                    passed_tests += 1
                else:
                    self.test_results[project["id"]].status = "失败"
                
                self.init_test_tree()  # 更新显示
                
            if self.test_running:
                self.log_message(f"\n测试完成 - 通过率: {passed_tests}/{total_tests}")
                if passed_tests == total_tests:
                    self.log_message("✅ 所有测试通过！")
                else:
                    self.log_message("❌ 存在测试失败项！")
                
                # 记录结束时间
                self.timer_running = False
                if self.timer_id:
                    self.root.after_cancel(self.timer_id)
                    self.timer_id = None
                self.end_time = datetime.datetime.now()
                self.end_time_label.config(text=self.end_time.strftime("%H:%M:%S"))
                self.update_total_time()
                
                # 保存测试记录和日志
                self.save_test_record()
                self.save_log()

                # 测试完成后直接回到序列号输入，重新开始测试
                self.root.after(1000, self.restart_test_cycle)

        except Exception as e:
            self.log_message(f"测试过程出错: {str(e)}")
            
        finally:
            self.test_running = False
            self.current_test = None
            self.start_all_btn.config(state="normal")
            self.stop_btn.config(state="disabled")
        
    def save_test_record(self):
        """保存测试记录"""
        if not self.current_sn:
            return
            
        try:
            # 创建records目录（如果不存在）
            os.makedirs("records", exist_ok=True)
            
            # 生成文件名：SN_日期时间
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"records/{self.current_sn}_{timestamp}.json"
            
            # 准备测试结果数据
            results = {
                "sn": self.current_sn,
                "timestamp": datetime.datetime.now().isoformat(),
                "results": {}
            }
            
            for test_id, result in self.test_results.items():
                results["results"][test_id] = {
                    "test_name": result.test_name,
                    "status": result.status,
                    "message": result.message,
                    "timestamp": result.timestamp.isoformat()
                }
                
            # 保存测试记录
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=4, ensure_ascii=False)
                
            self.log_message(f"测试记录已保存: {filename}")
            
            # 同时保存日志文件
            log_filename = f"records/{self.current_sn}_{timestamp}.log"
            with open(log_filename, 'w', encoding='utf-8') as f:
                f.write(self.log_text.get(1.0, tk.END))
                
            self.log_message(f"测试日志已保存: {log_filename}")
            
        except Exception as e:
            self.log_message(f"保存测试记录失败: {str(e)}")
            messagebox.showerror("错误", f"保存测试记录失败: {str(e)}")
        
    def clear_test_data(self):
        """清除所有测试数据"""
        # 重新初始化测试项目（基于当前工序）
        self.init_test_projects()
            
        # 更新显示
        self.init_test_tree()
        
        # 清除日志（静默）
        self._clear_log_silent()
        
        # 重置时间显示
        self.start_time = None
        self.end_time = None
        self.timer_running = False
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
        self.start_time_label.config(text="--:--:--")
        self.end_time_label.config(text="--:--:--")
        self.total_time_label.config(text="--:--:--")
        
        # 重置按钮状态
        self.start_all_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        
        self.log_message("测试数据已清除")
        
    def stop_tests(self):
        """停止测试"""
        self.test_running = False
        self.timer_running = False
        if self.timer_id:
            self.root.after_cancel(self.timer_id)
            self.timer_id = None
        self.log_message("正在停止测试...")
        
        # 记录结束时间
        self.end_time = datetime.datetime.now()
        self.end_time_label.config(text=self.end_time.strftime("%H:%M:%S"))
        self.update_total_time()
        
    def update_total_time(self):
        """更新总用时显示"""
        if self.start_time:
            current_time = datetime.datetime.now() if self.timer_running else self.end_time
            if current_time:
                elapsed = current_time - self.start_time
                hours = elapsed.seconds // 3600
                minutes = (elapsed.seconds % 3600) // 60
                seconds = elapsed.seconds % 60
                self.total_time_label.config(text=f"{hours:02d}:{minutes:02d}:{seconds:02d}")
                
                if self.timer_running:
                    self.timer_id = self.root.after(1000, self.update_total_time)
        
    def run_test(self, test_project):
        """执行测试项目"""
        try:
            # 在主线程中更新状态为测试中
            self.root.after(0, lambda: self.update_test_item(test_project["id"], "测试中"))
            
            if test_project["type"] == "connection_test":
                # 执行连接状态测试
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
                    connected_devices = [d for d in devices if d.strip() and 'device' in d]
                    
                    if connected_devices:
                        message = f"已连接 {len(connected_devices)} 个设备"
                        self.root.after(0, lambda: self.update_test_item(test_project["id"], "通过", message))
                        self.root.after(0, lambda: self.log_message(f"设备连接测试通过，检测到 {len(connected_devices)} 个设备"))
                        for device in connected_devices:
                            self.root.after(0, lambda d=device: self.log_message(f"设备: {d}"))
                        return True
                    else:
                        self.root.after(0, lambda: self.update_test_item(test_project["id"], "失败", "未连接设备"))
                        self.root.after(0, lambda: self.log_message("设备连接测试失败：未检测到设备"))
                        return False
                else:
                    self.root.after(0, lambda: self.update_test_item(test_project["id"], "失败", "ADB连接失败"))
                    self.root.after(0, lambda: self.log_message(f"设备连接测试失败：{result.stderr}"))
                    return False
                    
            elif test_project["type"] == "rom_version_test":
                return self.run_rom_version_test(test_project)
            elif test_project["type"] == "usb_test":
                return self.run_usb_test(test_project)
            elif test_project["type"] == "can_test":
                return self.run_can_test(test_project)
            elif test_project["type"] == "gps_test":
                return self.run_gps_test(test_project)
            elif test_project["type"] == "4g_test":
                return self.run_4g_test(test_project)
            elif test_project["type"] == "key_test":
                return self.run_key_test(test_project)
            elif test_project["type"] == "led_test":
                return self.run_led_test(test_project)
            elif test_project["type"] == "joystick_test":
                return self.run_joystick_test(test_project)
            elif test_project["type"] == "camera_test":
                return self.run_camera_test(test_project)
            elif test_project["type"] == "light_sensor_test":
                return self.run_light_sensor_test(test_project)
            elif test_project["type"] == "speaker_test":
                return self.run_speaker_test(test_project)
            elif test_project["type"] == "bluetooth_test":
                return self.run_bluetooth_test(test_project)
            elif test_project["type"] == "4g_network_test":
                return self.run_4g_network_test(test_project)
            elif test_project["type"] == "wifi_test":
                return self.run_wifi_test(test_project)
            elif test_project["type"] == "sensor_fps_test":
                return self.run_sensor_fps_test(test_project)
            elif test_project["type"] == "torch_test":
                return self.run_torch_test(test_project)
            else:
                self.update_test_item(test_project["id"], "失败", "未知的测试类型")
                self.log_message(f"未知的测试类型：{test_project['type']}")
                return False
                
        except Exception as e:
            self.update_test_item(test_project["id"], "失败", f"测试出错: {str(e)}")
            self.log_message(f"测试执行出错: {str(e)}")
            return False
            
    def run_usb_test(self, test_project):
        """USB设备测试"""
        try:
            self.log_message("执行USB设备检测...")
            command = "lsusb"
            self.log_message(f"执行命令: adb shell {command}")
            
            result = subprocess.run(['adb', 'shell', command], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log_message("设备返回数据:")
                self.log_message(result.stdout)
                
                # 解析设备
                devices = self.parse_lsusb_output(result.stdout)
                expected_devices = test_project.get("expected_devices", [])
                
                # 比较设备
                passed, issues = self.compare_devices(devices, expected_devices)
                
                if passed:
                    device_count = len(devices)
                    self.log_message("✅ USB设备检测通过")
                    self.test_results[test_project["id"]].message = f"检测到{device_count}个USB设备"
                    return True
                else:
                    device_count = len(devices)
                    self.log_message("❌ USB设备检测失败")
                    for issue in issues:
                        self.log_message(f"问题: {issue}")
                    self.test_results[test_project["id"]].message = f"检测到{device_count}个设备; " + "; ".join(issues)
                    return False
            else:
                self.log_message(f"命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "命令执行失败"
                return False
                
        except Exception as e:
            self.log_message(f"USB测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False

    def run_rom_version_test(self, test_project):
        """3568版本测试 - 读取ROM版本号"""
        try:
            self.log_message("执行3568版本测试...")
            self.log_message("读取ROM版本号...")

            # 执行uname -a命令获取系统信息
            command = "uname -a"
            self.log_message(f"执行命令: adb shell {command}")

            result = subprocess.run(['adb', 'shell', command],
                                  capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                output = result.stdout.strip()
                self.log_message("uname命令执行成功")
                self.log_message(f"返回数据: {output}")

                # 解析版本信息
                # 示例输出: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
                # 需要截取: Jul 9 12:01:12
                import re

                # 匹配日期时间模式: 月份 日期 时:分:秒
                # 支持格式: Wed Jul 9 12:01:12 CST 2025 或 Jul 9 12:01:12
                date_pattern = re.compile(r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})')
                match = date_pattern.search(output)

                if match:
                    version_info = match.group(1)
                    self.log_message(f"✅ 3568版本测试成功")
                    self.log_message(f"ROM版本号: {version_info}")

                    # 检查是否包含rk3568标识
                    if "rk3568" in output.lower():
                        self.log_message("✅ 确认为RK3568平台")
                        platform_info = "RK3568"
                    else:
                        self.log_message("⚠️ 未检测到RK3568标识")
                        platform_info = "未知平台"

                    self.test_results[test_project["id"]].message = f"{platform_info} - {version_info}"
                    return True
                else:
                    self.log_message("❌ 无法解析版本信息")
                    self.log_message("未找到有效的日期时间格式")
                    self.test_results[test_project["id"]].message = "版本信息解析失败"
                    return False
            else:
                self.log_message(f"❌ uname命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "命令执行失败"
                return False

        except Exception as e:
            self.log_message(f"3568版本测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False

    def parse_lsusb_output(self, output: str) -> List[Dict]:
        """解析lsusb输出"""
        devices = []
        lines = output.strip().split('\n')
        
        for line in lines:
            if line.strip():
                # 处理可能带时间戳的行，如: [2025-07-03 16:19:28] Bus 005 Device 001: ID 1d6b:0001
                # 先尝试匹配带时间戳的格式
                match = re.match(r'\[.*?\]\s*Bus (\d+) Device (\d+): ID ([a-f0-9:]+)', line)
                if not match:
                    # 如果没有时间戳，匹配标准格式
                    match = re.match(r'Bus (\d+) Device (\d+): ID ([a-f0-9:]+)', line)
                
                if match:
                    devices.append({
                        "bus": match.group(1),
                        "device": match.group(2),
                        "id": match.group(3)
                    })
                    self.log_message(f"解析到设备: Bus {match.group(1)} Device {match.group(2)} ID {match.group(3)}")
                    
        self.log_message(f"总共解析到 {len(devices)} 个设备")
        return devices
        
    def compare_devices(self, detected_devices: List[Dict], expected_devices: List[Dict]) -> Tuple[bool, List[str]]:
        """比较检测到的设备与预期设备"""
        issues = []
        passed = True
        
        # 只比较设备ID，不严格比较设备号（因为设备号可能会变化）
        detected_ids = [device["id"] for device in detected_devices]
        expected_ids = [device["id"] for device in expected_devices]
        
        # 检查是否有缺失的设备ID
        missing_ids = []
        for expected_id in expected_ids:
            if expected_id not in detected_ids:
                missing_ids.append(expected_id)
                issues.append(f"缺少设备ID: {expected_id}")
                passed = False
        
        # 检查是否有额外的设备ID
        extra_ids = []
        for detected_id in detected_ids:
            if detected_id not in expected_ids:
                extra_ids.append(detected_id)
        
        # 记录详细信息
        if missing_ids:
            self.log_message(f"缺失的设备ID: {missing_ids}")
        if extra_ids:
            self.log_message(f"额外的设备ID: {extra_ids}")
            
        # 如果所有预期的设备ID都找到了，就认为测试通过
        if not missing_ids:
            self.log_message(f"✅ 所有预期的设备ID都已找到")
            self.log_message(f"检测到的设备: {detected_ids}")
            self.log_message(f"预期的设备: {expected_ids}")
                
        return passed, issues
        
    def run_can_test(self, test_project):
        """CAN0测试，严格按照手动流程自动化"""
        import queue
        self.log_message("执行CAN0测试流程...")
        output_queue = queue.Queue()
        listen_timeout = 5  # 监听最大秒数
        send_frame = "1234ABCD#aa.00.cc.aa.55.66.aa.ff"
        send_id = "1234ABCD"
        send_data = "AA 00 CC AA 55 66 AA FF"
        can_pattern = re.compile(r'can0\s+([A-Fa-f0-9]+)\s+\[\d+\]\s+([A-Fa-f0-9 ]+)')

        # 1. 关闭CAN接口
        down_cmd = ["adb", "shell", "ip", "link", "set", "can0", "down"]
        self.log_message(f"执行命令: {' '.join(down_cmd)}")
        result = subprocess.run(down_cmd, capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            self.log_message(f"CAN0 down失败: {result.stderr}")
            self.test_results[test_project["id"]].message = f"CAN0 down失败: {result.stderr}"
            return False
        self.log_message("CAN0已关闭")

        # 2. 启动CAN接口
        up_cmd = ["adb", "shell", "ip", "link", "set", "can0", "up", "type", "can", "bitrate", "500000", "loopback", "on"]
        self.log_message(f"执行命令: {' '.join(up_cmd)}")
        result = subprocess.run(up_cmd, capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            self.log_message(f"CAN0 up失败: {result.stderr}")
            self.test_results[test_project["id"]].message = f"CAN0 up失败: {result.stderr}"
            return False
        self.log_message("CAN0已启动")

        # 3. 启动监听线程
        def listen_can(q, stop_event):
            try:
                proc = subprocess.Popen(["adb", "shell", "candump", "can0"], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                while not stop_event.is_set():
                    line = proc.stdout.readline()
                    if not line:
                        break
                    q.put(line)
                proc.terminate()
            except Exception as e:
                self.log_message(f"CAN监听线程异常: {str(e)}")
                q.put(f"[ERROR]{str(e)}")

        stop_event = threading.Event()
        listener = threading.Thread(target=listen_can, args=(output_queue, stop_event), daemon=True)
        listener.start()
        self.log_message("CAN监听线程已启动...")
        time.sleep(1)  # 等待监听就绪

        # 4. 发送测试数据
        send_cmd = ["adb", "shell", "cansend", "can0", send_frame]
        self.log_message(f"执行命令: {' '.join(send_cmd)}")
        send_result = subprocess.run(send_cmd, capture_output=True, text=True, timeout=5)
        if send_result.returncode != 0:
            self.log_message(f"CAN数据发送失败: {send_result.stderr}")
            self.test_results[test_project["id"]].message = f"CAN数据发送失败: {send_result.stderr}"
            stop_event.set()
            listener.join(timeout=2)
            return False
        self.log_message("CAN测试数据已发送，等待监听返回...")

        # 5. 采集监听数据
        output = ""
        can_data = None
        start_time = time.time()
        while time.time() - start_time < listen_timeout:
            while not output_queue.empty():
                line = output_queue.get()
                output += line
                self.log_message(f"CAN监听输出: {line.strip()}")
                match = can_pattern.search(line)
                if match:
                    can_id = match.group(1).upper()
                    can_payload = ' '.join(match.group(2).split()).upper()
                    can_data = f"ID: {can_id} DATA: {can_payload}"
                    # 判断是否与发送内容一致
                    if can_id == send_id and can_payload == send_data:
                        stop_event.set()
                        listener.join(timeout=2)
                        self.log_message(f"✅ CAN测试成功，接收到数据: {can_data}")
                        self.test_results[test_project["id"]].message = f"接收到: {can_data}"
                        return True
            time.sleep(0.1)
        stop_event.set()
        listener.join(timeout=2)
        # 未匹配到
        if can_data:
            self.log_message(f"❌ CAN测试数据不一致，接收到: {can_data}")
            self.test_results[test_project["id"]].message = f"接收到: {can_data}"
        else:
            self.log_message("❌ 未检测到CAN数据")
            self.test_results[test_project["id"]].message = "未检测到CAN数据"
        return False
            
    def run_gps_test(self, test_project):
        """GPS测试"""
        try:
            self.log_message("执行GPS测试...")
            
            for i, cmd in enumerate(test_project["commands"]):
                self.log_message(f"执行命令 {i+1}: adb shell {cmd}")
                result = subprocess.run(['adb', 'shell', cmd], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    if result.stdout.strip():
                        self.log_message(f"GPS信号检测成功")
                        self.log_message(f"返回数据: {result.stdout.strip()}")
                        self.test_results[test_project["id"]].message = "GPS信号正常"
                        return True
                    else:
                        self.log_message(f"命令执行成功，但无GPS信号数据")
                else:
                    self.log_message(f"命令执行失败: {result.stderr}")
                    
            self.log_message("GPS信号检测失败")
            self.test_results[test_project["id"]].message = "无GPS信号"
            return False
            
        except Exception as e:
            self.log_message(f"GPS测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_4g_test(self, test_project):
        """4G模组版本测试（获取模组型号信息）"""
        self.log_message("执行4G模组版本测试...（获取模组型号）")
        output_queue = queue.Queue()
        listen_timeout = 10  # 监听最大秒数
        # 修改正则表达式以匹配Revision行中的模组型号
        revision_pattern = re.compile(r'Revision:\s*([A-Z0-9]+)', re.IGNORECASE)
        
        def listen_serial(q, stop_event):
            try:
                proc = subprocess.Popen(['adb', 'shell', 'cat', '/dev/ttyUSB0'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
                while not stop_event.is_set():
                    line = proc.stdout.readline()
                    if not line:
                        break
                    q.put(line)
                proc.terminate()
            except Exception as e:
                self.log_message(f"监听线程异常: {str(e)}")
                q.put(f"[ERROR]{str(e)}")

        # 1. 启动监听线程
        stop_event = threading.Event()
        listener = threading.Thread(target=listen_serial, args=(output_queue, stop_event), daemon=True)
        listener.start()
        self.log_message("监听线程已启动，等待串口数据...")
        time.sleep(2)  # 等待监听就绪

        # 2. 发送ATI指令获取模组版本信息
        send_cmd = 'adb shell "echo -e \'ATI\\r\' > /dev/ttyUSB0"'
        self.log_message(f"执行命令: {send_cmd}")
        try:
            send_result = subprocess.run(send_cmd, shell=True, capture_output=True, text=True, timeout=5)
            if send_result.returncode != 0:
                self.log_message(f"ATI指令发送失败: {send_result.stderr}")
                self.test_results[test_project["id"]].message = f"ATI指令发送失败: {send_result.stderr}"
                stop_event.set()
                return False
            else:
                self.log_message("ATI指令已发送，等待串口返回模组版本信息...")
        except Exception as e:
            self.log_message(f"ATI指令发送异常: {str(e)}")
            self.test_results[test_project["id"]].message = f"ATI指令发送异常: {str(e)}"
            stop_event.set()
            return False

        # 3. 主进程等待串口输出，提取模组版本信息
        output = ""
        module_version = None
        start_time = time.time()
        while time.time() - start_time < listen_timeout:
            try:
                while not output_queue.empty():
                    line = output_queue.get()
                    output += line
                    self.log_message(f"串口输出: {line.strip()}")
                    # 查找Revision行并提取模组型号
                    match = revision_pattern.search(line)
                    if match:
                        module_version = match.group(1)
                        self.log_message(f"检测到模组版本: {module_version}")
                        break
                if module_version:
                    break
                time.sleep(0.2)
            except Exception as e:
                self.log_message(f"串口读取异常: {str(e)}")
                break
        stop_event.set()
        listener.join(timeout=2)

        # 4. 结果处理
        if module_version:
            self.log_message(f"✅ 4G模组版本测试成功，{module_version}")
            # 在测试数据列显示模组型号
            self.test_results[test_project["id"]].message = module_version
            return True
        else:
            self.log_message("❌ 未检测到模组版本信息，请检查4G模组或手动测试")
            self.log_message("完整串口输出:")
            if output:
                for line in output.split('\n'):
                    if line.strip():
                        self.log_message(f"  {line.strip()}")
            self.test_results[test_project["id"]].message = "未检测到"
            return False
            
    def run_key_test(self, test_project):
        """按键测试 - 简化版本，专注于UI更新
        测试流程：
        1. 执行 adb shell evtest /dev/input/event5
        2. 监听按键事件，检测8个按键的状态变化
        3. 按键事件码：656, 657, 658, 659, 660, 661, 662, 663
        4. 每个按键需要检测到 value 1->0 的变化
        """
        try:
            # 记录这是按键测试，已经在本函数中进行了手动确认
            test_project["_manual_check_done"] = True

            self.log_message("开始按键测试...")

            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title("按键测试")
            test_window.geometry("600x400")
            test_window.resizable(False, False)
            test_window.transient(self.root)
            test_window.grab_set()

            # 主框架
            main_frame = ttk.Frame(test_window, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # 标题
            title_label = ttk.Label(main_frame, text="按键测试", font=("Arial", 16, "bold"))
            title_label.pack(pady=(0, 20))

            # 状态显示
            status_var = tk.StringVar(value="正在初始化...")
            status_label = ttk.Label(main_frame, textvariable=status_var, font=("Arial", 12))
            status_label.pack(pady=10)

            # 按键状态显示
            expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
            # 按键名称映射
            key_names = {
                656: "锁定键",
                657: "SOS键",
                658: "喇叭键",
                659: "档位加",
                660: "智驾键",
                661: "静音键",
                662: "档位减",
                663: "语音键"
            }
            key_vars = {}
            key_labels = {}

            keys_frame = ttk.LabelFrame(main_frame, text="按键状态", padding="10")
            keys_frame.pack(fill=tk.BOTH, expand=True, pady=10)

            for i, key_code in enumerate(expected_keys):
                row = i // 4
                col = i % 4

                key_frame = ttk.Frame(keys_frame)
                key_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")

                key_vars[key_code] = tk.StringVar(value="未测试")

                ttk.Label(key_frame, text=f"{key_names[key_code]}:", font=("Arial", 10)).pack(side=tk.LEFT)

                status_label = ttk.Label(
                    key_frame,
                    textvariable=key_vars[key_code],
                    font=("Arial", 10, "bold"),
                    foreground="gray"
                )
                status_label.pack(side=tk.LEFT, padx=(5, 0))
                key_labels[key_code] = status_label

            for i in range(4):
                keys_frame.columnconfigure(i, weight=1)

            # 提示
            hint_label = ttk.Label(
                main_frame,
                text="请依次按下手柄上的8个功能按键，每个按键按下并松开即可\n包括：锁定键、SOS键、喇叭键、档位加、智驾键、静音键、档位减、语音键",
                font=("Arial", 11),
                justify=tk.CENTER
            )
            hint_label.pack(pady=10)

            test_window.update()

            # 启动evtest
            command = "evtest /dev/input/event5"
            self.log_message(f"执行命令: adb shell {command}")

            process = subprocess.Popen(
                ['adb', 'shell', command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            time.sleep(2)  # 等待初始化
            status_var.set("请开始按键测试...")
            test_window.update()

            # 测试变量
            detected_keys = set()
            key_press_states = {}
            start_time = time.time()
            timeout = 60

            # 输出队列
            output_queue = queue.Queue()

            def read_output():
                try:
                    while True:
                        line = process.stdout.readline()
                        if not line:
                            break
                        output_queue.put(line.strip())
                except Exception as e:
                    output_queue.put(f"ERROR: {str(e)}")

            # 启动读取线程
            threading.Thread(target=read_output, daemon=True).start()

            # 主循环 - 直接在主线程中处理
            while len(detected_keys) < 8 and (time.time() - start_time) < timeout and process.poll() is None:
                # 处理事件队列
                events_processed = 0
                while events_processed < 5:  # 每次处理最多5个事件
                    try:
                        line = output_queue.get_nowait()
                        events_processed += 1
                    except queue.Empty:
                        break

                    if not line or line.startswith("ERROR:"):
                        continue

                    # 解析按键事件
                    if "EV_KEY" in line and "code " in line:
                        try:
                            # 记录原始事件用于调试
                            self.log_message(f"原始事件: {line}")

                            key_code = None
                            value = None

                            # 解析格式: Event: time xxx, type 1 (EV_KEY), code 659 (?), value 0
                            parts = line.split(", ")
                            for part in parts:
                                part = part.strip()
                                if "code " in part:
                                    # 提取 "code 659 (?)" 中的 659
                                    code_match = part.split("code ")[1].split(" ")[0]
                                    key_code = int(code_match)
                                elif "value " in part:
                                    # 提取 "value 0" 中的 0
                                    value = int(part.split("value ")[1])

                            self.log_message(f"解析结果: key_code={key_code}, value={value}")

                            if key_code in expected_keys and value is not None:
                                key_name = key_names.get(key_code, f"按键{key_code}")
                                if value == 1:
                                    # 按键按下
                                    key_press_states[key_code] = True
                                    key_vars[key_code].set("按下")
                                    key_labels[key_code].configure(foreground="orange")
                                    self.log_message(f"✓ 检测到{key_name}({key_code})按下")
                                    # 立即更新UI
                                    test_window.update_idletasks()
                                elif value == 0 and key_press_states.get(key_code, False):
                                    # 按键松开
                                    detected_keys.add(key_code)
                                    key_vars[key_code].set("PASS")
                                    key_labels[key_code].configure(foreground="green")
                                    self.log_message(f"✓ {key_name}({key_code})测试通过")
                                    status_var.set(f"已完成 {len(detected_keys)}/8 个按键测试")
                                    # 立即更新UI
                                    test_window.update_idletasks()
                                elif value == 0:
                                    self.log_message(f"{key_name}({key_code})松开，但未记录按下状态")
                            else:
                                if key_code not in expected_keys:
                                    self.log_message(f"忽略按键{key_code}（不在预期列表中）")

                        except (ValueError, IndexError) as e:
                            self.log_message(f"解析按键事件失败: {str(e)}, 原始行: {line}")
                            continue

                # 更新UI
                test_window.update()
                time.sleep(0.1)

            # 停止进程
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()

            # 显示最终结果
            if len(detected_keys) >= 8:
                status_var.set("测试完成!")
                self.log_message(f"按键测试完成 - 检测到{len(detected_keys)}个按键")
                result = True
            elif (time.time() - start_time) >= timeout:
                status_var.set("测试超时!")
                self.log_message(f"⏰ 按键测试超时 - 1分钟内只检测到{len(detected_keys)}个按键")
                result = False
            else:
                status_var.set(f"测试失败 - 只检测到{len(detected_keys)}/8个按键")
                self.log_message(f"按键测试失败 - 只检测到{len(detected_keys)}个按键")
                result = False

            test_window.update()
            time.sleep(2)  # 显示结果2秒
            test_window.destroy()

            # 设置测试结果
            if result:
                self.test_results[test_project["id"]].message = f"按键测试完成，检测到{len(detected_keys)}个按键"
            else:
                self.test_results[test_project["id"]].message = f"按键测试失败，只检测到{len(detected_keys)}/8个按键"

            return result

        except Exception as e:
            self.log_message(f"按键测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_led_test(self, test_project):
        """LED灯测试
        简化后的LED测试流程：
        1. 执行点亮背光灯命令
        2. 弹出人工确认窗口
        3. 关闭背光灯
        4. 根据确认结果返回测试状态
        """
        try:
            # 记录这是LED灯测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message("开始LED背光灯测试...")
            
            # 执行点亮背光灯命令
            cmd_on = "echo 255 > /sys/class/leds/lock_led/brightness"
            self.log_message(f"执行命令: adb shell {cmd_on}")
            
            result = subprocess.run(['adb', 'shell', cmd_on], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                error_msg = f"LED控制命令执行失败: {result.stderr}"
                self.log_message(error_msg)
                self.test_results[test_project["id"]].message = "LED控制失败"
                return False
                
            self.log_message("LED控制命令执行成功")
            
            # 创建确认对话框 - 使用自定义超时对话框
            confirm = self.show_timeout_confirmation(
                "LED测试确认",
                "请确认：\n1. 背光灯是否已点亮？\n2. 背光灯颜色是否为蓝色？\n\n注意：1分钟内无响应将自动判定为失败"
            )
            
            # 执行关闭背光灯命令
            cmd_off = "echo 0 > /sys/class/leds/lock_led/brightness"
            self.log_message(f"执行命令: adb shell {cmd_off}")

            try:
                off_result = subprocess.run(['adb', 'shell', cmd_off], capture_output=True, text=True, timeout=10)
                if off_result.returncode != 0:
                    self.log_message(f"关闭LED命令执行失败: {off_result.stderr}")
                else:
                    self.log_message("LED灯已关闭")
            except Exception as e:
                self.log_message(f"关闭LED灯时出错: {str(e)}")

            # 处理不同的响应结果，保留人工判断逻辑
            if confirm == "yes":
                self.log_message("✅ LED测试通过 - 用户确认背光灯正常")
                self.test_results[test_project["id"]].message = "有背光"
                return True
            elif confirm == "no":
                self.log_message("❌ LED测试失败 - 用户确认背光灯异常")
                self.test_results[test_project["id"]].message = "无背光"
                return False
            elif confirm == "timeout":
                self.log_message("⏰ LED测试超时失败 - 1分钟内无人工确认")
                self.test_results[test_project["id"]].message = "测试超时，无人工确认"
                return False
            else:
                self.log_message("❌ LED测试失败 - 未知响应")
                self.test_results[test_project["id"]].message = "未知响应"
                return False
                
        except subprocess.TimeoutExpired:
            error_msg = "LED测试超时"
            self.log_message(error_msg)
            self.test_results[test_project["id"]].message = error_msg
            return False
        except Exception as e:
            error_msg = f"LED测试出错: {str(e)}"
            self.log_message(error_msg)
            self.test_results[test_project["id"]].message = error_msg
            return False
            
    def run_joystick_test(self, test_project):
        """摇杆测试"""
        try:
            self.log_message("执行摇杆测试...")
            command = test_project["command"]
            self.log_message(f"执行命令: adb shell {command}")
            
            result = subprocess.run(['adb', 'shell', command], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                value = result.stdout.strip()
                expected = test_project["expected_value"]
                
                self.log_message(f"命令执行成功")
                self.log_message(f"返回数据: {value}")
                
                if value == expected:
                    self.log_message(f"摇杆测试通过，值: {value}")
                    self.test_results[test_project["id"]].message = f"值: {value}"
                    return True
                else:
                    self.log_message(f"摇杆测试失败，期望: {expected}，实际: {value}")
                    self.test_results[test_project["id"]].message = f"期望{expected}，实际{value}"
                    return False
            else:
                self.log_message(f"命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "命令执行失败"
                return False
                
        except Exception as e:
            self.log_message(f"摇杆测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_camera_test(self, test_project):
        """摄像头测试
        优化后的摄像头测试流程：
        1. 显示测试窗口
        2. 执行拍照命令
        3. 拉取图片并在同一窗口中预览
        4. 用户确认测试结果
        """
        try:
            # 记录这是摄像头测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message(f"开始执行{test_project['name']}...")
            
            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title(f"{test_project['name']}")
            test_window.geometry("900x700")
            test_window.attributes('-topmost', True)
            test_window.resizable(False, False)
            
            # 设置窗口居中
            test_window.update_idletasks()
            x = (test_window.winfo_screenwidth() // 2) - (test_window.winfo_width() // 2)
            y = (test_window.winfo_screenheight() // 2) - (test_window.winfo_height() // 2)
            test_window.geometry(f"+{x}+{y}")
            
            # 创建主框架
            main_frame = ttk.Frame(test_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(
                main_frame, 
                text=f"{test_project['name']}", 
                font=("Arial", 16, "bold")
            )
            title_label.pack(pady=(0, 15))
            
            # 状态框架
            status_frame = ttk.LabelFrame(main_frame, text="测试状态", padding="10")
            status_frame.pack(fill=tk.X, pady=(0, 15))
            
            # 状态变量
            status_var = tk.StringVar(value="正在初始化...")
            status_label = ttk.Label(status_frame, textvariable=status_var, font=("Arial", 11))
            status_label.pack(pady=5)
            
            # 进度条
            progress = ttk.Progressbar(status_frame, mode='indeterminate', length=800)
            progress.pack(pady=5, fill=tk.X)
            progress.start()
            
            # 图片显示区域（初始隐藏）
            image_frame = ttk.LabelFrame(main_frame, text="图片预览", padding="10")
            image_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
            image_frame.pack_forget()  # 初始隐藏
            
            # 图片信息变量
            image_info_var = tk.StringVar(value="")
            
            # 结果变量
            result_var = tk.BooleanVar(value=False)
            
            # 按钮区域
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=(0, 10), fill=tk.X)
            
            # 确认和取消按钮（初始隐藏）
            def confirm_ok():
                result_var.set(True)
                test_window.destroy()
                
            def confirm_fail():
                result_var.set(False)
                test_window.destroy()
            
            # 按钮样式
            style = ttk.Style()
            style.configure("Green.TButton", foreground="dark green")
            style.configure("Red.TButton", foreground="dark red")
            
            # 创建按钮（初始不显示）
            ok_btn = ttk.Button(
                button_frame, 
                text="图片质量正常 (通过)", 
                command=confirm_ok,
                style="Green.TButton"
            )
            
            fail_btn = ttk.Button(
                button_frame, 
                text="图片质量异常 (不通过)", 
                command=confirm_fail,
                style="Red.TButton"
            )
            
            # 更新UI
            test_window.update()
            
            # 执行命令
            for i, cmd in enumerate(test_project["commands"]):
                status_var.set(f"执行命令 {i+1}/{len(test_project['commands'])}...")
                test_window.update()
                
                self.log_message(f"执行命令 {i+1}: adb shell {cmd}")
                result = subprocess.run(['adb', 'shell', cmd], capture_output=True, text=True, timeout=60)
                if result.returncode != 0:
                    test_window.destroy()
                    self.log_message(f"命令执行失败: {result.stderr}")
                    self.test_results[test_project["id"]].message = "拍照失败"
                    return False
                else:
                    self.log_message(f"命令执行成功，返回: {result.stdout.strip() if result.stdout.strip() else '无输出'}")
                    
            # 拉取图片
            output_file = test_project["output_file"]
            
            # 根据配置文件确定正确的路径
            if test_project["id"] == "front_camera_test":
                # 前摄像头图片在 /data/camera/ 目录下
                remote_path = f"/data/camera/{output_file}"
                status_var.set(f"正在获取前摄像头图片...")
            else:
                # 回充摄像头图片在 /data/ 目录下
                remote_path = f"/data/{output_file}"
                status_var.set(f"正在获取回充摄像头图片...")
                
            test_window.update()
            self.log_message(f"执行拉取命令: adb pull {remote_path} .")
            result = subprocess.run(['adb', 'pull', remote_path, '.'], capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                test_window.destroy()
                self.log_message(f"拉取命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "图片拉取失败"
                return False
                
            self.log_message(f"拉取命令执行成功")
            self.log_message(f"返回数据: {result.stdout.strip()}")
            self.log_message(f"图片已保存: {output_file}")
            
            # 停止进度条
            progress.stop()
            
            # 显示图片预览
            status_var.set("图片拉取成功，请确认图片质量")
            
            # 显示图片框架
            image_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
            
            # 添加说明文本
            if test_project["id"] == "front_camera_test":
                instruction_text = "请确认前摄像头拍摄的图片是否清晰、色彩是否正常，无明显失真或模糊。"
            else:
                instruction_text = "请确认回充摄像头拍摄的图片是否清晰、色彩是否正常，无明显失真或模糊。"
                
            instruction_label = ttk.Label(
                image_frame, 
                text=instruction_text,
                font=("Arial", 11),
                wraplength=850
            )
            instruction_label.pack(pady=(0, 10))
            
            # 显示图片
            try:
                # 使用PIL加载图片
                from PIL import Image, ImageTk
                
                # 打开图片
                img = Image.open(output_file)
                
                # 获取图片尺寸
                img_width, img_height = img.size
                
                # 计算缩放比例，适应窗口大小
                window_width = 850
                window_height = 400
                
                # 计算缩放比例
                scale_x = window_width / img_width
                scale_y = window_height / img_height
                scale = min(scale_x, scale_y, 1.0)  # 不放大，只缩小
                
                # 缩放图片
                new_width = int(img_width * scale)
                new_height = int(img_height * scale)
                img_resized = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
                
                # 转换为PhotoImage
                photo = ImageTk.PhotoImage(img_resized)
                
                # 创建标签显示图片
                image_label = ttk.Label(image_frame, image=photo)
                image_label.image = photo  # 保持引用
                image_label.pack(pady=10)
                
                # 更新图片信息
                image_info_var.set(f"图片信息: {img_width} x {img_height} 像素 | 文件: {output_file}")
                
            except ImportError:
                # 如果没有PIL，显示文件信息
                error_label = ttk.Label(
                    image_frame, 
                    text=f"无法预览图片\n请手动打开文件: {output_file}", 
                    font=("Arial", 12), 
                    foreground="red"
                )
                error_label.pack(expand=True, pady=50)
                
            except Exception as e:
                # 图片加载失败
                error_label = ttk.Label(
                    image_frame, 
                    text=f"图片加载失败: {str(e)}\n文件: {output_file}", 
                    font=("Arial", 12), 
                    foreground="red"
                )
                error_label.pack(expand=True, pady=50)
            
            # 图片信息标签
            info_label = ttk.Label(image_frame, textvariable=image_info_var, font=("Arial", 10))
            info_label.pack(pady=(5, 0))
            
            # 打开文件按钮
            def open_file():
                try:
                    import platform
                    
                    system = platform.system()
                    if system == "Windows":
                        os.startfile(output_file)
                    elif system == "Darwin":  # macOS
                        subprocess.run(["open", output_file])
                    else:  # Linux
                        subprocess.run(["xdg-open", output_file])
                        
                except Exception as e:
                    messagebox.showerror("错误", f"无法打开文件: {str(e)}")
            
            # 显示按钮
            open_btn = ttk.Button(button_frame, text="在默认程序中打开", command=open_file)
            open_btn.pack(side=tk.LEFT, padx=(0, 10))
            
            ok_btn.pack(side=tk.RIGHT)
            fail_btn.pack(side=tk.RIGHT, padx=(0, 10))
            
            # 设置窗口关闭事件 - 默认为不通过
            test_window.protocol("WM_DELETE_WINDOW", confirm_fail)

            # 添加超时处理
            def on_timeout():
                self.log_message("⏰ 摄像头测试超时 - 1分钟内无响应，自动判定为失败")
                result_var.set(False)
                test_window.destroy()

            # 设置1分钟超时
            timeout_id = test_window.after(60000, on_timeout)

            # 等待窗口关闭
            test_window.wait_window()

            # 取消超时定时器
            try:
                test_window.after_cancel(timeout_id)
            except:
                pass
            
            # 返回结果
            test_result = result_var.get()
            self.log_message(f"用户确认结果: {'通过' if test_result else '不通过'}")
            
            if test_result:
                self.test_results[test_project["id"]].message = f"图片质量正常"
                return True
            else:
                self.test_results[test_project["id"]].message = f"图片质量异常"
                return False
                
        except Exception as e:
            self.log_message(f"摄像头测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_light_sensor_test(self, test_project):
        """光感测试
        测试流程：
        1. 启动evtest监听光感数据
        2. 提示用户遮挡光感
        3. 检测光感数值变化
        4. 只要检测到数值变化就判定为正常
        """
        try:
            # 记录这是光感测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message("执行光感测试...")
            
            # 创建测试窗口
            test_window = tk.Toplevel(self.root)
            test_window.title("光感测试")
            test_window.geometry("600x400")
            test_window.attributes('-topmost', True)
            
            # 设置窗口居中
            test_window.update_idletasks()
            x = (test_window.winfo_screenwidth() // 2) - (test_window.winfo_width() // 2)
            y = (test_window.winfo_screenheight() // 2) - (test_window.winfo_height() // 2)
            test_window.geometry(f"+{x}+{y}")
            
            # 创建主框架
            main_frame = ttk.Frame(test_window, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # 标题
            title_label = ttk.Label(
                main_frame, 
                text="光感测试", 
                font=("Arial", 16, "bold")
            )
            title_label.pack(pady=(0, 15))
            
            # 状态框架
            status_frame = ttk.LabelFrame(main_frame, text="测试状态", padding="10")
            status_frame.pack(fill=tk.X, pady=(0, 15))
            
            # 状态变量
            status_var = tk.StringVar(value="正在初始化...")
            status_label = ttk.Label(
                status_frame,
                textvariable=status_var,
                font=("Arial", 12)
            )
            status_label.pack(fill=tk.X, padx=5, pady=5)
            
            # 数值显示框架
            value_frame = ttk.LabelFrame(main_frame, text="光感数值", padding="10")
            value_frame.pack(fill=tk.X, pady=(0, 15))
            
            # 数值变量
            value_var = tk.StringVar(value="等待数据...")
            value_label = ttk.Label(
                value_frame,
                textvariable=value_var,
                font=("Arial", 12)
            )
            value_label.pack(fill=tk.X, padx=5, pady=5)
            
            # 提示框架
            hint_frame = ttk.LabelFrame(main_frame, text="操作提示", padding="10")
            hint_frame.pack(fill=tk.X, pady=(0, 15))
            
            hint_label = ttk.Label(
                hint_frame,
                text="请用手遮挡光感传感器，观察数值变化\n数值变化即表示光感功能正常",
                font=("Arial", 12)
            )
            hint_label.pack(fill=tk.X, padx=5, pady=5)
            
            # 更新界面
            test_window.update()
            
            # 启动evtest监听
            status_var.set("正在启动光感监听...")
            test_window.update()
            
            command = test_project["command"]
            self.log_message(f"执行命令: adb shell {command}")
            
            # 使用subprocess.Popen启动命令，这样可以实时读取输出
            process = subprocess.Popen(
                ['adb', 'shell', command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 初始化变量
            last_value = None
            has_change = False
            start_time = time.time()
            timeout = 60  # 测试超时时间（秒） - 统一为1分钟
            
            # 更新状态
            status_var.set("正在检测光感数据...")
            test_window.update()
            
            # 读取并解析输出
            while True:
                # 检查是否超时
                if time.time() - start_time > timeout:
                    process.terminate()
                    status_var.set("测试超时！")
                    test_window.update()
                    test_window.after(2000, test_window.destroy)
                    self.log_message("⏰ 光感测试超时 - 1分钟内无响应，自动判定为失败")
                    self.test_results[test_project["id"]].message = "测试超时"
                    return False
                
                # 读取一行输出
                line = process.stdout.readline()
                if not line:
                    break
                
                # 解析ABS_MISC事件
                if "ABS_MISC" in line:
                    try:
                        # 提取value值
                        value = int(line.split("value")[-1].strip())
                        
                        # 更新显示
                        value_var.set(f"当前值: {value}")
                        test_window.update()
                        
                        # 检测值变化
                        if last_value is not None and value != last_value:
                            has_change = True
                            process.terminate()
                            status_var.set("检测完成！")
                            test_window.update()
                            test_window.after(2000, test_window.destroy)
                            
                            self.log_message(f"光感测试完成 - 检测到数值变化")
                            self.log_message(f"数值从 {last_value} 变化到 {value}")
                            self.test_results[test_project["id"]].message = "光感正常"
                            return True
                            
                        last_value = value
                            
                    except ValueError:
                        continue
            
            # 如果没有检测到变化
            process.terminate()
            status_var.set("未检测到数值变化！")
            test_window.update()
            test_window.after(2000, test_window.destroy)
            
            self.log_message("光感测试失败 - 未检测到数值变化")
            self.test_results[test_project["id"]].message = "光感异常"
            return False
            
        except Exception as e:
            self.log_message(f"光感测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_speaker_test(self, test_project):
        """喇叭测试"""
        try:
            # 记录这是喇叭测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message("执行喇叭测试...")
            command = test_project["command"]
            self.log_message(f"执行命令: adb shell {command}")
            
            result = subprocess.run(['adb', 'shell', command], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                self.log_message("命令执行成功")
                self.log_message(f"返回数据: {result.stdout.strip() if result.stdout.strip() else '无输出'}")
                self.log_message("音频播放完成")
                self.test_results[test_project["id"]].message = "音频播放完成"
                return True
            else:
                self.log_message(f"命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "播放失败"
                return False
                
        except Exception as e:
            self.log_message(f"喇叭测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
            
    def run_bluetooth_test(self, test_project):
        """蓝牙测试 - 使用bluetoothctl show命令获取Controller信息"""
        try:
            self.log_message("执行蓝牙测试...")
            self.log_message("使用bluetoothctl show命令获取蓝牙控制器信息...")

            # 使用bluetoothctl show命令获取蓝牙控制器信息
            self.log_message("执行命令: adb shell bluetoothctl show")

            result = subprocess.run(['adb', 'shell', 'bluetoothctl', 'show'],
                                  capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                self.log_message("命令执行成功")
                output = result.stdout.strip()

                if output:
                    self.log_message("返回数据:")
                    lines = output.split('\n')
                    for line in lines:
                        if line.strip():
                            self.log_message(f"  {line.strip()}")

                    # 查找Controller行并提取MAC地址
                    # 格式: Controller 24:21:5E:C0:30:F3 (public)
                    controller_mac = None
                    for line in lines:
                        line = line.strip()
                        if line.startswith("Controller "):
                            # 提取MAC地址
                            parts = line.split()
                            if len(parts) >= 2:
                                mac_address = parts[1]
                                # 验证MAC地址格式 (XX:XX:XX:XX:XX:XX)
                                if len(mac_address) == 17 and mac_address.count(':') == 5:
                                    controller_mac = mac_address
                                    break

                    if controller_mac:
                        self.log_message(f"✅ 蓝牙测试成功，检测到蓝牙控制器")
                        self.log_message(f"蓝牙控制器MAC地址: {controller_mac}")
                        self.test_results[test_project["id"]].message = f"MAC: {controller_mac}"
                        return True
                    else:
                        self.log_message("❌ 蓝牙测试失败，未找到有效的蓝牙控制器")
                        self.test_results[test_project["id"]].message = "未找到蓝牙控制器"
                        return False
                else:
                    self.log_message("❌ 蓝牙测试失败，无输出数据")
                    self.test_results[test_project["id"]].message = "无输出数据"
                    return False
            else:
                self.log_message(f"❌ 命令执行失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "命令执行失败"
                return False

        except Exception as e:
            self.log_message(f"蓝牙测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False

    def run_4g_network_test(self, test_project):
        """4G网络测试 - 关闭WiFi后使用4G网络进行ping测试"""
        try:
            self.log_message("执行4G网络测试...")
            self.log_message("第一步：关闭WiFi网络...")

            # 关闭WiFi网络，确保使用4G网络
            disable_wifi_cmd = "ifconfig wlan0 down"
            result = subprocess.run(
                ["adb", "shell", disable_wifi_cmd],
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode != 0:
                self.log_message(f"关闭WiFi失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "关闭WiFi失败"
                return False

            self.log_message("WiFi已关闭，等待4G网络连接...")
            time.sleep(3)  # 等待4G网络连接

            self.log_message("第二步：使用4G网络进行ping测试...")

            # 获取测试参数
            ping_target = test_project.get("ping_target", "www.baidu.com")
            test_duration = test_project.get("test_duration", 10)
            expected_pattern = test_project.get("expected_pattern", "64 bytes from")

            self.log_message(f"ping目标: {ping_target}")
            self.log_message(f"测试时长: {test_duration}秒")

            # 执行ping测试
            ping_cmd = f"ping -c {test_duration} {ping_target}"
            result = subprocess.run(
                ["adb", "shell", ping_cmd],
                capture_output=True,
                text=True,
                timeout=test_duration + 10
            )

            if result.returncode != 0:
                self.log_message(f"4G网络ping测试失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "4G网络不通"
                return False

            # 解析ping结果，计算延时平均值
            ping_output = result.stdout
            self.log_message("ping测试输出:")
            for line in ping_output.split('\n'):
                if line.strip():
                    self.log_message(f"  {line}")

            # 提取延时数据
            delay_times = []
            for line in ping_output.split('\n'):
                if expected_pattern in line and "time=" in line:
                    try:
                        # 提取time=xx.x ms中的延时值
                        time_part = line.split("time=")[1].split("ms")[0].strip()
                        delay_time = float(time_part)
                        delay_times.append(delay_time)
                    except (IndexError, ValueError):
                        continue

            if not delay_times:
                self.log_message("未能解析到延时数据")
                self.test_results[test_project["id"]].message = "无法解析延时数据"
                return False

            # 计算平均延时
            avg_delay = sum(delay_times) / len(delay_times)
            min_delay = min(delay_times)
            max_delay = max(delay_times)

            self.log_message(f"4G网络延时统计:")
            self.log_message(f"  成功ping包: {len(delay_times)} 个")
            self.log_message(f"  平均延时: {avg_delay:.1f} ms")
            self.log_message(f"  最小延时: {min_delay:.1f} ms")
            self.log_message(f"  最大延时: {max_delay:.1f} ms")

            # 判断测试结果
            if len(delay_times) >= test_duration * 0.8:  # 至少80%的包成功
                self.log_message("4G网络测试通过")
                self.test_results[test_project["id"]].message = f"平均延时: {avg_delay:.1f}ms"
                test_passed = True
            else:
                self.log_message("4G网络测试失败：丢包率过高")
                self.test_results[test_project["id"]].message = f"丢包率过高，仅收到{len(delay_times)}个回包"
                test_passed = False

            return test_passed

        except subprocess.TimeoutExpired:
            self.log_message("4G网络测试超时")
            self.test_results[test_project["id"]].message = "测试超时"
            return False
        except Exception as e:
            self.log_message(f"4G网络测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False
        finally:
            # 无论测试成功与否，都要重新打开WiFi
            try:
                self.log_message("第三步：重新打开WiFi网络...")
                enable_wifi_cmd = "ifconfig wlan0 up"
                result = subprocess.run(
                    ["adb", "shell", enable_wifi_cmd],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    self.log_message("WiFi已重新打开")
                else:
                    self.log_message(f"重新打开WiFi失败: {result.stderr}")

                time.sleep(2)  # 等待WiFi重新连接

            except Exception as e:
                self.log_message(f"重新打开WiFi时出错: {str(e)}")

    def run_wifi_test(self, test_project):
        """WiFi测试 - 关闭4G后连接WiFi并进行网络发包延时测试"""
        try:
            self.log_message("执行WiFi测试...")

            # 步骤0：关闭4G网络（usb0接口）
            self.log_message("第一步：关闭4G网络...")
            self.log_message("关闭usb0接口（4G网络）...")
            cmd_4g_down = "ifconfig usb0 down"
            self.log_message(f"执行命令: adb shell {cmd_4g_down}")

            result_4g_down = subprocess.run(['adb', 'shell', cmd_4g_down],
                                          capture_output=True, text=True, timeout=10)

            if result_4g_down.returncode == 0:
                self.log_message("✅ 4G网络已关闭")
            else:
                self.log_message(f"⚠️ 关闭4G网络时出现警告: {result_4g_down.stderr}")
                self.log_message("继续执行WiFi测试...")

            # 等待网络状态稳定
            import time
            time.sleep(2)

            self.log_message("第二步：连接WiFi网络...")

            # 步骤1：停止现有的wpa_supplicant进程
            self.log_message("停止现有的wpa_supplicant进程...")
            cmd1 = "killall wpa_supplicant 2>/dev/null"
            self.log_message(f"执行命令: adb shell \"{cmd1}\"")
            subprocess.run(['adb', 'shell', cmd1], capture_output=True, text=True, timeout=10)

            # 步骤2：清理wpa_supplicant socket文件
            self.log_message("清理wpa_supplicant socket文件...")
            cmd2 = "rm -f /var/run/wpa_supplicant/wlan0"
            self.log_message(f"执行命令: adb shell \"{cmd2}\"")
            subprocess.run(['adb', 'shell', cmd2], capture_output=True, text=True, timeout=10)

            # 步骤3：关闭wlan0接口
            self.log_message("关闭wlan0接口...")
            cmd3 = "ip link set wlan0 down"
            self.log_message(f"执行命令: adb shell {cmd3}")
            subprocess.run(['adb', 'shell', cmd3], capture_output=True, text=True, timeout=10)

            # 步骤4：连接WiFi网络
            self.log_message("连接WiFi网络...")
            # 从配置中获取WiFi信息，如果没有则使用默认值
            wifi_ssid = self.wifi_settings.get("ssid", "Orion_SZ_5G")
            wifi_password = self.wifi_settings.get("password", "Orion@2025")

            wifi_cmd = (
                f"wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && "
                f"wpa_cli -i wlan0 add_network && "
                f"wpa_cli -i wlan0 set_network 0 ssid '\"'{wifi_ssid}'\"' && "
                f"wpa_cli -i wlan0 set_network 0 psk '\"'{wifi_password}'\"' && "
                f"wpa_cli -i wlan0 enable_network 0 && "
                f"udhcpc -i wlan0 && "
                f"iw wlan0 link && "
                f"ip addr show wlan0"
            )

            self.log_message(f"执行WiFi连接命令...")
            self.log_message(f"SSID: {wifi_ssid}")

            wifi_result = subprocess.run(['adb', 'shell', wifi_cmd],
                                       capture_output=True, text=True, timeout=30)

            if wifi_result.returncode != 0:
                self.log_message(f"❌ WiFi连接失败: {wifi_result.stderr}")
                self.test_results[test_project["id"]].message = "WiFi连接失败"
                # WiFi连接失败也要恢复4G网络
                self._restore_4g_network()
                return False

            # 显示WiFi连接结果
            self.log_message("WiFi连接命令执行完成，返回数据:")
            wifi_output = wifi_result.stdout.strip()
            if wifi_output:
                lines = wifi_output.split('\n')
                for line in lines:
                    if line.strip():
                        self.log_message(f"  {line.strip()}")

            # 检查连接状态
            if "Connected to" in wifi_output or "inet " in wifi_output:
                self.log_message("✅ WiFi连接成功")
            else:
                self.log_message("⚠️ WiFi连接状态不明确，继续进行网络测试...")

            # 等待网络稳定
            import time
            self.log_message("等待网络稳定...")
            time.sleep(3)

            # 步骤5：进行网络延时测试
            self.log_message("第二步：开始网络发包延时测试...")

            # 执行10秒钟的ping测试来计算平均延时
            ping_command = "ping -c 10 www.baidu.com"
            self.log_message(f"执行命令: adb shell {ping_command}")
            self.log_message("正在进行10秒钟的网络延时测试...")

            ping_result = subprocess.run(['adb', 'shell', ping_command],
                                       capture_output=True, text=True, timeout=20)

            if ping_result.returncode == 0:
                output = ping_result.stdout.strip()
                self.log_message("ping命令执行成功")
                self.log_message("返回数据:")

                # 分行显示输出
                lines = output.split('\n')
                for line in lines:
                    if line.strip():
                        self.log_message(f"  {line.strip()}")

                # 解析延时数据
                import re
                time_pattern = re.compile(r'time=(\d+\.?\d*)\s*ms')
                delays = []

                for line in lines:
                    if "64 bytes from" in line and "time=" in line:
                        match = time_pattern.search(line)
                        if match:
                            delay = float(match.group(1))
                            delays.append(delay)
                            self.log_message(f"检测到延时: {delay} ms")

                if delays:
                    # 计算平均延时
                    avg_delay = sum(delays) / len(delays)
                    min_delay = min(delays)
                    max_delay = max(delays)

                    self.log_message(f"✅ WiFi延时测试成功")
                    self.log_message(f"发包数量: {len(delays)} 个")
                    self.log_message(f"平均延时: {avg_delay:.2f} ms")
                    self.log_message(f"最小延时: {min_delay:.2f} ms")
                    self.log_message(f"最大延时: {max_delay:.2f} ms")

                    # 判断网络质量
                    if avg_delay <= 50:
                        quality = "优秀"
                    elif avg_delay <= 100:
                        quality = "良好"
                    elif avg_delay <= 200:
                        quality = "一般"
                    else:
                        quality = "较差"

                    self.test_results[test_project["id"]].message = f"平均延时: {avg_delay:.2f}ms ({quality})"

                    # WiFi测试成功，重新打开4G网络
                    self._restore_4g_network()
                    return True
                else:
                    self.log_message("❌ 未检测到有效的延时数据")
                    self.test_results[test_project["id"]].message = "未检测到延时数据"
                    # 测试失败也要恢复4G网络
                    self._restore_4g_network()
                    return False
            else:
                self.log_message(f"❌ ping命令执行失败: {ping_result.stderr}")
                self.test_results[test_project["id"]].message = "网络连接失败"
                # 测试失败也要恢复4G网络
                self._restore_4g_network()
                return False

        except Exception as e:
            self.log_message(f"WiFi测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            # 异常情况也要恢复4G网络
            self._restore_4g_network()
            return False

    def _restore_4g_network(self):
        """恢复4G网络连接"""
        try:
            self.log_message("第三步：恢复4G网络...")
            self.log_message("重新打开usb0接口（4G网络）...")
            cmd_4g_up = "ifconfig usb0 up"
            self.log_message(f"执行命令: adb shell {cmd_4g_up}")

            result_4g_up = subprocess.run(['adb', 'shell', cmd_4g_up],
                                        capture_output=True, text=True, timeout=10)

            if result_4g_up.returncode == 0:
                self.log_message("✅ 4G网络已恢复")
            else:
                self.log_message(f"⚠️ 恢复4G网络时出现警告: {result_4g_up.stderr}")

            # 等待网络状态稳定
            import time
            time.sleep(2)

        except Exception as e:
            self.log_message(f"恢复4G网络时出错: {str(e)}")

    def run_sensor_fps_test(self, test_project):
        """单个传感器FPS测试 - 读取指定传感器的FPS数据"""
        try:
            sensor_name = test_project.get("sensor_name", "Unknown")
            self.log_message(f"执行{sensor_name}传感器FPS测试...")
            self.log_message(f"读取{sensor_name}传感器FPS数据...")

            # 执行cat命令读取传感器日志
            command = "cat /sdcard/lmv/normal_logs/sensor/sensor_newest"
            self.log_message(f"执行命令: adb shell {command}")

            result = subprocess.run(['adb', 'shell', command],
                                  capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                output = result.stdout.strip()
                self.log_message("传感器日志读取成功")

                # 解析指定传感器的FPS数据
                avg_fps = self.parse_single_sensor_fps_data(output, sensor_name)

                if avg_fps is not None:
                    self.log_message(f"✅ {sensor_name}传感器测试成功")
                    self.log_message(f"{sensor_name}平均FPS: {avg_fps:.1f}")

                    # 将结果保存到测试结果中
                    self.test_results[test_project["id"]].message = f"{avg_fps:.1f} FPS"
                    return True
                else:
                    self.log_message(f"❌ 未找到{sensor_name}传感器的有效FPS数据")
                    self.test_results[test_project["id"]].message = "未找到FPS数据"
                    return False
            else:
                self.log_message(f"❌ 传感器日志读取失败: {result.stderr}")
                self.test_results[test_project["id"]].message = "日志读取失败"
                return False

        except Exception as e:
            self.log_message(f"{sensor_name}传感器测试出错: {str(e)}")
            self.test_results[test_project["id"]].message = f"错误: {str(e)}"
            return False

    def parse_sensor_fps_data(self, log_content):
        """解析传感器FPS数据"""
        try:
            import re

            # 查找包含"sensor fps:"关键字段的行
            lines = log_content.split('\n')
            fps_sections = []

            # 找到所有包含"sensor fps:"的行及其后续的FPS数据
            for i, line in enumerate(lines):
                if "sensor fps:" in line:
                    # 收集这个section的FPS数据
                    section_data = {}
                    # 从下一行开始查找FPS数据，最多查找10行
                    for j in range(i + 1, min(i + 11, len(lines))):
                        fps_line = lines[j].strip()
                        if not fps_line:
                            continue

                        # 匹配FPS数据格式: FPS Accel: 184
                        fps_match = re.match(r'FPS\s+(\w+):\s+(\d+)', fps_line)
                        if fps_match:
                            sensor_name = fps_match.group(1)
                            fps_value = int(fps_match.group(2))
                            section_data[sensor_name] = fps_value
                        elif fps_line.startswith('FPS '):
                            # 如果遇到其他FPS行但格式不匹配，继续
                            continue
                        else:
                            # 如果遇到非FPS行，结束这个section
                            break

                    if section_data:
                        fps_sections.append(section_data)

            self.log_message(f"找到 {len(fps_sections)} 个FPS数据段")

            if not fps_sections:
                return None

            # 取最近5条数据（如果不足5条则取全部）
            recent_sections = fps_sections[-5:]
            self.log_message(f"使用最近 {len(recent_sections)} 条数据计算平均值")

            # 计算每个传感器的平均FPS
            sensor_averages = {}
            all_sensors = set()

            # 收集所有传感器名称
            for section in recent_sections:
                all_sensors.update(section.keys())

            # 计算每个传感器的平均值
            for sensor in all_sensors:
                values = []
                for section in recent_sections:
                    if sensor in section:
                        values.append(section[sensor])

                if values:
                    avg_fps = sum(values) / len(values)
                    sensor_averages[sensor] = avg_fps
                    self.log_message(f"传感器 {sensor}: {values} -> 平均 {avg_fps:.1f}")

            return sensor_averages

        except Exception as e:
            self.log_message(f"解析传感器FPS数据出错: {str(e)}")
            return None

    def parse_single_sensor_fps_data(self, log_content, target_sensor):
        """解析单个传感器的FPS数据"""
        try:
            import re

            self.log_message(f"开始解析{target_sensor}传感器FPS数据...")

            # 查找包含"sensor fps:"关键字段的行
            lines = log_content.split('\n')
            sensor_values = []

            # 找到所有包含"sensor fps:"的行及其后续的FPS数据
            for i, line in enumerate(lines):
                if "sensor fps:" in line:
                    # 从下一行开始查找目标传感器的FPS数据，最多查找10行
                    for j in range(i + 1, min(i + 11, len(lines))):
                        fps_line = lines[j].strip()
                        if not fps_line:
                            continue

                        # 匹配目标传感器的FPS数据格式: FPS Accel: 184
                        fps_pattern = f"FPS\\s+{re.escape(target_sensor)}:\\s+(\\d+)"
                        fps_match = re.match(fps_pattern, fps_line)
                        if fps_match:
                            fps_value = int(fps_match.group(1))
                            sensor_values.append(fps_value)
                            break  # 找到目标传感器数据后跳出内层循环
                        elif fps_line.startswith('FPS '):
                            # 如果遇到其他传感器的FPS行，继续查找
                            continue
                        else:
                            # 如果遇到非FPS行，结束这个section
                            break

            self.log_message(f"找到 {len(sensor_values)} 个{target_sensor}传感器FPS数据点")

            if not sensor_values:
                return None

            # 取最近5条数据（如果不足5条则取全部）
            recent_values = sensor_values[-5:]
            self.log_message(f"使用最近 {len(recent_values)} 条数据计算平均值: {recent_values}")

            # 计算平均值
            avg_fps = sum(recent_values) / len(recent_values)
            self.log_message(f"{target_sensor}传感器平均FPS: {avg_fps:.1f}")

            return avg_fps

        except Exception as e:
            self.log_message(f"解析{target_sensor}传感器FPS数据出错: {str(e)}")
            return None

    def open_config_manager(self):
        """打开配置管理器"""
        try:
            import subprocess
            import os

            # 检查配置管理器文件是否存在
            config_manager_path = "config_manager.py"
            if not os.path.exists(config_manager_path):
                messagebox.showerror("错误", "配置管理器文件不存在: config_manager.py")
                return

            # 启动配置管理器
            self.log_message("正在启动配置管理器...")

            # 使用subprocess启动配置管理器
            subprocess.Popen([sys.executable, config_manager_path],
                           cwd=os.getcwd(),
                           creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)

            self.log_message("配置管理器已启动")

        except Exception as e:
            self.log_message(f"启动配置管理器失败: {str(e)}")
            messagebox.showerror("错误", f"启动配置管理器失败: {str(e)}")

    def open_quick_config(self):
        """打开快速配置工具"""
        try:
            import subprocess
            import os

            # 检查快速配置文件是否存在
            quick_config_path = "quick_start_config.py"
            if not os.path.exists(quick_config_path):
                messagebox.showerror("错误", "快速配置工具文件不存在: quick_start_config.py")
                return

            # 启动快速配置工具
            self.log_message("正在启动快速配置工具...")

            subprocess.Popen([sys.executable, quick_config_path],
                           cwd=os.getcwd(),
                           creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)

            self.log_message("快速配置工具已启动")

        except Exception as e:
            self.log_message(f"启动快速配置工具失败: {str(e)}")
            messagebox.showerror("错误", f"启动快速配置工具失败: {str(e)}")

    def reload_config_from_menu(self):
        """从菜单重新加载配置"""
        if messagebox.askyesno("确认", "重新加载配置将重置当前测试状态，确定继续吗？"):
            try:
                # 重新加载配置
                self.config = self.load_config()
                self.test_projects = self.config.get("test_projects", [])
                self.work_processes = self.config.get("work_processes", {})
                self.adb_settings = self.config.get("adb_settings", {})
                self.wifi_settings = self.config.get("wifi_settings", {})

                # 重新初始化
                self.test_results.clear()
                self.init_test_projects()
                self.init_test_tree()

                # 更新工序选择
                if hasattr(self, 'process_combo'):
                    self.process_combo['values'] = list(self.work_processes.keys())
                    self.update_process_description()

                self.log_message("配置已重新加载")
                messagebox.showinfo("成功", "配置已重新加载")

            except Exception as e:
                self.log_message(f"重新加载配置失败: {str(e)}")
                messagebox.showerror("错误", f"重新加载配置失败: {str(e)}")

    def validate_config(self):
        """验证配置"""
        try:
            self.log_message("开始验证配置...")

            errors = []
            warnings = []

            # 检查测试项目
            project_ids = []
            for project in self.test_projects:
                project_id = project.get("id")
                if not project_id:
                    errors.append("发现没有ID的测试项目")
                elif project_id in project_ids:
                    errors.append(f"重复的项目ID: {project_id}")
                else:
                    project_ids.append(project_id)

                if not project.get("name"):
                    warnings.append(f"项目 {project_id} 没有名称")
                if not project.get("type"):
                    warnings.append(f"项目 {project_id} 没有类型")

            # 检查工序
            valid_project_ids = set(project_ids)
            for process_name, process in self.work_processes.items():
                test_ids = process.get("test_ids", [])
                for test_id in test_ids:
                    if test_id not in valid_project_ids:
                        errors.append(f"工序 '{process_name}' 包含无效的项目ID: {test_id}")

            # 显示验证结果
            result_text = "配置验证结果:\n\n"

            if errors:
                result_text += "❌ 错误:\n"
                for error in errors:
                    result_text += f"  • {error}\n"
                result_text += "\n"

            if warnings:
                result_text += "⚠️ 警告:\n"
                for warning in warnings:
                    result_text += f"  • {warning}\n"
                result_text += "\n"

            if not errors and not warnings:
                result_text += "✅ 配置验证通过，没有发现问题"

            result_text += f"\n统计信息:\n"
            result_text += f"  • 测试项目: {len(self.test_projects)} 个\n"
            result_text += f"  • 工序: {len(self.work_processes)} 个\n"

            # 显示结果窗口
            result_window = tk.Toplevel(self.root)
            result_window.title("配置验证结果")
            result_window.geometry("500x400")
            result_window.transient(self.root)

            text_widget = scrolledtext.ScrolledText(result_window, wrap=tk.WORD, font=("Microsoft YaHei UI", 10))
            text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            text_widget.insert(tk.END, result_text)
            text_widget.config(state=tk.DISABLED)

            self.log_message("配置验证完成")

        except Exception as e:
            self.log_message(f"配置验证失败: {str(e)}")
            messagebox.showerror("错误", f"配置验证失败: {str(e)}")

    def check_adb_devices(self):
        """检查ADB设备"""
        try:
            self.log_message("检查ADB设备连接...")

            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                output = result.stdout.strip()
                lines = output.split('\n')

                device_info = "ADB设备检查结果:\n\n"
                device_info += f"原始输出:\n{output}\n\n"

                devices = []
                for line in lines[1:]:  # 跳过标题行
                    if line.strip() and '\t' in line:
                        device_id, status = line.split('\t')
                        devices.append((device_id, status))

                if devices:
                    device_info += f"发现 {len(devices)} 个设备:\n"
                    for device_id, status in devices:
                        device_info += f"  • {device_id}: {status}\n"
                else:
                    device_info += "❌ 没有发现连接的设备"

                # 显示结果
                messagebox.showinfo("ADB设备检查", device_info)
                self.log_message("ADB设备检查完成")

            else:
                error_msg = f"ADB命令执行失败: {result.stderr}"
                messagebox.showerror("错误", error_msg)
                self.log_message(error_msg)

        except Exception as e:
            error_msg = f"ADB设备检查失败: {str(e)}"
            self.log_message(error_msg)
            messagebox.showerror("错误", error_msg)

    def clear_log(self):
        """清空日志"""
        if messagebox.askyesno("确认", "确定要清空所有日志吗？"):
            self.log_text.delete(1.0, tk.END)
            self.log_message("日志已清空")

    def test_connection(self):
        """测试连接"""
        self.log_message("开始测试连接...")

        # 在新线程中运行连接测试
        def run_connection_test():
            try:
                # 测试ADB连接
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    self.log_message("✅ ADB连接正常")
                else:
                    self.log_message("❌ ADB连接失败")

                # 测试设备连接
                result = subprocess.run(['adb', 'shell', 'echo', 'test'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    self.log_message("✅ 设备连接正常")
                else:
                    self.log_message("❌ 设备连接失败")

                self.log_message("连接测试完成")

            except Exception as e:
                self.log_message(f"连接测试出错: {str(e)}")

        threading.Thread(target=run_connection_test, daemon=True).start()

    def show_help(self):
        """显示使用说明"""
        help_text = """CMG整机功能测试软件使用说明

1. 工序选择
   • 在顶部选择要执行的测试工序
   • 不同工序包含不同的测试项目组合

2. 测试执行
   • 点击"开始测试"执行选定工序的所有测试
   • 可以右键单击测试项目执行单个测试
   • 测试过程中可以点击"停止测试"中断

3. 结果查看
   • 绿色表示测试通过
   • 红色表示测试失败
   • 黄色表示测试进行中
   • 详细信息显示在右侧列

4. 配置管理
   • 使用"配置"菜单管理测试项目和工序
   • 可以添加、修改、删除测试项目
   • 可以创建自定义工序组合

5. 日志查看
   • 底部显示详细的测试日志
   • 可以通过"工具"菜单清空日志

6. 快捷操作
   • Ctrl+S: 保存测试结果
   • F5: 刷新配置
   • F1: 显示帮助"""

        help_window = tk.Toplevel(self.root)
        help_window.title("使用说明")
        help_window.geometry("600x500")
        help_window.transient(self.root)

        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD, font=("Microsoft YaHei UI", 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, help_text)
        text_widget.config(state=tk.DISABLED)

    def show_config_help(self):
        """显示配置管理帮助"""
        config_help_text = """配置管理帮助

1. 配置管理器
   • 图形化界面管理测试项目和工序
   • 支持添加、编辑、删除操作
   • 自动备份和恢复功能

2. 快速配置
   • 命令行方式快速配置
   • 适合批量操作和脚本化

3. 测试项目配置
   • 项目ID: 唯一标识符
   • 项目名称: 显示名称
   • 项目类型: 测试类型分类
   • 命令: 执行的ADB命令
   • 期望模式: 预期输出模式

4. 工序配置
   • 工序名称: 工序显示名称
   • 工序描述: 详细说明
   • 测试项目: 包含的测试项目列表

5. 配置文件
   • 位置: config.json
   • 备份: config_backups/ 目录
   • 格式: JSON格式

详细说明请参考 CONFIG_MANAGER_GUIDE.md 文件"""

        help_window = tk.Toplevel(self.root)
        help_window.title("配置管理帮助")
        help_window.geometry("600x400")
        help_window.transient(self.root)

        text_widget = scrolledtext.ScrolledText(help_window, wrap=tk.WORD, font=("Microsoft YaHei UI", 10))
        text_widget.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        text_widget.insert(tk.END, config_help_text)
        text_widget.config(state=tk.DISABLED)

    def show_about(self):
        """显示关于信息"""
        about_text = """CMG整机功能测试软件

版本: 2.0
开发: CMG技术团队
日期: 2024年

功能特点:
• 支持多种硬件测试
• 灵活的工序配置
• 图形化配置管理
• 自动化测试流程
• 详细的测试日志

技术支持:
• 配置管理工具
• 快速配置脚本
• 自动备份恢复
• 测试结果导出"""

        messagebox.showinfo("关于", about_text)

    def _center_dialog_on_parent(self, dialog):
        """将对话框居中显示在父窗口上"""
        try:
            # 更新窗口信息
            self.root.update_idletasks()
            dialog.update_idletasks()

            # 获取主窗口位置和尺寸
            root_x = self.root.winfo_x()
            root_y = self.root.winfo_y()
            root_width = self.root.winfo_width()
            root_height = self.root.winfo_height()

            # 获取对话框尺寸
            dialog_width = dialog.winfo_width()
            dialog_height = dialog.winfo_height()

            # 计算居中位置
            x = root_x + (root_width - dialog_width) // 2
            y = root_y + (root_height - dialog_height) // 2

            # 确保对话框不会超出屏幕边界
            screen_width = dialog.winfo_screenwidth()
            screen_height = dialog.winfo_screenheight()

            x = max(0, min(x, screen_width - dialog_width))
            y = max(0, min(y, screen_height - dialog_height))

            dialog.geometry(f"+{x}+{y}")
        except Exception as e:
            print(f"居中对话框时出错: {e}")

    def select_work_process(self):
        """第一步：选择工序"""
        if not self.work_processes:
            messagebox.showerror("错误", "没有找到工序配置，请检查配置文件")
            return False

        # 创建工序选择对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("选择测试工序")
        dialog.geometry("400x200")
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.resizable(False, False)

        # 强制更新窗口尺寸信息
        self.root.update()
        dialog.update()

        # 等待窗口完全创建
        self.root.after(10, lambda: self._center_dialog_on_parent(dialog))

        # 禁止关闭窗口
        dialog.protocol("WM_DELETE_WINDOW", lambda: None)

        result = {"selected": False}

        main_frame = ttk.Frame(dialog, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text="请选择测试工序",
                               font=("Microsoft YaHei UI", 14, "bold"))
        title_label.pack(pady=(0, 25))

        # 工序选择区域
        select_frame = ttk.Frame(main_frame)
        select_frame.pack(fill=tk.X, pady=(0, 30))
        select_frame.columnconfigure(1, weight=1)

        # 工序标签
        ttk.Label(select_frame, text="测试工序:",
                 font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)

        # 工序下拉列表
        process_var = tk.StringVar()
        process_names = list(self.work_processes.keys())
        process_combo = ttk.Combobox(
            select_frame,
            textvariable=process_var,
            values=process_names,
            state="readonly",
            font=("Microsoft YaHei UI", 11),
            width=25
        )
        process_combo.grid(row=0, column=1, sticky=(tk.W, tk.E))

        # 默认选择第一个工序
        if process_names:
            process_combo.current(0)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def confirm_selection():
            """确认选择"""
            selected_process = process_var.get()
            if selected_process:
                self.selected_process = selected_process
                result["selected"] = True
                dialog.destroy()
            else:
                messagebox.showwarning("警告", "请选择一个工序")

        def cancel_selection():
            """取消选择"""
            result["selected"] = False
            dialog.destroy()

        # 按钮区域居中布局
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)

        # 按钮布局：确认选择在左边，退出程序在右边
        confirm_btn = ttk.Button(button_container, text="确认选择",
                               command=confirm_selection,
                               style="Accent.TButton",
                               width=15)  # 增加宽度确保字体居中
        confirm_btn.pack(side=tk.LEFT, padx=(0, 20))

        exit_btn = ttk.Button(button_container, text="退出程序",
                            command=cancel_selection,
                            width=15)  # 增加宽度确保字体居中
        exit_btn.pack(side=tk.LEFT)

        # 绑定回车键确认
        dialog.bind('<Return>', lambda e: confirm_selection())

        # 设置焦点到下拉框
        process_combo.focus()

        # 等待对话框关闭
        dialog.wait_window()

        return result["selected"]

    def reselect_work_process(self):
        """重新选择工序"""
        if self.select_work_process():
            # 工序选择成功，重新初始化测试项目
            self.init_test_projects()
            self.init_test_tree()
            self.log_message(f"已切换到工序: {self.selected_process}")

            # 更新界面显示
            self.update_info_display()
        else:
            self.log_message("工序选择已取消")

    def input_serial_number(self, title="输入序列号", prompt="请输入设备序列号"):
        """输入序列号对话框"""
        # 创建序列号输入对话框
        dialog = tk.Toplevel(self.root)
        dialog.title(title)
        dialog.geometry("500x280")  # 增加高度确保按钮可见
        dialog.transient(self.root)
        dialog.grab_set()
        dialog.resizable(False, False)

        # 强制更新窗口尺寸信息
        self.root.update()
        dialog.update()

        # 等待窗口完全创建后居中
        self.root.after(10, lambda: self._center_dialog_on_parent(dialog))

        # 禁止关闭窗口
        dialog.protocol("WM_DELETE_WINDOW", lambda: None)

        result = {"confirmed": False, "serial_number": ""}

        main_frame = ttk.Frame(dialog, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        title_label = ttk.Label(main_frame, text=title,
                               font=("Microsoft YaHei UI", 14, "bold"))
        title_label.pack(pady=(0, 20))

        # 当前工序信息
        if self.selected_process:
            process_info = f"当前工序: {self.selected_process}"
            info_label = ttk.Label(main_frame, text=process_info,
                                  font=("Microsoft YaHei UI", 10),
                                  foreground="blue")
            info_label.pack(pady=(0, 20))

        # 序列号输入区域
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=(0, 25))
        input_frame.columnconfigure(1, weight=1)

        # 输入标签
        ttk.Label(input_frame, text="设备序列号:",
                 font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)

        # 序列号输入框
        serial_var = tk.StringVar()
        serial_entry = ttk.Entry(input_frame, textvariable=serial_var,
                               font=("Microsoft YaHei UI", 11), width=20)
        serial_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
        serial_entry.focus()

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        def confirm_serial():
            """确认序列号并直接开始测试"""
            serial_number = serial_var.get().strip()
            if serial_number:
                self.current_serial_number = serial_number
                result["confirmed"] = True
                result["serial_number"] = serial_number
                # 释放焦点抓取
                dialog.grab_release()
                dialog.destroy()
                # 确保主窗口重新获得焦点
                self.root.focus_force()
                self.root.lift()

                # 更新界面显示
                self.update_info_display()

                # 直接开始测试，与主界面开始测试按钮功能一致
                self.root.after(100, self.start_all_tests)
            else:
                messagebox.showwarning("警告", "请输入序列号")
                serial_entry.focus()

        def cancel_serial():
            """取消输入，只关闭弹窗"""
            result["confirmed"] = False
            # 释放焦点抓取
            dialog.grab_release()
            dialog.destroy()
            # 确保主窗口重新获得焦点
            self.root.focus_force()
            self.root.lift()

            # 确保测试树重新获得焦点和事件响应
            self.root.after(100, lambda: self.test_tree.focus_set())
            self.root.after(200, lambda: self.root.update())

            # 记录取消操作
            self.log_message("用户取消序列号输入，返回主界面")
            self.log_message("可以查看测试结果或手动点击开始测试")

            # 修复测试树事件绑定（如果需要）
            self.root.after(300, self.debug_test_tree_state)

        # 绑定回车键
        serial_entry.bind("<Return>", lambda e: confirm_serial())

        # 按钮区域居中布局
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)

        # 按钮布局：开始测试在左边，退出程序在右边
        start_btn = ttk.Button(button_container, text="开始测试",
                             command=confirm_serial,
                             style="Accent.TButton",
                             width=15)  # 增加宽度确保字体居中
        start_btn.pack(side=tk.LEFT, padx=(0, 20))

        exit_btn = ttk.Button(button_container, text="取消",
                            command=cancel_serial,
                            width=15)  # 增加宽度确保字体居中
        exit_btn.pack(side=tk.LEFT)

        # 等待对话框关闭
        dialog.wait_window()

        return result["confirmed"]

    def restart_test_cycle(self):
        """重新开始测试循环"""
        self.log_message("测试完成，准备开始下一轮测试")

        # 重置测试状态
        self.test_running = False
        self.start_all_btn.config(state="normal")
        self.stop_btn.config(state="disabled")

        # 不清空测试结果数据和日志，保留上次测试的完整结果

        # 直接弹出序列号输入窗口，开始新的测试循环
        if self.input_serial_number("输入新序列号", "请输入下一个设备的序列号"):
            # 序列号输入成功，会自动开始测试（在input_serial_number的confirm_serial中处理）
            pass
        else:
            # 用户取消输入，返回主界面等待操作
            self.log_message("用户取消序列号输入，返回主界面")
            self.log_message("可以查看测试结果或手动点击开始测试")

    def show_test_completion_dialog(self):
        """显示测试完成对话框"""
        # 计算测试结果统计
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.status == "通过")
        failed_tests = total_tests - passed_tests

        # 创建测试完成对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("测试完成")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        main_frame = ttk.Frame(dialog, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        if passed_tests == total_tests:
            title_text = "🎉 测试完成 - 全部通过！"
            title_color = "green"
        else:
            title_text = "⚠️ 测试完成 - 存在失败项"
            title_color = "orange"

        title_label = ttk.Label(main_frame, text=title_text, font=("Microsoft YaHei UI", 16, "bold"))
        title_label.pack(pady=(0, 20))

        # 测试结果统计
        stats_frame = ttk.LabelFrame(main_frame, text="测试结果统计", padding="15")
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        stats_text = f"""工序: {self.selected_process}
序列号: {self.current_serial_number}
总测试项目: {total_tests} 个
通过项目: {passed_tests} 个
失败项目: {failed_tests} 个
通过率: {(passed_tests/total_tests*100):.1f}%"""

        if hasattr(self, 'start_time') and hasattr(self, 'end_time'):
            duration = self.end_time - self.start_time
            stats_text += f"\n测试用时: {str(duration).split('.')[0]}"

        ttk.Label(stats_frame, text=stats_text, font=("Microsoft YaHei UI", 11), justify=tk.LEFT).pack(anchor=tk.W)

        # 失败项目详情（如果有）
        if failed_tests > 0:
            failed_frame = ttk.LabelFrame(main_frame, text="失败项目详情", padding="10")
            failed_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

            failed_text = tk.Text(failed_frame, height=6, font=("Microsoft YaHei UI", 10), wrap=tk.WORD)
            failed_scrollbar = ttk.Scrollbar(failed_frame, orient=tk.VERTICAL, command=failed_text.yview)
            failed_text.configure(yscrollcommand=failed_scrollbar.set)

            failed_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            failed_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            failed_info = ""
            for test_id, result in self.test_results.items():
                if result.status == "失败":
                    failed_info += f"• {result.test_name}: {result.message}\n"

            failed_text.insert(1.0, failed_info)
            failed_text.config(state=tk.DISABLED)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))

        def start_new_test():
            """开始新的测试"""
            dialog.destroy()
            # 输入新的序列号
            if self.input_serial_number("输入新序列号", "请输入下一个设备的序列号"):
                # 清空测试结果，准备新测试
                self.clear_test_data()
                self.init_test_tree()
                self.log_message(f"\n=== 开始新测试 ===")
                self.log_message(f"工序: {self.selected_process}")
                self.log_message(f"序列号: {self.current_serial_number}")
            else:
                # 用户取消，退出程序
                self.root.quit()

        def change_process():
            """更换工序"""
            dialog.destroy()
            # 重新选择工序
            if self.select_work_process():
                # 重新初始化测试项目
                self.init_test_projects()
                self.init_test_tree()
                # 输入序列号
                if self.input_serial_number():
                    self.log_message(f"\n=== 更换工序 ===")
                    self.log_message(f"新工序: {self.selected_process}")
                    self.log_message(f"序列号: {self.current_serial_number}")
                else:
                    self.root.quit()
            else:
                self.root.quit()

        def exit_program():
            """退出程序"""
            dialog.destroy()
            self.root.quit()

        # 按钮布局
        ttk.Button(button_frame, text="继续测试(相同工序)", command=start_new_test,
                  style="Success.TButton").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="更换工序", command=change_process).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="退出程序", command=exit_program).pack(side=tk.LEFT)

    def run_torch_test(self, test_project):
        """手电筒LED测试
        测试流程：
        1. 执行点亮手电筒命令
        2. 弹出人工确认窗口
        3. 关闭手电筒
        4. 根据确认结果返回测试状态
        """
        try:
            # 记录这是手电筒测试，已经在本函数中进行了手动确认
            # 这样run_test函数就不会再次调用ask_manual_confirmation
            test_project["_manual_check_done"] = True
            
            self.log_message("开始手电筒LED测试...")
            
            # 执行点亮手电筒命令
            cmd_on = "echo 255 > /sys/class/leds/torch/brightness"
            self.log_message(f"执行命令: adb shell {cmd_on}")
            
            result = subprocess.run(['adb', 'shell', cmd_on], capture_output=True, text=True, timeout=10)
            if result.returncode != 0:
                error_msg = f"手电筒控制命令执行失败: {result.stderr}"
                self.log_message(error_msg)
                self.test_results[test_project["id"]].message = "手电筒控制失败"
                return False
                
            self.log_message("手电筒控制命令执行成功")
            
            # 创建确认对话框 - 使用自定义超时对话框
            confirm = self.show_timeout_confirmation(
                "手电筒测试确认",
                "请确认：\n1. 手电筒是否已点亮？\n2. 亮度是否正常？\n\n注意：1分钟内无响应将自动判定为失败"
            )
            
            # 执行关闭手电筒命令
            cmd_off = "echo 0 > /sys/class/leds/torch/brightness"
            self.log_message(f"执行命令: adb shell {cmd_off}")

            try:
                off_result = subprocess.run(['adb', 'shell', cmd_off], capture_output=True, text=True, timeout=10)
                if off_result.returncode != 0:
                    self.log_message(f"关闭手电筒命令执行失败: {off_result.stderr}")
                else:
                    self.log_message("手电筒已关闭")
            except Exception as e:
                self.log_message(f"关闭手电筒时出错: {str(e)}")

            # 处理不同的响应结果，保留人工判断逻辑
            if confirm == "yes":
                self.log_message("✅ 手电筒测试通过 - 用户确认手电筒正常")
                self.test_results[test_project["id"]].message = "手电筒正常"
                return True
            elif confirm == "no":
                self.log_message("❌ 手电筒测试失败 - 用户确认手电筒异常")
                self.test_results[test_project["id"]].message = "手电筒异常"
                return False
            elif confirm == "timeout":
                self.log_message("⏰ 手电筒测试超时失败 - 1分钟内无人工确认")
                self.test_results[test_project["id"]].message = "测试超时，无人工确认"
                return False
            else:
                self.log_message("❌ 手电筒测试失败 - 未知响应")
                self.test_results[test_project["id"]].message = "未知响应"
                return False
                
        except subprocess.TimeoutExpired:
            error_msg = "手电筒测试超时"
            self.log_message(error_msg)
            self.test_results[test_project["id"]].message = error_msg
            return False
        except Exception as e:
            error_msg = f"手电筒测试出错: {str(e)}"
            self.log_message(error_msg)
            self.test_results[test_project["id"]].message = error_msg
            return False

def main():
    root = tk.Tk()
    app = WheelchairTester(root)
    root.mainloop()

if __name__ == "__main__":
    main() 