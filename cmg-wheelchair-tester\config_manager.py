#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理工具 - 用于维护工序和测试项目
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime

class ConfigManager:
    def __init__(self, root):
        self.root = root
        self.root.title("CMG测试配置管理工具")
        self.root.geometry("1200x800")
        
        # 配置文件路径
        self.config_file = "config.json"
        self.backup_dir = "config_backups"
        
        # 确保备份目录存在
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        # 加载配置
        self.config = self.load_config()
        self.test_projects = self.config.get("test_projects", [])
        self.work_processes = self.config.get("work_processes", {})
        
        self.setup_ui()
        self.refresh_all()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            messagebox.showerror("错误", f"加载配置文件失败: {str(e)}")
        
        return {"test_projects": [], "work_processes": {}}
    
    def save_config(self):
        """保存配置文件"""
        try:
            # 创建备份
            self.create_backup()
            
            # 更新配置
            self.config["test_projects"] = self.test_projects
            self.config["work_processes"] = self.work_processes
            
            # 保存配置
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            
            messagebox.showinfo("成功", "配置已保存")
            return True
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            return False
    
    def create_backup(self):
        """创建配置备份"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = os.path.join(self.backup_dir, f"config_backup_{timestamp}.json")
            
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
                
                print(f"配置备份已创建: {backup_file}")
        except Exception as e:
            print(f"创建备份失败: {str(e)}")
    
    def setup_ui(self):
        """设置UI界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建笔记本控件（标签页）
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True)
        
        # 测试项目管理标签页
        self.setup_test_projects_tab(notebook)
        
        # 工序管理标签页
        self.setup_work_processes_tab(notebook)
        
        # 配置预览标签页
        self.setup_preview_tab(notebook)
        
        # 底部按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(button_frame, text="保存配置", command=self.save_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="重新加载", command=self.reload_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导出配置", command=self.export_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="导入配置", command=self.import_config).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="恢复备份", command=self.restore_backup).pack(side=tk.LEFT, padx=(0, 10))
    
    def setup_test_projects_tab(self, notebook):
        """设置测试项目管理标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="测试项目管理")
        
        # 左侧项目列表
        left_frame = ttk.LabelFrame(frame, text="测试项目列表", padding="10")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 项目列表
        self.project_tree = ttk.Treeview(
            left_frame,
            columns=("id", "type", "description"),
            show="tree headings",
            height=20
        )
        
        self.project_tree.heading("#0", text="项目名称")
        self.project_tree.heading("id", text="项目ID")
        self.project_tree.heading("type", text="类型")
        self.project_tree.heading("description", text="描述")
        
        self.project_tree.column("#0", width=200)
        self.project_tree.column("id", width=150)
        self.project_tree.column("type", width=120)
        self.project_tree.column("description", width=250)
        
        self.project_tree.pack(fill=tk.BOTH, expand=True)
        self.project_tree.bind("<<TreeviewSelect>>", self.on_project_select)
        
        # 右侧项目编辑区域
        right_frame = ttk.LabelFrame(frame, text="项目编辑", padding="10")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.configure(width=400)
        
        # 项目编辑表单
        self.setup_project_form(right_frame)
    
    def setup_project_form(self, parent):
        """设置项目编辑表单"""
        # 项目名称
        ttk.Label(parent, text="项目名称:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.project_name_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.project_name_var, width=30).grid(row=0, column=1, sticky=tk.W, pady=2)
        
        # 项目ID
        ttk.Label(parent, text="项目ID:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.project_id_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.project_id_var, width=30).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # 项目类型
        ttk.Label(parent, text="项目类型:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.project_type_var = tk.StringVar()
        type_combo = ttk.Combobox(parent, textvariable=self.project_type_var, width=27)
        type_combo['values'] = [
            'connection_test', 'rom_version_test', 'usb_test', 'can_test', 'gps_test',
            '4g_test', 'key_test', 'led_test', 'torch_test', 'joystick_test',
            'camera_test', 'light_sensor_test', 'speaker_test', 'bluetooth_test',
            'wifi_test', 'sensor_fps_test'
        ]
        type_combo.grid(row=2, column=1, sticky=tk.W, pady=2)
        
        # 项目描述
        ttk.Label(parent, text="项目描述:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.project_desc_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.project_desc_var, width=30).grid(row=3, column=1, sticky=tk.W, pady=2)
        
        # 命令
        ttk.Label(parent, text="命令:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.project_command_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.project_command_var, width=30).grid(row=4, column=1, sticky=tk.W, pady=2)
        
        # 期望模式
        ttk.Label(parent, text="期望模式:").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.project_pattern_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.project_pattern_var, width=30).grid(row=5, column=1, sticky=tk.W, pady=2)
        
        # 手动检查
        self.project_manual_var = tk.BooleanVar()
        ttk.Checkbutton(parent, text="需要手动检查", variable=self.project_manual_var).grid(row=6, column=0, columnspan=2, sticky=tk.W, pady=5)
        
        # 按钮区域
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=7, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="新建项目", command=self.new_project).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存项目", command=self.save_project).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除项目", command=self.delete_project).pack(side=tk.LEFT, padx=(0, 5))
        
        # 项目详情显示区域
        detail_frame = ttk.LabelFrame(parent, text="项目详情", padding="5")
        detail_frame.grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.project_detail_text = tk.Text(detail_frame, height=10, width=45, font=("Consolas", 9))
        detail_scrollbar = ttk.Scrollbar(detail_frame, orient=tk.VERTICAL, command=self.project_detail_text.yview)
        self.project_detail_text.configure(yscrollcommand=detail_scrollbar.set)
        
        self.project_detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_work_processes_tab(self, notebook):
        """设置工序管理标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="工序管理")
        
        # 左侧工序列表
        left_frame = ttk.LabelFrame(frame, text="工序列表", padding="10")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # 工序列表
        self.process_tree = ttk.Treeview(
            left_frame,
            columns=("description", "count"),
            show="tree headings",
            height=15
        )
        
        self.process_tree.heading("#0", text="工序名称")
        self.process_tree.heading("description", text="描述")
        self.process_tree.heading("count", text="项目数量")
        
        self.process_tree.column("#0", width=200)
        self.process_tree.column("description", width=300)
        self.process_tree.column("count", width=100)
        
        self.process_tree.pack(fill=tk.BOTH, expand=True)
        self.process_tree.bind("<<TreeviewSelect>>", self.on_process_select)
        
        # 右侧工序编辑区域
        right_frame = ttk.LabelFrame(frame, text="工序编辑", padding="10")
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.configure(width=450)
        
        # 工序编辑表单
        self.setup_process_form(right_frame)
    
    def setup_process_form(self, parent):
        """设置工序编辑表单"""
        # 工序名称
        ttk.Label(parent, text="工序名称:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.process_name_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.process_name_var, width=35).grid(row=0, column=1, sticky=tk.W, pady=2)
        
        # 工序描述
        ttk.Label(parent, text="工序描述:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.process_desc_var = tk.StringVar()
        ttk.Entry(parent, textvariable=self.process_desc_var, width=35).grid(row=1, column=1, sticky=tk.W, pady=2)
        
        # 按钮区域
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        ttk.Button(button_frame, text="新建工序", command=self.new_process).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="保存工序", command=self.save_process).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="删除工序", command=self.delete_process).pack(side=tk.LEFT, padx=(0, 5))
        
        # 测试项目选择区域
        select_frame = ttk.LabelFrame(parent, text="选择测试项目", padding="5")
        select_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # 可用项目列表
        available_frame = ttk.Frame(select_frame)
        available_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        ttk.Label(available_frame, text="可用项目:").pack(anchor=tk.W)
        self.available_listbox = tk.Listbox(available_frame, height=12, width=25, selectmode=tk.MULTIPLE)
        available_scroll = ttk.Scrollbar(available_frame, orient=tk.VERTICAL, command=self.available_listbox.yview)
        self.available_listbox.configure(yscrollcommand=available_scroll.set)
        self.available_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        available_scroll.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 中间按钮
        middle_frame = ttk.Frame(select_frame)
        middle_frame.pack(side=tk.LEFT, padx=10)
        
        ttk.Button(middle_frame, text=">>", command=self.add_to_process, width=5).pack(pady=5)
        ttk.Button(middle_frame, text="<<", command=self.remove_from_process, width=5).pack(pady=5)
        ttk.Button(middle_frame, text="全选", command=self.add_all_to_process, width=5).pack(pady=5)
        ttk.Button(middle_frame, text="清空", command=self.clear_process, width=5).pack(pady=5)
        
        # 已选项目列表
        selected_frame = ttk.Frame(select_frame)
        selected_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        ttk.Label(selected_frame, text="已选项目:").pack(anchor=tk.W)
        self.selected_listbox = tk.Listbox(selected_frame, height=12, width=25, selectmode=tk.MULTIPLE)
        selected_scroll = ttk.Scrollbar(selected_frame, orient=tk.VERTICAL, command=self.selected_listbox.yview)
        self.selected_listbox.configure(yscrollcommand=selected_scroll.set)
        self.selected_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        selected_scroll.pack(side=tk.RIGHT, fill=tk.Y)
    
    def setup_preview_tab(self, notebook):
        """设置配置预览标签页"""
        frame = ttk.Frame(notebook)
        notebook.add(frame, text="配置预览")
        
        # 预览文本区域
        preview_frame = ttk.LabelFrame(frame, text="配置文件预览", padding="10")
        preview_frame.pack(fill=tk.BOTH, expand=True)
        
        self.preview_text = tk.Text(preview_frame, font=("Consolas", 10))
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_text.yview)
        self.preview_text.configure(yscrollcommand=preview_scrollbar.set)
        
        self.preview_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        preview_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 刷新按钮
        ttk.Button(frame, text="刷新预览", command=self.refresh_preview).pack(pady=5)

    def refresh_all(self):
        """刷新所有界面"""
        self.refresh_projects()
        self.refresh_processes()
        self.refresh_preview()

    def refresh_projects(self):
        """刷新测试项目列表"""
        # 清空现有项目
        for item in self.project_tree.get_children():
            self.project_tree.delete(item)

        # 添加项目到树中
        for project in self.test_projects:
            self.project_tree.insert("", "end",
                                   text=project.get("name", ""),
                                   values=(
                                       project.get("id", ""),
                                       project.get("type", ""),
                                       project.get("description", "")
                                   ))

    def refresh_processes(self):
        """刷新工序列表"""
        # 清空现有工序
        for item in self.process_tree.get_children():
            self.process_tree.delete(item)

        # 添加工序到树中
        for name, process in self.work_processes.items():
            count = len(process.get("test_ids", []))
            self.process_tree.insert("", "end",
                                   text=name,
                                   values=(
                                       process.get("description", ""),
                                       str(count)
                                   ))

        # 刷新可用项目列表
        self.refresh_available_projects()

    def refresh_available_projects(self):
        """刷新可用项目列表"""
        self.available_listbox.delete(0, tk.END)
        for project in self.test_projects:
            display_text = f"{project.get('name', '')} ({project.get('id', '')})"
            self.available_listbox.insert(tk.END, display_text)

    def refresh_preview(self):
        """刷新配置预览"""
        self.preview_text.delete(1.0, tk.END)

        # 更新配置
        self.config["test_projects"] = self.test_projects
        self.config["work_processes"] = self.work_processes

        # 格式化JSON
        try:
            preview_json = json.dumps(self.config, ensure_ascii=False, indent=4)
            self.preview_text.insert(1.0, preview_json)
        except Exception as e:
            self.preview_text.insert(1.0, f"JSON格式化错误: {str(e)}")

    def on_project_select(self, event):
        """项目选择事件"""
        selection = self.project_tree.selection()
        if not selection:
            return

        item = selection[0]
        project_name = self.project_tree.item(item, "text")

        # 查找对应的项目
        for project in self.test_projects:
            if project.get("name") == project_name:
                self.load_project_to_form(project)
                break

    def load_project_to_form(self, project):
        """加载项目到编辑表单"""
        self.project_name_var.set(project.get("name", ""))
        self.project_id_var.set(project.get("id", ""))
        self.project_type_var.set(project.get("type", ""))
        self.project_desc_var.set(project.get("description", ""))
        self.project_command_var.set(project.get("command", ""))
        self.project_pattern_var.set(project.get("expected_pattern", ""))
        self.project_manual_var.set(project.get("manual_check", False))

        # 显示项目详情
        self.project_detail_text.delete(1.0, tk.END)
        detail_json = json.dumps(project, ensure_ascii=False, indent=2)
        self.project_detail_text.insert(1.0, detail_json)

    def new_project(self):
        """新建项目"""
        # 清空表单
        self.project_name_var.set("")
        self.project_id_var.set("")
        self.project_type_var.set("")
        self.project_desc_var.set("")
        self.project_command_var.set("")
        self.project_pattern_var.set("")
        self.project_manual_var.set(False)
        self.project_detail_text.delete(1.0, tk.END)

    def save_project(self):
        """保存项目"""
        name = self.project_name_var.get().strip()
        project_id = self.project_id_var.get().strip()

        if not name or not project_id:
            messagebox.showerror("错误", "项目名称和ID不能为空")
            return

        # 创建项目对象
        project = {
            "id": project_id,
            "name": name,
            "description": self.project_desc_var.get().strip(),
            "type": self.project_type_var.get().strip()
        }

        # 添加可选字段
        command = self.project_command_var.get().strip()
        if command:
            project["command"] = command

        pattern = self.project_pattern_var.get().strip()
        if pattern:
            project["expected_pattern"] = pattern

        if self.project_manual_var.get():
            project["manual_check"] = True

        # 检查ID是否重复
        existing_index = -1
        for i, existing_project in enumerate(self.test_projects):
            if existing_project["id"] == project_id:
                existing_index = i
                break

        if existing_index >= 0:
            # 更新现有项目
            self.test_projects[existing_index] = project
            messagebox.showinfo("成功", "项目已更新")
        else:
            # 添加新项目
            self.test_projects.append(project)
            messagebox.showinfo("成功", "项目已添加")

        self.refresh_projects()
        self.refresh_available_projects()

    def delete_project(self):
        """删除项目"""
        selection = self.project_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的项目")
            return

        item = selection[0]
        project_name = self.project_tree.item(item, "text")

        if messagebox.askyesno("确认", f"确定要删除项目 '{project_name}' 吗？"):
            # 从列表中删除项目
            self.test_projects = [p for p in self.test_projects if p.get("name") != project_name]

            # 从所有工序中删除该项目
            for process in self.work_processes.values():
                test_ids = process.get("test_ids", [])
                # 找到要删除的项目ID
                project_id = None
                for p in self.test_projects:
                    if p.get("name") == project_name:
                        project_id = p.get("id")
                        break

                if project_id and project_id in test_ids:
                    test_ids.remove(project_id)

            self.refresh_all()
            messagebox.showinfo("成功", "项目已删除")

    def on_process_select(self, event):
        """工序选择事件"""
        selection = self.process_tree.selection()
        if not selection:
            return

        item = selection[0]
        process_name = self.process_tree.item(item, "text")

        if process_name in self.work_processes:
            process = self.work_processes[process_name]
            self.load_process_to_form(process_name, process)

    def load_process_to_form(self, name, process):
        """加载工序到编辑表单"""
        self.process_name_var.set(name)
        self.process_desc_var.set(process.get("description", ""))

        # 清空已选项目列表
        self.selected_listbox.delete(0, tk.END)

        # 加载已选项目
        test_ids = process.get("test_ids", [])
        for test_id in test_ids:
            # 查找项目名称
            for project in self.test_projects:
                if project.get("id") == test_id:
                    display_text = f"{project.get('name', '')} ({test_id})"
                    self.selected_listbox.insert(tk.END, display_text)
                    break

    def new_process(self):
        """新建工序"""
        self.process_name_var.set("")
        self.process_desc_var.set("")
        self.selected_listbox.delete(0, tk.END)

    def save_process(self):
        """保存工序"""
        name = self.process_name_var.get().strip()
        description = self.process_desc_var.get().strip()

        if not name:
            messagebox.showerror("错误", "工序名称不能为空")
            return

        # 获取已选项目ID
        test_ids = []
        for i in range(self.selected_listbox.size()):
            item_text = self.selected_listbox.get(i)
            # 从显示文本中提取ID
            if "(" in item_text and ")" in item_text:
                test_id = item_text.split("(")[-1].split(")")[0]
                test_ids.append(test_id)

        # 创建工序对象
        process = {
            "description": description,
            "test_ids": test_ids
        }

        self.work_processes[name] = process
        self.refresh_processes()
        messagebox.showinfo("成功", "工序已保存")

    def delete_process(self):
        """删除工序"""
        selection = self.process_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的工序")
            return

        item = selection[0]
        process_name = self.process_tree.item(item, "text")

        if messagebox.askyesno("确认", f"确定要删除工序 '{process_name}' 吗？"):
            del self.work_processes[process_name]
            self.refresh_processes()
            messagebox.showinfo("成功", "工序已删除")

    def add_to_process(self):
        """添加项目到工序"""
        selection = self.available_listbox.curselection()
        for index in selection:
            item_text = self.available_listbox.get(index)
            # 检查是否已存在
            if item_text not in [self.selected_listbox.get(i) for i in range(self.selected_listbox.size())]:
                self.selected_listbox.insert(tk.END, item_text)

    def remove_from_process(self):
        """从工序中移除项目"""
        selection = self.selected_listbox.curselection()
        for index in reversed(selection):  # 从后往前删除
            self.selected_listbox.delete(index)

    def add_all_to_process(self):
        """添加所有项目到工序"""
        self.selected_listbox.delete(0, tk.END)
        for i in range(self.available_listbox.size()):
            item_text = self.available_listbox.get(i)
            self.selected_listbox.insert(tk.END, item_text)

    def clear_process(self):
        """清空工序项目"""
        self.selected_listbox.delete(0, tk.END)

    def reload_config(self):
        """重新加载配置"""
        if messagebox.askyesno("确认", "重新加载将丢失未保存的更改，确定继续吗？"):
            self.config = self.load_config()
            self.test_projects = self.config.get("test_projects", [])
            self.work_processes = self.config.get("work_processes", {})
            self.refresh_all()
            messagebox.showinfo("成功", "配置已重新加载")

    def export_config(self):
        """导出配置"""
        filename = filedialog.asksaveasfilename(
            title="导出配置文件",
            defaultextension=".json",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                # 更新配置
                self.config["test_projects"] = self.test_projects
                self.config["work_processes"] = self.work_processes

                with open(filename, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, ensure_ascii=False, indent=4)

                messagebox.showinfo("成功", f"配置已导出到: {filename}")
            except Exception as e:
                messagebox.showerror("错误", f"导出失败: {str(e)}")

    def import_config(self):
        """导入配置"""
        filename = filedialog.askopenfilename(
            title="导入配置文件",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
        )

        if filename:
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    imported_config = json.load(f)

                if messagebox.askyesno("确认", "导入配置将覆盖当前配置，确定继续吗？"):
                    self.config = imported_config
                    self.test_projects = self.config.get("test_projects", [])
                    self.work_processes = self.config.get("work_processes", {})
                    self.refresh_all()
                    messagebox.showinfo("成功", "配置已导入")
            except Exception as e:
                messagebox.showerror("错误", f"导入失败: {str(e)}")

    def restore_backup(self):
        """恢复备份"""
        backup_files = []
        if os.path.exists(self.backup_dir):
            for file in os.listdir(self.backup_dir):
                if file.startswith("config_backup_") and file.endswith(".json"):
                    backup_files.append(file)

        if not backup_files:
            messagebox.showinfo("信息", "没有找到备份文件")
            return

        # 按时间排序，最新的在前面
        backup_files.sort(reverse=True)

        # 创建选择窗口
        backup_window = tk.Toplevel(self.root)
        backup_window.title("选择备份文件")
        backup_window.geometry("400x300")
        backup_window.transient(self.root)
        backup_window.grab_set()

        ttk.Label(backup_window, text="选择要恢复的备份文件:").pack(pady=10)

        backup_listbox = tk.Listbox(backup_window, height=10)
        backup_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        for backup_file in backup_files:
            backup_listbox.insert(tk.END, backup_file)

        def restore_selected():
            selection = backup_listbox.curselection()
            if not selection:
                messagebox.showwarning("警告", "请选择一个备份文件")
                return

            selected_file = backup_files[selection[0]]
            backup_path = os.path.join(self.backup_dir, selected_file)

            try:
                with open(backup_path, 'r', encoding='utf-8') as f:
                    backup_config = json.load(f)

                if messagebox.askyesno("确认", f"确定要恢复备份 '{selected_file}' 吗？"):
                    self.config = backup_config
                    self.test_projects = self.config.get("test_projects", [])
                    self.work_processes = self.config.get("work_processes", {})
                    self.refresh_all()
                    backup_window.destroy()
                    messagebox.showinfo("成功", "备份已恢复")
            except Exception as e:
                messagebox.showerror("错误", f"恢复备份失败: {str(e)}")

        button_frame = ttk.Frame(backup_window)
        button_frame.pack(pady=10)

        ttk.Button(button_frame, text="恢复", command=restore_selected).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=backup_window.destroy).pack(side=tk.LEFT, padx=5)

if __name__ == "__main__":
    root = tk.Tk()
    app = ConfigManager(root)
    root.mainloop()
