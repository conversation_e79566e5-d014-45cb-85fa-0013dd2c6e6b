#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LED功能的确认对话框
"""

import tkinter as tk
from tkinter import ttk
import sys
import os
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主程序类
from main import WheelchairTester

def test_led_function():
    """测试LED功能"""
    print("=== 测试LED功能 ===")
    
    # 创建测试器实例
    root = tk.Tk()
    tester = WheelchairTester(root)
    
    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return
    
    # 查找LED测试项目
    led_project = None
    for project in config.get("test_projects", []):
        if project.get("id") == "led_test":
            led_project = project
            break
    
    if not led_project:
        print("未找到LED测试项目配置")
        return
    
    print(f"找到LED测试项目: {led_project['name']}")
    print(f"测试类型: {led_project['type']}")
    print(f"需要人工确认: {led_project.get('manual_check', False)}")
    
    # 创建控制窗口
    control_window = tk.Toplevel(root)
    control_window.title("LED测试控制")
    control_window.geometry("400x300")
    
    main_frame = ttk.Frame(control_window, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="LED测试控制", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    status_var = tk.StringVar(value="准备就绪")
    status_label = ttk.Label(main_frame, textvariable=status_var, font=("Arial", 12))
    status_label.pack(pady=10)
    
    def run_led_test():
        """运行LED测试"""
        status_var.set("正在运行LED测试...")
        control_window.update()
        
        print("开始运行LED测试...")
        
        try:
            # 调用LED测试函数
            result = tester.run_led_test(led_project)
            
            if result:
                status_var.set("LED测试通过 ✅")
                print("LED测试通过")
            else:
                status_var.set("LED测试失败 ❌")
                print("LED测试失败")
                
        except Exception as e:
            status_var.set(f"LED测试出错: {str(e)}")
            print(f"LED测试出错: {e}")
    
    def test_dialog_only():
        """只测试确认对话框"""
        status_var.set("显示确认对话框...")
        control_window.update()
        
        print("显示确认对话框...")
        
        try:
            # 直接调用确认对话框
            result = tester.show_timeout_confirmation(
                "LED测试确认",
                "请确认：\n1. 背光灯是否已点亮？\n2. 背光灯颜色是否为蓝色？\n\n注意：1分钟内无响应将自动判定为失败"
            )
            
            status_var.set(f"对话框结果: {result}")
            print(f"对话框结果: {result}")
            
        except Exception as e:
            status_var.set(f"对话框出错: {str(e)}")
            print(f"对话框出错: {e}")
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=20)
    
    led_test_button = ttk.Button(
        button_frame,
        text="运行完整LED测试",
        command=run_led_test
    )
    led_test_button.pack(side=tk.LEFT, padx=(0, 10))
    
    dialog_test_button = ttk.Button(
        button_frame,
        text="只测试确认对话框",
        command=test_dialog_only
    )
    dialog_test_button.pack(side=tk.LEFT)
    
    # 说明文本
    info_frame = ttk.LabelFrame(main_frame, text="测试说明", padding="10")
    info_frame.pack(fill=tk.X, pady=20)
    
    info_text = """测试功能：
• 运行完整LED测试：执行LED点亮命令 → 显示确认对话框 → 关闭LED
• 只测试确认对话框：直接显示确认对话框，测试按钮功能

注意事项：
• 确认对话框应该显示"是 (通过)"和"否 (失败)"按钮
• 有60秒倒计时显示
• 支持键盘快捷键：Enter确认，Escape取消
• 超时后自动判定为失败"""
    
    info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
    info_label.pack(fill=tk.X)
    
    print("LED测试控制窗口已创建")
    print("点击按钮开始测试")
    
    root.mainloop()

if __name__ == "__main__":
    print("LED功能测试")
    print("=" * 50)
    
    test_led_function()
