[2025-07-05 21:04:45] 测试数据已清除
[2025-07-05 21:04:46] 测试数据已清除
[2025-07-05 21:04:51] 开始测试设备: 1123
[2025-07-05 21:04:51] 测试数据已清除
[2025-07-05 21:04:51] 开始执行全部测试...
[2025-07-05 21:04:51] 开始测试: USB关键器件测试
[2025-07-05 21:04:51] 执行USB设备检测...
[2025-07-05 21:04:51] 执行命令: adb shell lsusb
[2025-07-05 21:04:51] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:04:52] 开始测试: CAN0测试
[2025-07-05 21:04:53] 执行CAN0测试流程...
[2025-07-05 21:04:53] 执行命令: adb shell ip link set can0 down
[2025-07-05 21:04:53] CAN0 down失败: error: no devices/emulators found

[2025-07-05 21:04:54] 开始测试: GPS测试
[2025-07-05 21:04:54] 执行GPS测试...
[2025-07-05 21:04:54] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-05 21:04:54] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:04:54] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-05 21:04:54] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:04:54] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-05 21:04:54] 命令执行失败: error: no devices/emulators found

[2025-07-05 21:04:54] GPS信号检测失败
[2025-07-05 21:04:55] 开始测试: 4G模组测试
[2025-07-05 21:04:55] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-05 21:04:55] 监听线程已启动，等待串口数据...
[2025-07-05 21:04:57] 正在停止测试...
[2025-07-05 21:04:57] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-05 21:04:57] AT指令发送失败: error: no devices/emulators found

[2025-07-05 21:04:58] 全部测试完成
[2025-07-05 21:04:58] 测试记录已保存: records/1123_20250705_210458.json

