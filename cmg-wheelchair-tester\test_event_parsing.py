#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试事件解析功能
"""

def test_event_parsing():
    """测试事件解析功能"""
    print("=== 测试事件解析 ===")
    
    # 测试用的事件行
    test_lines = [
        "Event: time 1751301654.282726, type 1 (EV_KEY), code 659 (?), value 0",
        "Event: time 1751301654.282726, type 1 (EV_KEY), code 659 (?), value 1",
        "Event: time 1751301654.282726, type 1 (EV_KEY), code 656 (?), value 1",
        "Event: time 1751301654.282726, type 1 (EV_KEY), code 656 (?), value 0",
        "Event: time 1751301654.282726, type 1 (EV_KEY), code 663 (?), value 1",
        "Event: time 1751301654.282726, type 1 (EV_KEY), code 663 (?), value 0",
        "Event: time 1751301654.282726, type 0 (EV_SYN), code 0 (SYN_REPORT), value 0",  # 非按键事件
        "Some other output line",  # 非事件行
    ]
    
    expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
    detected_keys = set()
    key_press_states = {}
    
    print("开始解析测试事件...")
    
    for line in test_lines:
        print(f"\n处理行: {line}")
        
        # 解析按键事件
        if "EV_KEY" in line and "code " in line:
            try:
                print(f"  -> 这是一个按键事件")
                
                key_code = None
                value = None
                
                # 解析格式: Event: time xxx, type 1 (EV_KEY), code 659 (?), value 0
                parts = line.split(", ")
                for part in parts:
                    part = part.strip()
                    if "code " in part:
                        # 提取 "code 659 (?)" 中的 659
                        code_match = part.split("code ")[1].split(" ")[0]
                        key_code = int(code_match)
                        print(f"  -> 提取到按键码: {key_code}")
                    elif "value " in part:
                        # 提取 "value 0" 中的 0
                        value = int(part.split("value ")[1])
                        print(f"  -> 提取到值: {value}")
                
                print(f"  -> 解析结果: key_code={key_code}, value={value}")
                
                if key_code in expected_keys and value is not None:
                    if value == 1:
                        # 按键按下
                        key_press_states[key_code] = True
                        print(f"  -> ✓ 按键{key_code}按下")
                    elif value == 0 and key_press_states.get(key_code, False):
                        # 按键松开
                        detected_keys.add(key_code)
                        print(f"  -> ✓ 按键{key_code}测试完成 (按下->松开)")
                    elif value == 0:
                        print(f"  -> 按键{key_code}松开，但未记录按下状态")
                else:
                    if key_code not in expected_keys:
                        print(f"  -> 忽略按键{key_code}（不在预期列表中）")
            
            except (ValueError, IndexError) as e:
                print(f"  -> 解析失败: {str(e)}")
        else:
            print(f"  -> 跳过非按键事件")
    
    print(f"\n=== 解析结果 ===")
    print(f"检测到的按键: {sorted(detected_keys)}")
    print(f"按键按下状态: {key_press_states}")
    print(f"测试结果: {'通过' if len(detected_keys) >= 3 else '需要更多按键'}")

def test_real_evtest():
    """测试真实的evtest输出"""
    print("\n=== 测试真实evtest输出 ===")
    
    import subprocess
    import threading
    import time
    import queue
    
    try:
        print("启动evtest...")
        process = subprocess.Popen(
            ['adb', 'shell', 'evtest', '/dev/input/event5'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        time.sleep(2)
        print("evtest已启动，请在10秒内按几个按键...")
        
        # 输出队列
        output_queue = queue.Queue()
        
        def read_output():
            try:
                while True:
                    line = process.stdout.readline()
                    if not line:
                        break
                    output_queue.put(line.strip())
            except Exception as e:
                output_queue.put(f"ERROR: {str(e)}")
        
        # 启动读取线程
        threading.Thread(target=read_output, daemon=True).start()
        
        # 监听10秒
        expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
        detected_keys = set()
        key_press_states = {}
        start_time = time.time()
        
        while time.time() - start_time < 10 and process.poll() is None:
            try:
                line = output_queue.get_nowait()
            except queue.Empty:
                time.sleep(0.1)
                continue
            
            if not line or line.startswith("ERROR:"):
                continue
            
            print(f"收到: {line}")
            
            # 解析按键事件
            if "EV_KEY" in line and "code " in line:
                try:
                    key_code = None
                    value = None
                    
                    parts = line.split(", ")
                    for part in parts:
                        part = part.strip()
                        if "code " in part:
                            code_match = part.split("code ")[1].split(" ")[0]
                            key_code = int(code_match)
                        elif "value " in part:
                            value = int(part.split("value ")[1])
                    
                    if key_code in expected_keys and value is not None:
                        if value == 1:
                            key_press_states[key_code] = True
                            print(f"  -> ✓ 按键{key_code}按下")
                        elif value == 0 and key_press_states.get(key_code, False):
                            detected_keys.add(key_code)
                            print(f"  -> ✓ 按键{key_code}完成")
                
                except (ValueError, IndexError) as e:
                    print(f"  -> 解析失败: {e}")
        
        # 停止进程
        try:
            process.terminate()
            process.wait(timeout=2)
        except:
            process.kill()
        
        print(f"\n真实测试结果:")
        print(f"检测到的按键: {sorted(detected_keys)}")
        print(f"按键按下状态: {key_press_states}")
        
    except Exception as e:
        print(f"真实测试失败: {e}")

if __name__ == "__main__":
    print("事件解析测试工具")
    print("=" * 50)
    
    # 测试事件解析
    test_event_parsing()
    
    # 测试真实evtest（如果有设备连接）
    try:
        import subprocess
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            devices = result.stdout.strip().split('\n')[1:]
            if any('device' in line for line in devices):
                print("\n检测到ADB设备，进行真实测试...")
                test_real_evtest()
            else:
                print("\n没有ADB设备连接，跳过真实测试")
        else:
            print("\nADB不可用，跳过真实测试")
    except Exception as e:
        print(f"\n检查ADB失败: {e}")
    
    print("\n测试完成")
