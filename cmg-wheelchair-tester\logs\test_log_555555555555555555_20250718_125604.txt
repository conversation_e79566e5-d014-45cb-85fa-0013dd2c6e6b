[2025-07-18 12:54:23] === 开始新的测试 ===
[2025-07-18 12:54:23] 开始测试
[2025-07-18 12:54:23] 工序: 右手臂功能测试
[2025-07-18 12:54:23] 序列号: 555555555555555555
[2025-07-18 12:54:23] 开始运行 右手臂功能测试 工序测试，共 17 个项目
[2025-07-18 12:54:23] 
开始执行: 设备连接状态检测
[2025-07-18 12:54:23] 设备连接测试通过，检测到 1 个设备
[2025-07-18 12:54:23] 设备: ?	device
[2025-07-18 12:54:23] 
开始执行: 3568版本测试
[2025-07-18 12:54:23] 执行3568版本测试...
[2025-07-18 12:54:23] 读取ROM版本号...
[2025-07-18 12:54:23] 执行命令: adb shell uname -a
[2025-07-18 12:54:24] uname命令执行成功
[2025-07-18 12:54:24] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 12:54:24] ✅ 3568版本测试成功
[2025-07-18 12:54:24] ROM版本号: Jul 9 12:01:12
[2025-07-18 12:54:24] ✅ 确认为RK3568平台
[2025-07-18 12:54:24] 
开始执行: USB关键器件检测
[2025-07-18 12:54:24] 执行USB设备检测...
[2025-07-18 12:54:24] 执行命令: adb shell lsusb
[2025-07-18 12:54:24] 设备返回数据:
[2025-07-18 12:54:24] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 12:54:24] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 12:54:24] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 12:54:24] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 12:54:24] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 12:54:24] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 12:54:24] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 12:54:24] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 12:54:24] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 12:54:24] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 12:54:24] 总共解析到 9 个设备
[2025-07-18 12:54:24] ✅ 所有预期的设备ID都已找到
[2025-07-18 12:54:24] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 12:54:24] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 12:54:24] ✅ USB设备检测通过
[2025-07-18 12:54:25] 
开始执行: CAN0测试
[2025-07-18 12:54:25] 执行CAN0测试流程...
[2025-07-18 12:54:25] 执行命令: adb shell ip link set can0 down
[2025-07-18 12:54:25] CAN0已关闭
[2025-07-18 12:54:25] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 12:54:25] CAN0已启动
[2025-07-18 12:54:25] CAN监听线程已启动...
[2025-07-18 12:54:26] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 12:54:26] CAN测试数据已发送，等待监听返回...
[2025-07-18 12:54:26] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 12:54:28] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 12:54:29] 
开始执行: GPS测试
[2025-07-18 12:54:29] 执行GPS测试...
[2025-07-18 12:54:29] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 12:54:39] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 12:54:40] 
开始执行: 4G模组版本测试
[2025-07-18 12:54:40] 执行4G模组版本测试...（获取模组型号）
[2025-07-18 12:54:40] 监听线程已启动，等待串口数据...
[2025-07-18 12:54:42] 执行命令: adb shell "echo -e 'ATI\r' > /dev/ttyUSB0"
[2025-07-18 12:54:42] ATI指令已发送，等待串口返回模组版本信息...
[2025-07-18 12:54:42] 串口输出: ATI
[2025-07-18 12:54:42] 串口输出: 
[2025-07-18 12:54:42] 串口输出: 
[2025-07-18 12:54:42] 串口输出: Quectel
[2025-07-18 12:54:42] 串口输出: 
[2025-07-18 12:54:42] 串口输出: EG912U
[2025-07-18 12:54:42] 串口输出: 
[2025-07-18 12:54:42] 串口输出: Revision: EG912UGLAAR03A15M08
[2025-07-18 12:54:42] 检测到模组版本: EG912UGLAAR03A15M08
[2025-07-18 12:54:44] ✅ 4G模组版本测试成功，EG912UGLAAR03A15M08
[2025-07-18 12:54:45] 
开始执行: 按键测试
[2025-07-18 12:54:45] 开始按键测试...
[2025-07-18 12:54:45] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 12:54:49] 原始事件: Event: time 1752814490.166671, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-18 12:54:49] 解析结果: key_code=662, value=1
[2025-07-18 12:54:49] ✓ 检测到档位减(662)按下
[2025-07-18 12:54:49] 原始事件: Event: time 1752814490.340166, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-18 12:54:49] 解析结果: key_code=662, value=0
[2025-07-18 12:54:49] ✓ 档位减(662)测试通过
[2025-07-18 12:54:50] 原始事件: Event: time 1752814491.116686, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-18 12:54:50] 解析结果: key_code=658, value=1
[2025-07-18 12:54:50] ✓ 检测到喇叭键(658)按下
[2025-07-18 12:54:50] 原始事件: Event: time 1752814491.316709, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-18 12:54:50] 解析结果: key_code=658, value=0
[2025-07-18 12:54:50] ✓ 喇叭键(658)测试通过
[2025-07-18 12:54:57] 原始事件: Event: time 1752814497.566680, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-18 12:54:57] 解析结果: key_code=661, value=1
[2025-07-18 12:54:57] ✓ 检测到静音键(661)按下
[2025-07-18 12:54:57] 原始事件: Event: time 1752814497.767026, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-18 12:54:57] 解析结果: key_code=661, value=0
[2025-07-18 12:54:57] ✓ 静音键(661)测试通过
[2025-07-18 12:54:58] 原始事件: Event: time 1752814498.816709, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 12:54:58] 解析结果: key_code=663, value=1
[2025-07-18 12:54:58] ✓ 检测到语音键(663)按下
[2025-07-18 12:54:58] 原始事件: Event: time 1752814499.016663, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 12:54:58] 解析结果: key_code=663, value=0
[2025-07-18 12:54:58] ✓ 语音键(663)测试通过
[2025-07-18 12:54:59] 原始事件: Event: time 1752814499.916634, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-18 12:54:59] 解析结果: key_code=657, value=1
[2025-07-18 12:54:59] ✓ 检测到SOS键(657)按下
[2025-07-18 12:54:59] 原始事件: Event: time 1752814500.189958, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-18 12:54:59] 解析结果: key_code=657, value=0
[2025-07-18 12:54:59] ✓ SOS键(657)测试通过
[2025-07-18 12:55:01] 原始事件: Event: time 1752814501.590106, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-18 12:55:01] 解析结果: key_code=659, value=1
[2025-07-18 12:55:01] ✓ 检测到档位加(659)按下
[2025-07-18 12:55:01] 原始事件: Event: time 1752814501.690005, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-18 12:55:01] 解析结果: key_code=659, value=0
[2025-07-18 12:55:01] ✓ 档位加(659)测试通过
[2025-07-18 12:55:01] 原始事件: Event: time 1752814502.366681, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-18 12:55:01] 解析结果: key_code=660, value=1
[2025-07-18 12:55:01] ✓ 检测到智驾键(660)按下
[2025-07-18 12:55:01] 原始事件: Event: time 1752814502.516794, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-18 12:55:01] 解析结果: key_code=660, value=0
[2025-07-18 12:55:01] ✓ 智驾键(660)测试通过
[2025-07-18 12:55:03] 原始事件: Event: time 1752814503.590006, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-18 12:55:03] 解析结果: key_code=656, value=1
[2025-07-18 12:55:03] ✓ 检测到锁定键(656)按下
[2025-07-18 12:55:03] 原始事件: Event: time 1752814503.740034, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-18 12:55:03] 解析结果: key_code=656, value=0
[2025-07-18 12:55:03] ✓ 锁定键(656)测试通过
[2025-07-18 12:55:03] 按键测试完成 - 检测到8个按键
[2025-07-18 12:55:05] 
开始执行: 按键灯测试
[2025-07-18 12:55:05] 开始LED背光灯测试...
[2025-07-18 12:55:05] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 12:55:06] LED控制命令执行成功
[2025-07-18 12:55:06] 🔧 显示确认对话框: LED测试确认
[2025-07-18 12:55:06] 🔧 对话框窗口已创建
[2025-07-18 12:55:06] 🔧 '是'按钮已创建
[2025-07-18 12:55:06] 🔧 '否'按钮已创建
[2025-07-18 12:55:06] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 12:55:07] 👤 用户选择: 是 (测试通过)
[2025-07-18 12:55:07] 🔧 对话框关闭，用户响应: yes
[2025-07-18 12:55:07] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-18 12:55:07] LED灯已关闭
[2025-07-18 12:55:07] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-18 12:55:08] 
开始执行: 手电筒测试
[2025-07-18 12:55:08] 开始手电筒LED测试...
[2025-07-18 12:55:08] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 12:55:08] 手电筒控制命令执行成功
[2025-07-18 12:55:08] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-18 12:55:08] 🔧 对话框窗口已创建
[2025-07-18 12:55:08] 🔧 '是'按钮已创建
[2025-07-18 12:55:08] 🔧 '否'按钮已创建
[2025-07-18 12:55:08] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 12:55:09] 👤 用户选择: 是 (测试通过)
[2025-07-18 12:55:09] 🔧 对话框关闭，用户响应: yes
[2025-07-18 12:55:09] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-18 12:55:09] 手电筒已关闭
[2025-07-18 12:55:09] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-18 12:55:10] 
开始执行: 摇杆使能测试
[2025-07-18 12:55:10] 执行摇杆测试...
[2025-07-18 12:55:10] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 12:55:10] 命令执行成功
[2025-07-18 12:55:10] 返回数据: 255
[2025-07-18 12:55:10] 摇杆测试通过，值: 255
[2025-07-18 12:55:10] 
开始执行: 前摄像头测试
[2025-07-18 12:55:10] 开始执行前摄像头测试...
[2025-07-18 12:55:10] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 12:55:10] 命令执行成功，返回: 无输出
[2025-07-18 12:55:10] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-18 12:55:11] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.327222356
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-18 12:55:11] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-18 12:55:11] 拉取命令执行成功
[2025-07-18 12:55:11] 返回数据: [ 37%] /data/camera/cam0_3840x2160.jpg
[ 75%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 17.4 MB/s (173663 bytes in 0.010s)
[2025-07-18 12:55:11] 图片已保存: cam0_3840x2160.jpg
[2025-07-18 12:55:13] 用户确认结果: 通过
[2025-07-18 12:55:13] 
开始执行: 光感测试
[2025-07-18 12:55:13] 执行光感测试...
[2025-07-18 12:55:13] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 12:55:19] 光感测试完成 - 检测到数值变化
[2025-07-18 12:55:19] 数值从 1 变化到 0
[2025-07-18 12:55:19] 
开始执行: 回充摄像头测试
[2025-07-18 12:55:19] 开始执行回充摄像头测试...
[2025-07-18 12:55:20] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 12:55:20] 命令执行成功，返回: 无输出
[2025-07-18 12:55:20] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-18 12:55:21] 命令执行成功，返回: 无输出
[2025-07-18 12:55:21] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-18 12:55:21] 拉取命令执行成功
[2025-07-18 12:55:21] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 7.6 MB/s (41448 bytes in 0.005s)
[2025-07-18 12:55:21] 图片已保存: output.jpg
[2025-07-18 12:55:23] 用户确认结果: 通过
[2025-07-18 12:55:23] 
开始执行: 喇叭测试
[2025-07-18 12:55:24] 执行喇叭测试...
[2025-07-18 12:55:24] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 12:55:38] 命令执行成功
[2025-07-18 12:55:38] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-18 12:55:38] 音频播放完成
[2025-07-18 12:55:38] 
开始执行: 蓝牙测试
[2025-07-18 12:55:38] 执行蓝牙测试...
[2025-07-18 12:55:38] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 12:55:38] 执行命令: adb shell bluetoothctl show
[2025-07-18 12:55:38] 命令执行成功
[2025-07-18 12:55:38] 返回数据:
[2025-07-18 12:55:38]   Controller 24:21:5E:C0:30:4A (public)
[2025-07-18 12:55:38]   Name: CMG-1
[2025-07-18 12:55:38]   Alias: CMG-1
[2025-07-18 12:55:38]   Class: 0x006c0000 (7077888)
[2025-07-18 12:55:38]   Powered: yes
[2025-07-18 12:55:38]   PowerState: on
[2025-07-18 12:55:38]   Discoverable: no
[2025-07-18 12:55:38]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-18 12:55:38]   Pairable: yes
[2025-07-18 12:55:38]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-18 12:55:38]   Modalias: usb:v1D6Bp0246d0544
[2025-07-18 12:55:38]   Discovering: no
[2025-07-18 12:55:38]   Roles: central
[2025-07-18 12:55:38]   Roles: peripheral
[2025-07-18 12:55:38]   Advertising Features:
[2025-07-18 12:55:38]   ActiveInstances: 0x00 (0)
[2025-07-18 12:55:38]   SupportedInstances: 0x10 (16)
[2025-07-18 12:55:38]   SupportedIncludes: tx-power
[2025-07-18 12:55:38]   SupportedIncludes: appearance
[2025-07-18 12:55:38]   SupportedIncludes: local-name
[2025-07-18 12:55:38]   SupportedSecondaryChannels: 1M
[2025-07-18 12:55:38]   SupportedSecondaryChannels: 2M
[2025-07-18 12:55:38]   SupportedSecondaryChannels: Coded
[2025-07-18 12:55:38]   SupportedCapabilities Key: MaxAdvLen
[2025-07-18 12:55:38]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 12:55:38]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-18 12:55:38]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 12:55:38]   SupportedFeatures: CanSetTxPower
[2025-07-18 12:55:38]   SupportedFeatures: HardwareOffload
[2025-07-18 12:55:38]   Advertisement Monitor Features:
[2025-07-18 12:55:38]   SupportedMonitorTypes: or_patterns
[2025-07-18 12:55:38] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-18 12:55:38] 蓝牙控制器MAC地址: 24:21:5E:C0:30:4A
[2025-07-18 12:55:39] 
开始执行: 4G网络测试
[2025-07-18 12:55:39] 执行4G网络测试...
[2025-07-18 12:55:39] 第一步：关闭WiFi网络...
[2025-07-18 12:55:39] WiFi已关闭，等待4G网络连接...
[2025-07-18 12:55:42] 第二步：使用4G网络进行ping测试...
[2025-07-18 12:55:42] ping目标: www.baidu.com
[2025-07-18 12:55:42] 测试时长: 10秒
[2025-07-18 12:55:42] 4G网络ping测试失败: ping: www.baidu.com: Temporary failure in name resolution

[2025-07-18 12:55:42] 第三步：重新打开WiFi网络...
[2025-07-18 12:55:42] WiFi已重新打开
[2025-07-18 12:55:45] 
开始执行: WiFi测试
[2025-07-18 12:55:45] 执行WiFi测试...
[2025-07-18 12:55:45] 第一步：关闭4G网络...
[2025-07-18 12:55:45] 关闭usb0接口（4G网络）...
[2025-07-18 12:55:45] 执行命令: adb shell ifconfig usb0 down
[2025-07-18 12:55:45] ✅ 4G网络已关闭
[2025-07-18 12:55:47] 第二步：连接WiFi网络...
[2025-07-18 12:55:47] 停止现有的wpa_supplicant进程...
[2025-07-18 12:55:47] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 12:55:47] 清理wpa_supplicant socket文件...
[2025-07-18 12:55:47] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 12:55:47] 关闭wlan0接口...
[2025-07-18 12:55:47] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 12:55:47] 连接WiFi网络...
[2025-07-18 12:55:47] 执行WiFi连接命令...
[2025-07-18 12:55:47] SSID: Orion_SZ_5G
[2025-07-18 12:55:50] WiFi连接命令执行完成，返回数据:
[2025-07-18 12:55:50]   Successfully initialized wpa_supplicant
[2025-07-18 12:55:50]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-18 12:55:50]   1
[2025-07-18 12:55:50]   OK
[2025-07-18 12:55:50]   OK
[2025-07-18 12:55:50]   OK
[2025-07-18 12:55:50]   deleting routers
[2025-07-18 12:55:50]   adding dns ************
[2025-07-18 12:55:50]   adding dns ***********
[2025-07-18 12:55:50]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-18 12:55:50]   SSID: Orion_SZ_5G
[2025-07-18 12:55:50]   freq: 5300
[2025-07-18 12:55:50]   RX: 53795 bytes (511 packets)
[2025-07-18 12:55:50]   TX: 38243 bytes (456 packets)
[2025-07-18 12:55:50]   signal: -51 dBm
[2025-07-18 12:55:50]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-18 12:55:50]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-18 12:55:50]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-18 12:55:50]   link/ether 24:21:5e:c0:3c:0b brd ff:ff:ff:ff:ff:ff
[2025-07-18 12:55:50]   inet 192.168.20.167/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-18 12:55:50]   valid_lft forever preferred_lft forever
[2025-07-18 12:55:50]   inet6 fe80::c5c9:135f:3de4:4a19/64 scope link tentative
[2025-07-18 12:55:50]   valid_lft forever preferred_lft forever
[2025-07-18 12:55:50] ✅ WiFi连接成功
[2025-07-18 12:55:50] 等待网络稳定...
[2025-07-18 12:55:53] 第二步：开始网络发包延时测试...
[2025-07-18 12:55:53] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-18 12:55:53] 正在进行10秒钟的网络延时测试...
[2025-07-18 12:56:02] ping命令执行成功
[2025-07-18 12:56:02] 返回数据:
[2025-07-18 12:56:02]   PING www.a.shifen.com (157.148.69.151) 56(84) bytes of data.
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=1 ttl=54 time=9.58 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=2 ttl=54 time=11.3 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=3 ttl=54 time=10.5 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=4 ttl=54 time=10.3 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=5 ttl=54 time=11.1 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=6 ttl=54 time=10.8 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=7 ttl=54 time=13.7 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=8 ttl=54 time=10.9 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=9 ttl=54 time=9.31 ms
[2025-07-18 12:56:02]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=10 ttl=54 time=10.3 ms
[2025-07-18 12:56:02]   --- www.a.shifen.com ping statistics ---
[2025-07-18 12:56:02]   10 packets transmitted, 10 received, 0% packet loss, time 9011ms
[2025-07-18 12:56:02]   rtt min/avg/max/mdev = 9.312/10.774/13.712/1.148 ms
[2025-07-18 12:56:02] 检测到延时: 9.58 ms
[2025-07-18 12:56:02] 检测到延时: 11.3 ms
[2025-07-18 12:56:02] 检测到延时: 10.5 ms
[2025-07-18 12:56:02] 检测到延时: 10.3 ms
[2025-07-18 12:56:02] 检测到延时: 11.1 ms
[2025-07-18 12:56:02] 检测到延时: 10.8 ms
[2025-07-18 12:56:02] 检测到延时: 13.7 ms
[2025-07-18 12:56:02] 检测到延时: 10.9 ms
[2025-07-18 12:56:02] 检测到延时: 9.31 ms
[2025-07-18 12:56:02] 检测到延时: 10.3 ms
[2025-07-18 12:56:02] ✅ WiFi延时测试成功
[2025-07-18 12:56:02] 发包数量: 10 个
[2025-07-18 12:56:02] 平均延时: 10.78 ms
[2025-07-18 12:56:02] 最小延时: 9.31 ms
[2025-07-18 12:56:02] 最大延时: 13.70 ms
[2025-07-18 12:56:02] 第三步：恢复4G网络...
[2025-07-18 12:56:02] 重新打开usb0接口（4G网络）...
[2025-07-18 12:56:02] 执行命令: adb shell ifconfig usb0 up
[2025-07-18 12:56:02] ✅ 4G网络已恢复
[2025-07-18 12:56:04] 
测试完成 - 通过率: 15/17
[2025-07-18 12:56:04] ❌ 存在测试失败项！
[2025-07-18 12:56:04] 测试记录已保存: records/555555555555555555_20250718_125604.json
[2025-07-18 12:56:04] 测试日志已保存: records/555555555555555555_20250718_125604.log

