{"sn": "888888888888", "timestamp": "2025-07-18T13:40:50.774285", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-18T13:39:11.277305"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-18T13:39:11.829411"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-18T13:39:12.327023"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-18T13:39:12.827417"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-18T13:39:16.480985"}, "4g_test": {"test_name": "4G模组版本测试", "status": "通过", "message": "EG912UGLAAR03A15M08", "timestamp": "2025-07-18T13:39:27.222256"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-18T13:39:32.072941"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-18T13:39:53.400753"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-18T13:39:55.434491"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-18T13:39:56.963204"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T13:39:57.484588"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-18T13:40:00.316526"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T13:40:06.902256"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-18T13:40:09.587229"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 24:21:5E:C0:30:4A", "timestamp": "2025-07-18T13:40:24.263602"}, "4g_network_test": {"test_name": "4G网络测试", "status": "失败", "message": "4G网络不通", "timestamp": "2025-07-18T13:40:24.917210"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "平均延时: 11.64ms (优秀)", "timestamp": "2025-07-18T13:40:30.534049"}}}