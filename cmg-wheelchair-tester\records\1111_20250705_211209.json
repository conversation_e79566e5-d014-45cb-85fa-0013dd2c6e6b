{"sn": "1111", "timestamp": "2025-07-05T21:12:09.661973", "results": {"usb_test": {"test_name": "USB关键器件测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T21:11:51.012876"}, "can_test": {"test_name": "CAN0测试", "status": "失败", "message": "CAN0 down失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T21:11:52.171567"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "无GPS信号", "timestamp": "2025-07-05T21:11:53.486454"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "AT指令发送失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T21:11:56.680674"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成", "timestamp": "2025-07-05T21:11:57.733581"}, "led_test": {"test_name": "按键灯测试", "status": "失败", "message": "LED控制失败", "timestamp": "2025-07-05T21:11:58.876632"}, "torch_test": {"test_name": "手电筒测试", "status": "失败", "message": "手电筒控制失败", "timestamp": "2025-07-05T21:12:00.033580"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T21:12:01.176088"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-05T21:12:02.534973"}, "light_sensor_test": {"test_name": "光感测试", "status": "失败", "message": "光感异常", "timestamp": "2025-07-05T21:12:03.778919"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-05T21:12:05.051865"}, "speaker_test": {"test_name": "喇叭测试", "status": "失败", "message": "播放失败", "timestamp": "2025-07-05T21:12:06.168263"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T21:12:07.485916"}, "wifi_test": {"test_name": "WiFi测试", "status": "失败", "message": "WiFi连接失败", "timestamp": "2025-07-05T21:12:08.628594"}}}