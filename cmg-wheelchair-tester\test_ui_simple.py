#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的UI更新测试
"""

import tkinter as tk
from tkinter import ttk
import time

def test_simple_ui_update():
    """测试简单的UI更新"""
    print("=== 简单UI更新测试 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("简单UI更新测试")
    root.geometry("400x300")
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(
        main_frame,
        text="简单UI更新测试",
        font=("Arial", 16, "bold")
    )
    title_label.pack(pady=(0, 20))
    
    # 状态显示
    status_var = tk.StringVar(value="准备就绪")
    status_label = ttk.Label(
        main_frame,
        textvariable=status_var,
        font=("Arial", 12)
    )
    status_label.pack(pady=10)
    
    # 按键状态显示
    expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
    key_vars = {}
    key_labels = {}
    
    keys_frame = ttk.LabelFrame(main_frame, text="按键状态", padding="10")
    keys_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    for i, key_code in enumerate(expected_keys):
        row = i // 4
        col = i % 4
        
        key_frame = ttk.Frame(keys_frame)
        key_frame.grid(row=row, column=col, padx=5, pady=2, sticky="ew")
        
        key_vars[key_code] = tk.StringVar(value="未测试")
        
        key_label = ttk.Label(
            key_frame,
            text=f"按键{key_code}:",
            font=("Arial", 9)
        )
        key_label.pack(side=tk.LEFT)
        
        status_label = ttk.Label(
            key_frame,
            textvariable=key_vars[key_code],
            font=("Arial", 9, "bold"),
            foreground="gray"
        )
        status_label.pack(side=tk.LEFT, padx=(5, 0))
        key_labels[key_code] = status_label
    
    # 配置网格权重
    for i in range(4):
        keys_frame.columnconfigure(i, weight=1)
    
    # 模拟按键测试
    def simulate_key_test():
        """模拟按键测试过程"""
        status_var.set("开始模拟测试...")
        root.update_idletasks()
        
        for i, key_code in enumerate(expected_keys):
            # 模拟按键按下
            status_var.set(f"模拟按键{key_code}按下...")
            key_vars[key_code].set("按下")
            key_labels[key_code].configure(foreground="orange")
            root.update_idletasks()
            time.sleep(0.5)
            
            # 模拟按键松开
            status_var.set(f"模拟按键{key_code}松开...")
            key_vars[key_code].set("完成")
            key_labels[key_code].configure(foreground="green")
            root.update_idletasks()
            time.sleep(0.5)
            
            # 更新进度
            status_var.set(f"已完成 {i+1}/8 个按键测试")
            root.update_idletasks()
        
        status_var.set("模拟测试完成!")
        root.update_idletasks()
    
    # 重置按钮
    def reset_test():
        """重置测试状态"""
        status_var.set("准备就绪")
        for key_code in expected_keys:
            key_vars[key_code].set("未测试")
            key_labels[key_code].configure(foreground="gray")
        root.update_idletasks()
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    simulate_button = ttk.Button(
        button_frame,
        text="模拟测试",
        command=simulate_key_test
    )
    simulate_button.pack(side=tk.LEFT, padx=(0, 10))
    
    reset_button = ttk.Button(
        button_frame,
        text="重置",
        command=reset_test
    )
    reset_button.pack(side=tk.LEFT)
    
    print("简单UI测试窗口已创建")
    print("点击'模拟测试'按钮查看UI更新效果")
    
    root.mainloop()

def test_timer_update():
    """测试定时器更新UI"""
    print("\n=== 定时器UI更新测试 ===")
    
    root = tk.Tk()
    root.title("定时器UI更新测试")
    root.geometry("300x200")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(
        main_frame,
        text="定时器UI更新测试",
        font=("Arial", 14, "bold")
    )
    title_label.pack(pady=(0, 20))
    
    counter_var = tk.StringVar(value="0")
    counter_label = ttk.Label(
        main_frame,
        textvariable=counter_var,
        font=("Arial", 24, "bold")
    )
    counter_label.pack(pady=20)
    
    status_var = tk.StringVar(value="准备开始")
    status_label = ttk.Label(
        main_frame,
        textvariable=status_var,
        font=("Arial", 12)
    )
    status_label.pack(pady=10)
    
    counter = [0]
    timer_running = [False]
    
    def update_counter():
        """定时更新计数器"""
        if timer_running[0]:
            counter[0] += 1
            counter_var.set(str(counter[0]))
            status_var.set(f"计数中... {counter[0]}")
            root.update_idletasks()
            
            if counter[0] < 10:
                root.after(500, update_counter)  # 500ms后再次更新
            else:
                status_var.set("计数完成!")
                timer_running[0] = False
    
    def start_timer():
        """开始定时器"""
        counter[0] = 0
        timer_running[0] = True
        status_var.set("开始计数...")
        root.after(100, update_counter)
    
    def stop_timer():
        """停止定时器"""
        timer_running[0] = False
        status_var.set("已停止")
    
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    start_button = ttk.Button(
        button_frame,
        text="开始",
        command=start_timer
    )
    start_button.pack(side=tk.LEFT, padx=(0, 10))
    
    stop_button = ttk.Button(
        button_frame,
        text="停止",
        command=stop_timer
    )
    stop_button.pack(side=tk.LEFT)
    
    print("定时器测试窗口已创建")
    print("点击'开始'按钮查看定时器更新效果")
    
    root.mainloop()

if __name__ == "__main__":
    print("UI更新测试工具")
    print("=" * 50)
    
    choice = input("选择测试类型:\n1. 简单UI更新测试\n2. 定时器UI更新测试\n请输入选择 (1 或 2): ")
    
    if choice == "1":
        test_simple_ui_update()
    elif choice == "2":
        test_timer_update()
    else:
        print("无效选择，运行简单UI更新测试")
        test_simple_ui_update()
