// 产品数据
const products = [
    // 接待服务机器人系列
    {
        id: 1,
        name: '豹小秘2',
        description: '新一代AI语音交互机器人',
        category: '接待服务机器人',
        image: 'images/baoxiaomi2.png',
        configUrl: 'config-baoxiaomi2.html'
    },
    {
        id: 2,
        name: '豹小秘mini',
        description: '小场景用更有性价比｜让远程沟通身临其境',
        category: '接待服务机器人',
        image: 'images/baoxiaomi-mini.png',
        configUrl: 'config-baoxiaomi-mini.html'
    },
    {
        id: 3,
        name: '豹小秘Plus',
        description: '大屏接待机器人',
        category: '接待服务机器人',
        image: 'images/baoxiaomi-plus.png',
        configUrl: 'config-baoxiaomi-plus.html'
    },
    // 递送服务机器人系列
    {
        id: 4,
        name: '招财豹',
        description: '忙时送餐｜闲时揽客｜一个顶俩',
        category: '递送服务机器人',
        image: 'images/zhaocaibao.png',
        configUrl: 'config-zhaocaibao.html'
    },
    {
        id: 5,
        name: '招财豹Pro',
        description: '新一代营销递送机器人',
        category: '递送服务机器人',
        image: 'images/zhaocaibao-pro.png',
        configUrl: 'config-zhaocaibao-pro.html'
    },
    {
        id: 6,
        name: '豹小递Slim',
        description: '智能递送机器人',
        category: '递送服务机器人',
        image: 'images/baoxiaodi-slim.png',
        configUrl: 'config-baoxiaodi-slim.html'
    },
    {
        id: 7,
        name: '豹小递Pro',
        description: '智能递送机器人',
        category: '递送服务机器人',
        image: 'images/baoxiaodi-pro.png',
        configUrl: 'config-baoxiaodi-pro.html'
    },
    // 工业配送机器人系列
    {
        id: 8,
        name: '豹厂通',
        description: 'AI配货员',
        category: '工业配送机器人',
        image: 'images/baochangtong.png',
        configUrl: 'config-baochangtong.html'
    }
];

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    const productsGrid = document.querySelector('.products-grid');
    
    // 清空示例产品卡片
    productsGrid.innerHTML = '';
    
    // 按类别分组显示产品
    const categories = ['接待服务机器人', '递送服务机器人', '工业配送机器人'];
    
    categories.forEach(category => {
        // 添加类别标题
        const categoryTitle = document.createElement('h2');
        categoryTitle.className = 'category-title';
        categoryTitle.textContent = category;
        productsGrid.appendChild(categoryTitle);
        
        // 创建该类别产品的容器
        const categoryContainer = document.createElement('div');
        categoryContainer.className = 'category-container';
        
        // 添加该类别的所有产品
        products
            .filter(product => product.category === category)
            .forEach(product => {
                const productCard = createProductCard(product);
                categoryContainer.appendChild(productCard);
            });
        
        productsGrid.appendChild(categoryContainer);
    });
});

// 创建产品卡片
function createProductCard(product) {
    const card = document.createElement('div');
    card.className = 'product-card';
    
    card.innerHTML = `
        <div class="product-image">
            <img src="${product.image}" alt="${product.name}">
        </div>
        <div class="product-info">
            <h3>${product.name}</h3>
            <p>${product.description}</p>
            <a href="${product.configUrl}" class="btn-select">开始选配</a>
        </div>
    `;
    
    return card;
}

// 添加页面滚动动画
window.addEventListener('scroll', () => {
    const cards = document.querySelectorAll('.product-card');
    cards.forEach(card => {
        const cardTop = card.getBoundingClientRect().top;
        const windowHeight = window.innerHeight;
        
        if (cardTop < windowHeight * 0.8) {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }
    });
}); 