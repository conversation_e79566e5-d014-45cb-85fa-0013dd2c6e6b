[2025-07-17 11:48:07] 开始测试 - SN: 444444444444
[2025-07-17 11:48:07] 
开始执行: 设备连接状态检测
[2025-07-17 11:48:07] 设备连接测试通过，检测到 1 个设备
[2025-07-17 11:48:07] 设备: ?	device
[2025-07-17 11:48:07] 
开始执行: 3568版本测试
[2025-07-17 11:48:07] 执行3568版本测试...
[2025-07-17 11:48:07] 读取ROM版本号...
[2025-07-17 11:48:07] 执行命令: adb shell uname -a
[2025-07-17 11:48:07] uname命令执行成功
[2025-07-17 11:48:07] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-17 11:48:07] ✅ 3568版本测试成功
[2025-07-17 11:48:07] ROM版本号: Jul 9 12:01:12
[2025-07-17 11:48:07] ✅ 确认为RK3568平台
[2025-07-17 11:48:08] 
开始执行: USB关键器件检测
[2025-07-17 11:48:08] 执行USB设备检测...
[2025-07-17 11:48:08] 执行命令: adb shell lsusb
[2025-07-17 11:48:08] 设备返回数据:
[2025-07-17 11:48:08] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-17 11:48:08] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-17 11:48:08] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-17 11:48:08] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-17 11:48:08] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-17 11:48:08] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-17 11:48:08] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-17 11:48:08] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-17 11:48:08] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-17 11:48:08] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-17 11:48:08] 总共解析到 9 个设备
[2025-07-17 11:48:08] ✅ 所有预期的设备ID都已找到
[2025-07-17 11:48:08] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-17 11:48:08] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-17 11:48:08] ✅ USB设备检测通过
[2025-07-17 11:48:09] 
开始执行: CAN0测试
[2025-07-17 11:48:09] 执行CAN0测试流程...
[2025-07-17 11:48:09] 执行命令: adb shell ip link set can0 down
[2025-07-17 11:48:09] CAN0已关闭
[2025-07-17 11:48:09] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-17 11:48:09] CAN0已启动
[2025-07-17 11:48:09] CAN监听线程已启动...
[2025-07-17 11:48:10] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-17 11:48:10] CAN测试数据已发送，等待监听返回...
[2025-07-17 11:48:10] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-17 11:48:12] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-17 11:48:12] 
开始执行: GPS测试
[2025-07-17 11:48:12] 执行GPS测试...
[2025-07-17 11:48:12] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-17 11:48:22] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-17 11:48:23] 
开始执行: 4G模组测试
[2025-07-17 11:48:23] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-17 11:48:23] 监听线程已启动，等待串口数据...
[2025-07-17 11:48:25] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-17 11:48:25] AT+CCID指令已发送，等待串口返回...
[2025-07-17 11:48:25] 串口输出: AT+CCID
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 13
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: AT+C
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 58
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: AT+C
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 58
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: AT+C
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 58
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: AT+C
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 58
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: AT+C
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 58
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: AT+C
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 58
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: AT+C
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 58
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: AT+C
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:25] 串口输出: +CME ERROR: 58
[2025-07-17 11:48:25] 串口输出: 
[2025-07-17 11:48:37] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-17 11:48:38] 
开始执行: 按键测试
[2025-07-17 11:48:38] 开始按键测试...
[2025-07-17 11:48:38] 执行命令: adb shell evtest /dev/input/event5
[2025-07-17 11:48:41] 原始事件: Event: time 1752724120.653380, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-17 11:48:41] 解析结果: key_code=656, value=1
[2025-07-17 11:48:41] ✓ 检测到锁定键(656)按下
[2025-07-17 11:48:41] 原始事件: Event: time 1752724120.800387, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-17 11:48:41] 解析结果: key_code=656, value=0
[2025-07-17 11:48:41] ✓ 锁定键(656)测试通过
[2025-07-17 11:48:41] 原始事件: Event: time 1752724121.076729, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-17 11:48:41] 解析结果: key_code=657, value=1
[2025-07-17 11:48:41] ✓ 检测到SOS键(657)按下
[2025-07-17 11:48:41] 原始事件: Event: time 1752724121.250072, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-17 11:48:41] 解析结果: key_code=657, value=0
[2025-07-17 11:48:41] ✓ SOS键(657)测试通过
[2025-07-17 11:48:41] 原始事件: Event: time 1752724122.053398, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-17 11:48:41] 解析结果: key_code=660, value=1
[2025-07-17 11:48:42] ✓ 检测到智驾键(660)按下
[2025-07-17 11:48:42] 原始事件: Event: time 1752724122.226750, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-17 11:48:42] 解析结果: key_code=660, value=0
[2025-07-17 11:48:42] ✓ 智驾键(660)测试通过
[2025-07-17 11:48:42] 原始事件: Event: time 1752724122.400098, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-17 11:48:42] 解析结果: key_code=663, value=1
[2025-07-17 11:48:42] ✓ 检测到语音键(663)按下
[2025-07-17 11:48:42] 原始事件: Event: time 1752724122.553412, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-17 11:48:42] 解析结果: key_code=663, value=0
[2025-07-17 11:48:42] ✓ 语音键(663)测试通过
[2025-07-17 11:48:42] 原始事件: Event: time 1752724122.750054, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-17 11:48:42] 解析结果: key_code=658, value=1
[2025-07-17 11:48:42] ✓ 检测到喇叭键(658)按下
[2025-07-17 11:48:42] 原始事件: Event: time 1752724122.900076, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-17 11:48:42] 解析结果: key_code=658, value=0
[2025-07-17 11:48:42] ✓ 喇叭键(658)测试通过
[2025-07-17 11:48:43] 原始事件: Event: time 1752724123.300134, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-17 11:48:43] 解析结果: key_code=659, value=1
[2025-07-17 11:48:43] ✓ 检测到档位加(659)按下
[2025-07-17 11:48:43] 原始事件: Event: time 1752724123.450051, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-17 11:48:43] 解析结果: key_code=659, value=0
[2025-07-17 11:48:43] ✓ 档位加(659)测试通过
[2025-07-17 11:48:43] 原始事件: Event: time 1752724123.750089, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-17 11:48:43] 解析结果: key_code=661, value=1
[2025-07-17 11:48:43] ✓ 检测到静音键(661)按下
[2025-07-17 11:48:43] 原始事件: Event: time 1752724123.926747, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-17 11:48:43] 解析结果: key_code=661, value=0
[2025-07-17 11:48:43] ✓ 静音键(661)测试通过
[2025-07-17 11:48:44] 原始事件: Event: time 1752724124.176742, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-17 11:48:44] 解析结果: key_code=662, value=1
[2025-07-17 11:48:44] ✓ 检测到档位减(662)按下
[2025-07-17 11:48:44] 原始事件: Event: time 1752724124.376760, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-17 11:48:44] 解析结果: key_code=662, value=0
[2025-07-17 11:48:44] ✓ 档位减(662)测试通过
[2025-07-17 11:48:44] 按键测试完成 - 检测到8个按键
[2025-07-17 11:48:46] 
开始执行: 按键灯测试
[2025-07-17 11:48:46] 开始LED背光灯测试...
[2025-07-17 11:48:46] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-17 11:48:46] LED控制命令执行成功
[2025-07-17 11:48:46] 🔧 显示确认对话框: LED测试确认
[2025-07-17 11:48:47] 🔧 对话框窗口已创建
[2025-07-17 11:48:47] 🔧 '是'按钮已创建
[2025-07-17 11:48:47] 🔧 '否'按钮已创建
[2025-07-17 11:48:47] 🔧 对话框显示完成，等待用户响应...
[2025-07-17 11:48:48] 👤 用户选择: 是 (测试通过)
[2025-07-17 11:48:48] 🔧 对话框关闭，用户响应: yes
[2025-07-17 11:48:48] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-17 11:48:48] LED灯已关闭
[2025-07-17 11:48:48] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-17 11:48:49] 
开始执行: 手电筒测试
[2025-07-17 11:48:49] 开始手电筒LED测试...
[2025-07-17 11:48:49] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-17 11:48:49] 手电筒控制命令执行成功
[2025-07-17 11:48:49] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-17 11:48:49] 🔧 对话框窗口已创建
[2025-07-17 11:48:49] 🔧 '是'按钮已创建
[2025-07-17 11:48:49] 🔧 '否'按钮已创建
[2025-07-17 11:48:49] 🔧 对话框显示完成，等待用户响应...
[2025-07-17 11:48:50] 👤 用户选择: 是 (测试通过)
[2025-07-17 11:48:50] 🔧 对话框关闭，用户响应: yes
[2025-07-17 11:48:50] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-17 11:48:50] 手电筒已关闭
[2025-07-17 11:48:50] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-17 11:48:50] 
开始执行: 摇杆使能测试
[2025-07-17 11:48:50] 执行摇杆测试...
[2025-07-17 11:48:50] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-17 11:48:50] 命令执行成功
[2025-07-17 11:48:50] 返回数据: 255
[2025-07-17 11:48:50] 摇杆测试通过，值: 255
[2025-07-17 11:48:51] 
开始执行: 前摄像头测试
[2025-07-17 11:48:51] 开始执行前摄像头测试...
[2025-07-17 11:48:51] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-17 11:48:51] 命令执行成功，返回: 无输出
[2025-07-17 11:48:51] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-17 11:48:52] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.337974537
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-17 11:48:52] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-17 11:48:52] 拉取命令执行成功
[2025-07-17 11:48:52] 返回数据: [ 35%] /data/camera/cam0_3840x2160.jpg
[ 70%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 14.3 MB/s (187174 bytes in 0.013s)
[2025-07-17 11:48:52] 图片已保存: cam0_3840x2160.jpg
[2025-07-17 11:48:54] 用户确认结果: 通过
[2025-07-17 11:48:54] 
开始执行: 光感测试
[2025-07-17 11:48:54] 执行光感测试...
[2025-07-17 11:48:55] 执行命令: adb shell evtest /dev/input/event1
[2025-07-17 11:49:02] 光感测试完成 - 检测到数值变化
[2025-07-17 11:49:02] 数值从 0 变化到 2
[2025-07-17 11:49:02] 
开始执行: 回充摄像头测试
[2025-07-17 11:49:02] 开始执行回充摄像头测试...
[2025-07-17 11:49:02] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-17 11:49:03] 命令执行成功，返回: 无输出
[2025-07-17 11:49:03] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-17 11:49:04] 命令执行成功，返回: 无输出
[2025-07-17 11:49:04] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-17 11:49:04] 拉取命令执行成功
[2025-07-17 11:49:04] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 2.7 MB/s (24720 bytes in 0.009s)
[2025-07-17 11:49:04] 图片已保存: output.jpg
[2025-07-17 11:49:05] 用户确认结果: 通过
[2025-07-17 11:49:05] 
开始执行: 喇叭测试
[2025-07-17 11:49:05] 执行喇叭测试...
[2025-07-17 11:49:05] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-17 11:49:19] 命令执行成功
[2025-07-17 11:49:19] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-17 11:49:19] 音频播放完成
[2025-07-17 11:49:19] 
开始执行: 蓝牙测试
[2025-07-17 11:49:19] 执行蓝牙测试...
[2025-07-17 11:49:19] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-17 11:49:19] 执行命令: adb shell bluetoothctl show
[2025-07-17 11:49:20] 命令执行成功
[2025-07-17 11:49:20] 返回数据:
[2025-07-17 11:49:20]   Controller 40:55:48:07:E4:2D (public)
[2025-07-17 11:49:20]   Name: CMG-1
[2025-07-17 11:49:20]   Alias: CMG-1
[2025-07-17 11:49:20]   Class: 0x006c0000 (7077888)
[2025-07-17 11:49:20]   Powered: yes
[2025-07-17 11:49:20]   PowerState: on
[2025-07-17 11:49:20]   Discoverable: no
[2025-07-17 11:49:20]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-17 11:49:20]   Pairable: yes
[2025-07-17 11:49:20]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-17 11:49:20]   Modalias: usb:v1D6Bp0246d0544
[2025-07-17 11:49:20]   Discovering: no
[2025-07-17 11:49:20]   Roles: central
[2025-07-17 11:49:20]   Roles: peripheral
[2025-07-17 11:49:20]   Advertising Features:
[2025-07-17 11:49:20]   ActiveInstances: 0x00 (0)
[2025-07-17 11:49:20]   SupportedInstances: 0x10 (16)
[2025-07-17 11:49:20]   SupportedIncludes: tx-power
[2025-07-17 11:49:20]   SupportedIncludes: appearance
[2025-07-17 11:49:20]   SupportedIncludes: local-name
[2025-07-17 11:49:20]   SupportedSecondaryChannels: 1M
[2025-07-17 11:49:20]   SupportedSecondaryChannels: 2M
[2025-07-17 11:49:20]   SupportedSecondaryChannels: Coded
[2025-07-17 11:49:20]   SupportedCapabilities Key: MaxAdvLen
[2025-07-17 11:49:20]   SupportedCapabilities Value: 0x1f (31)
[2025-07-17 11:49:20]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-17 11:49:20]   SupportedCapabilities Value: 0x1f (31)
[2025-07-17 11:49:20]   SupportedFeatures: CanSetTxPower
[2025-07-17 11:49:20]   SupportedFeatures: HardwareOffload
[2025-07-17 11:49:20]   Advertisement Monitor Features:
[2025-07-17 11:49:20]   SupportedMonitorTypes: or_patterns
[2025-07-17 11:49:20] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-17 11:49:20] 蓝牙控制器MAC地址: 40:55:48:07:E4:2D
[2025-07-17 11:49:20] 
开始执行: WiFi测试
[2025-07-17 11:49:20] 执行WiFi测试...
[2025-07-17 11:49:20] 第一步：连接WiFi网络...
[2025-07-17 11:49:20] 停止现有的wpa_supplicant进程...
[2025-07-17 11:49:20] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-17 11:49:20] 清理wpa_supplicant socket文件...
[2025-07-17 11:49:20] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-17 11:49:20] 关闭wlan0接口...
[2025-07-17 11:49:20] 执行命令: adb shell ip link set wlan0 down
[2025-07-17 11:49:20] 连接WiFi网络...
[2025-07-17 11:49:20] 执行WiFi连接命令...
[2025-07-17 11:49:20] SSID: Orion_SZ_5G
[2025-07-17 11:49:23] WiFi连接命令执行完成，返回数据:
[2025-07-17 11:49:23]   Successfully initialized wpa_supplicant
[2025-07-17 11:49:23]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-17 11:49:23]   1
[2025-07-17 11:49:23]   OK
[2025-07-17 11:49:23]   OK
[2025-07-17 11:49:23]   OK
[2025-07-17 11:49:23]   deleting routers
[2025-07-17 11:49:23]   adding dns ************
[2025-07-17 11:49:23]   adding dns ***********
[2025-07-17 11:49:23]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-17 11:49:23]   SSID: Orion_SZ_5G
[2025-07-17 11:49:23]   freq: 5300
[2025-07-17 11:49:23]   RX: 73409 bytes (866 packets)
[2025-07-17 11:49:23]   TX: 37555 bytes (470 packets)
[2025-07-17 11:49:23]   signal: -50 dBm
[2025-07-17 11:49:23]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-17 11:49:23]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-17 11:49:23]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-17 11:49:23]   link/ether 40:55:48:08:32:89 brd ff:ff:ff:ff:ff:ff
[2025-07-17 11:49:23]   inet 192.168.20.172/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-17 11:49:23]   valid_lft forever preferred_lft forever
[2025-07-17 11:49:23]   inet6 fe80::8f36:8270:ea2c:38fe/64 scope link tentative
[2025-07-17 11:49:23]   valid_lft forever preferred_lft forever
[2025-07-17 11:49:23] ✅ WiFi连接成功
[2025-07-17 11:49:23] 等待网络稳定...
[2025-07-17 11:49:26] 第二步：开始网络发包延时测试...
[2025-07-17 11:49:26] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-17 11:49:26] 正在进行10秒钟的网络延时测试...
[2025-07-17 11:49:36] ping命令执行成功
[2025-07-17 11:49:36] 返回数据:
[2025-07-17 11:49:36]   PING www.a.shifen.com (157.148.69.186) 56(84) bytes of data.
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=1 ttl=54 time=10.6 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=2 ttl=54 time=19.1 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=3 ttl=54 time=15.5 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=4 ttl=54 time=11.4 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=5 ttl=54 time=11.3 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=6 ttl=54 time=12.4 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=7 ttl=54 time=11.9 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=8 ttl=54 time=11.2 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=9 ttl=54 time=11.6 ms
[2025-07-17 11:49:36]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=10 ttl=54 time=11.5 ms
[2025-07-17 11:49:36]   --- www.a.shifen.com ping statistics ---
[2025-07-17 11:49:36]   10 packets transmitted, 10 received, 0% packet loss, time 9010ms
[2025-07-17 11:49:36]   rtt min/avg/max/mdev = 10.569/12.644/19.080/2.497 ms
[2025-07-17 11:49:36] 检测到延时: 10.6 ms
[2025-07-17 11:49:36] 检测到延时: 19.1 ms
[2025-07-17 11:49:36] 检测到延时: 15.5 ms
[2025-07-17 11:49:36] 检测到延时: 11.4 ms
[2025-07-17 11:49:36] 检测到延时: 11.3 ms
[2025-07-17 11:49:36] 检测到延时: 12.4 ms
[2025-07-17 11:49:36] 检测到延时: 11.9 ms
[2025-07-17 11:49:36] 检测到延时: 11.2 ms
[2025-07-17 11:49:36] 检测到延时: 11.6 ms
[2025-07-17 11:49:36] 检测到延时: 11.5 ms
[2025-07-17 11:49:36] ✅ WiFi延时测试成功
[2025-07-17 11:49:36] 发包数量: 10 个
[2025-07-17 11:49:36] 平均延时: 12.65 ms
[2025-07-17 11:49:36] 最小延时: 10.60 ms
[2025-07-17 11:49:36] 最大延时: 19.10 ms
[2025-07-17 11:49:36] 
开始执行: Accel
[2025-07-17 11:49:36] 执行Accel传感器FPS测试...
[2025-07-17 11:49:36] 读取Accel传感器FPS数据...
[2025-07-17 11:49:36] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-17 11:49:36] 传感器日志读取成功
[2025-07-17 11:49:36] 开始解析Accel传感器FPS数据...
[2025-07-17 11:49:36] 找到 3962 个Accel传感器FPS数据点
[2025-07-17 11:49:36] 使用最近 5 条数据计算平均值: [188, 200, 196, 188, 186]
[2025-07-17 11:49:36] Accel传感器平均FPS: 191.6
[2025-07-17 11:49:36] ✅ Accel传感器测试成功
[2025-07-17 11:49:36] Accel平均FPS: 191.6
[2025-07-17 11:49:36] 
开始执行: Gyro
[2025-07-17 11:49:36] 执行Gyro传感器FPS测试...
[2025-07-17 11:49:36] 读取Gyro传感器FPS数据...
[2025-07-17 11:49:36] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-17 11:49:37] 传感器日志读取成功
[2025-07-17 11:49:37] 开始解析Gyro传感器FPS数据...
[2025-07-17 11:49:37] 找到 3963 个Gyro传感器FPS数据点
[2025-07-17 11:49:37] 使用最近 5 条数据计算平均值: [200, 196, 188, 186, 196]
[2025-07-17 11:49:37] Gyro传感器平均FPS: 193.2
[2025-07-17 11:49:37] ✅ Gyro传感器测试成功
[2025-07-17 11:49:37] Gyro平均FPS: 193.2
[2025-07-17 11:49:37] 
开始执行: Laser
[2025-07-17 11:49:37] 执行Laser传感器FPS测试...
[2025-07-17 11:49:37] 读取Laser传感器FPS数据...
[2025-07-17 11:49:37] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-17 11:49:37] 传感器日志读取成功
[2025-07-17 11:49:37] 开始解析Laser传感器FPS数据...
[2025-07-17 11:49:37] 找到 3963 个Laser传感器FPS数据点
[2025-07-17 11:49:37] 使用最近 5 条数据计算平均值: [4033, 3991, 4066, 3993, 3949]
[2025-07-17 11:49:37] Laser传感器平均FPS: 4006.4
[2025-07-17 11:49:37] ✅ Laser传感器测试成功
[2025-07-17 11:49:37] Laser平均FPS: 4006.4
[2025-07-17 11:49:38] 
开始执行: Laser2
[2025-07-17 11:49:38] 执行Laser2传感器FPS测试...
[2025-07-17 11:49:38] 读取Laser2传感器FPS数据...
[2025-07-17 11:49:38] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-17 11:49:38] 传感器日志读取成功
[2025-07-17 11:49:38] 开始解析Laser2传感器FPS数据...
[2025-07-17 11:49:38] 找到 3964 个Laser2传感器FPS数据点
[2025-07-17 11:49:38] 使用最近 5 条数据计算平均值: [3995, 4068, 3959, 3961, 3961]
[2025-07-17 11:49:38] Laser2传感器平均FPS: 3988.8
[2025-07-17 11:49:38] ✅ Laser2传感器测试成功
[2025-07-17 11:49:38] Laser2平均FPS: 3988.8
[2025-07-17 11:49:39] 
开始执行: Odom
[2025-07-17 11:49:39] 执行Odom传感器FPS测试...
[2025-07-17 11:49:39] 读取Odom传感器FPS数据...
[2025-07-17 11:49:39] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-17 11:49:39] 传感器日志读取成功
[2025-07-17 11:49:39] 开始解析Odom传感器FPS数据...
[2025-07-17 11:49:39] 找到 3965 个Odom传感器FPS数据点
[2025-07-17 11:49:39] 使用最近 5 条数据计算平均值: [102, 100, 100, 100, 100]
[2025-07-17 11:49:39] Odom传感器平均FPS: 100.4
[2025-07-17 11:49:39] ✅ Odom传感器测试成功
[2025-07-17 11:49:39] Odom平均FPS: 100.4
[2025-07-17 11:49:39] 
测试完成 - 通过率: 19/21
[2025-07-17 11:49:39] ❌ 存在测试失败项！
[2025-07-17 11:49:39] 测试记录已保存: records/444444444444_20250717_114939.json

