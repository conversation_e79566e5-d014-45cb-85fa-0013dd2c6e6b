#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试evtest命令输出
"""

import subprocess
import time
import threading
import queue

def test_adb_connection():
    """测试ADB连接"""
    print("=== 测试ADB连接 ===")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout}")
        if result.stderr:
            print(f"错误: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"ADB连接测试失败: {e}")
        return False

def test_evtest_devices():
    """测试evtest设备列表"""
    print("\n=== 测试evtest设备列表 ===")
    try:
        print("执行: adb shell evtest")
        process = subprocess.Popen(
            ['adb', 'shell', 'evtest'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待设备列表输出
        print("等待设备列表...")
        time.sleep(3)
        
        # 读取初始输出
        output_lines = []
        try:
            while True:
                line = process.stdout.readline()
                if not line:
                    break
                line = line.strip()
                if line:
                    output_lines.append(line)
                    print(f"输出: {line}")
                
                # 如果看到设备选择提示，停止读取
                if "Select the device event number" in line:
                    break
                    
                # 防止无限循环
                if len(output_lines) > 20:
                    break
        except Exception as e:
            print(f"读取输出时出错: {e}")
        
        # 终止进程
        try:
            process.terminate()
            process.wait(timeout=2)
        except:
            process.kill()
        
        return output_lines
        
    except Exception as e:
        print(f"测试evtest设备列表失败: {e}")
        return []

def test_evtest_with_device_selection():
    """测试选择设备5后的evtest输出"""
    print("\n=== 测试选择设备5后的evtest输出 ===")
    try:
        print("执行: adb shell evtest")
        process = subprocess.Popen(
            ['adb', 'shell', 'evtest'],
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待设备列表
        time.sleep(3)
        
        # 发送设备选择
        print("发送设备选择: 5")
        process.stdin.write("5\n")
        process.stdin.flush()
        
        # 等待进入监听模式
        time.sleep(2)
        
        print("现在请按一个按键进行测试...")
        print("等待10秒钟...")
        
        # 读取输出
        start_time = time.time()
        while time.time() - start_time < 10:
            if process.poll() is not None:
                print("进程已结束")
                break
            
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    if line:
                        print(f"事件: {line}")
                        
                        # 检查是否是按键事件
                        if "EV_KEY" in line:
                            print(f"  -> 检测到按键事件!")
                            
                        # 检查错误信息
                        if "error" in line.lower() or "failed" in line.lower():
                            print(f"  -> 可能的错误: {line}")
                            
            except Exception as e:
                print(f"读取输出时出错: {e}")
                break
        
        # 终止进程
        try:
            process.terminate()
            process.wait(timeout=2)
        except:
            process.kill()
        
        print("测试完成")
        
    except Exception as e:
        print(f"测试选择设备后的evtest输出失败: {e}")

def test_direct_evtest():
    """直接测试evtest /dev/input/event5"""
    print("\n=== 直接测试evtest /dev/input/event5 ===")
    try:
        print("执行: adb shell evtest /dev/input/event5")
        process = subprocess.Popen(
            ['adb', 'shell', 'evtest', '/dev/input/event5'],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("等待5秒，请按按键...")
        
        start_time = time.time()
        while time.time() - start_time < 5:
            if process.poll() is not None:
                print("进程已结束")
                break
            
            try:
                line = process.stdout.readline()
                if line:
                    line = line.strip()
                    if line:
                        print(f"直接事件: {line}")
            except:
                break
        
        # 检查stderr
        try:
            stderr_output = process.stderr.read()
            if stderr_output:
                print(f"错误输出: {stderr_output}")
        except:
            pass
        
        # 终止进程
        try:
            process.terminate()
            process.wait(timeout=2)
        except:
            process.kill()
        
    except Exception as e:
        print(f"直接测试evtest失败: {e}")

def test_input_devices():
    """检查输入设备"""
    print("\n=== 检查输入设备 ===")
    try:
        result = subprocess.run(['adb', 'shell', 'ls', '-l', '/dev/input/'], 
                              capture_output=True, text=True, timeout=10)
        print("输入设备列表:")
        print(result.stdout)
        
        if result.stderr:
            print(f"错误: {result.stderr}")
            
    except Exception as e:
        print(f"检查输入设备失败: {e}")

if __name__ == "__main__":
    print("evtest调试工具")
    print("=" * 50)
    
    # 测试ADB连接
    if not test_adb_connection():
        print("ADB连接失败，无法继续测试")
        exit(1)
    
    # 检查输入设备
    test_input_devices()
    
    # 测试evtest设备列表
    device_list = test_evtest_devices()
    
    # 测试选择设备后的输出
    test_evtest_with_device_selection()
    
    # 直接测试event5设备
    test_direct_evtest()
    
    print("\n调试完成")
