#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工序选择和测试项目加载修复
验证：
1. 工序选择在程序启动时进行（不是在输入序列号后）
2. 选择工序后正确加载测试项目
3. 界面信息正确更新
4. 单项测试功能正常工作
"""

import subprocess
import time
import sys
import os

def test_process_selection_fix():
    """测试工序选择和测试项目加载修复"""
    print("=== 测试工序选择和测试项目加载修复 ===")
    
    # 检查文件是否存在
    if not os.path.exists('main.py'):
        print("❌ main.py 文件不存在")
        return False
    
    if not os.path.exists('config.json'):
        print("❌ config.json 文件不存在")
        return False
    
    print("✅ 文件检查通过")
    
    # 提供测试指导
    print("\n📋 修复内容:")
    print("1. 工序选择时机修复：")
    print("   - 修复前：程序启动 → 输入序列号 → 选择工序")
    print("   - 修复后：程序启动 → 选择工序 → 输入序列号")
    
    print("\n2. 测试项目加载修复：")
    print("   - 修复前：选择工序后测试项目列表为空")
    print("   - 修复后：选择工序后正确显示测试项目")
    
    print("\n3. 界面信息更新修复：")
    print("   - 修复前：工序信息不更新")
    print("   - 修复后：工序和序列号信息实时更新")
    
    print("\n📋 测试步骤:")
    print("步骤1: 程序启动验证")
    print("  1. 程序启动后立即弹出工序选择对话框")
    print("  2. 选择一个工序（如'整机半成品功能测试'）")
    print("  3. 观察主界面是否显示正确的工序信息")
    print("  4. 观察测试项目列表是否有内容")
    
    print("\n步骤2: 单项测试验证")
    print("  1. 双击测试项目列表中的任意项目")
    print("  2. 观察是否有调试信息输出")
    print("  3. 观察是否正常执行单项测试")
    
    print("\n步骤3: 工序切换验证")
    print("  1. 通过菜单'配置'->'选择工序'重新选择")
    print("  2. 选择不同的工序")
    print("  3. 观察工序信息是否更新")
    print("  4. 观察测试项目列表是否更新")
    
    print("\n步骤4: 序列号输入验证")
    print("  1. 点击'开始测试'按钮")
    print("  2. 输入序列号")
    print("  3. 观察序列号信息是否更新")
    print("  4. 观察是否开始测试流程")
    
    print("\n🔍 验证要点:")
    print("  ✅ 程序启动时先选择工序，再输入序列号")
    print("  ✅ 选择工序后测试项目列表有内容")
    print("  ✅ 工序信息显示正确（蓝色文字）")
    print("  ✅ 序列号信息显示正确（绿色或灰色）")
    print("  ✅ 双击测试项目能触发单项测试")
    print("  ✅ 右键菜单正常显示")
    print("  ✅ 日志输出调试信息")
    
    print("\n⏰ 3秒后启动主程序...")
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    try:
        print("🚀 启动主程序...")
        
        # 启动主程序
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("✅ 主程序已启动")
        print("📝 请按照上述步骤进行测试")
        print("🔍 特别注意工序选择的时机和测试项目的加载")
        print("⚠️ 如果工序选择在序列号输入后，说明修复不完整")
        
        # 等待程序结束
        stdout, stderr = process.communicate()
        
        print(f"\n📊 程序退出，返回码: {process.returncode}")
        
        if stdout:
            print("📤 标准输出:")
            print(stdout)
        
        if stderr:
            print("❌ 错误输出:")
            print(stderr)
        
        return process.returncode == 0
        
    except Exception as e:
        print(f"❌ 启动主程序失败: {e}")
        return False

def provide_expected_behavior():
    """提供预期行为说明"""
    print("\n📋 预期行为说明:")
    
    print("\n1. 程序启动流程:")
    print("   程序启动 → 工序选择对话框 → 选择工序 → 主界面显示")
    print("   ↓")
    print("   点击开始测试 → 序列号输入对话框 → 输入序列号 → 开始测试")
    
    print("\n2. 界面状态显示:")
    print("   - 工序信息: '整机半成品功能测试 - 整机半成品功能测试 (17个测试项目)' (蓝色)")
    print("   - 序列号信息: '未输入' (灰色) 或 'CMG20241201' (绿色)")
    print("   - 测试项目列表: 显示对应工序的所有测试项目")
    
    print("\n3. 单项测试功能:")
    print("   - 双击测试项目: 触发单项测试，显示调试信息")
    print("   - 右键菜单: 显示'运行测试'和'查看详情'选项")
    print("   - 日志输出: 显示详细的测试过程信息")
    
    print("\n4. 工序切换功能:")
    print("   - 菜单操作: 配置 → 选择工序")
    print("   - 选择新工序: 界面信息和测试项目列表自动更新")
    print("   - 状态保持: 序列号信息保持不变")

def provide_troubleshooting():
    """提供故障排除指导"""
    print("\n🔧 故障排除指导:")
    
    print("\n1. 如果工序选择在序列号输入后:")
    print("   - 检查程序启动流程是否正确")
    print("   - 确认修复是否完全应用")
    print("   - 重新启动程序验证")
    
    print("\n2. 如果选择工序后测试项目列表为空:")
    print("   - 检查工序配置是否正确")
    print("   - 确认 init_test_projects() 是否被调用")
    print("   - 查看日志是否有错误信息")
    
    print("\n3. 如果界面信息不更新:")
    print("   - 检查 update_info_display() 是否被调用")
    print("   - 确认标签引用是否正确")
    print("   - 尝试重新选择工序")
    
    print("\n4. 如果单项测试不工作:")
    print("   - 确保已选择工序且测试项目列表有内容")
    print("   - 检查事件绑定是否正确")
    print("   - 查看日志输出的调试信息")
    
    print("\n5. 常见问题解决:")
    print("   - 重启程序: 解决大部分状态问题")
    print("   - 重新选择工序: 刷新测试项目列表")
    print("   - 检查配置文件: 确保工序和测试项目配置正确")

def check_key_fixes():
    """检查关键修复点"""
    print("\n🔧 关键修复点检查:")
    
    print("\n1. 初始化顺序修复:")
    print("   - ✅ 先设置UI界面")
    print("   - ✅ 再选择工序")
    print("   - ✅ 最后初始化测试项目")
    
    print("\n2. 界面更新机制:")
    print("   - ✅ 工序信息标签动态更新")
    print("   - ✅ 序列号信息标签动态更新")
    print("   - ✅ 测试项目列表动态加载")
    
    print("\n3. 状态管理改进:")
    print("   - ✅ 工序选择取消不退出程序")
    print("   - ✅ 提供菜单重新选择工序")
    print("   - ✅ 界面状态实时反映")
    
    print("\n4. 用户体验提升:")
    print("   - ✅ 清晰的状态提示信息")
    print("   - ✅ 完善的错误处理")
    print("   - ✅ 直观的界面反馈")

if __name__ == "__main__":
    print("工序选择和测试项目加载修复验证")
    print("=" * 50)
    
    # 检查关键修复点
    check_key_fixes()
    
    # 提供预期行为说明
    provide_expected_behavior()
    
    # 运行测试
    success = test_process_selection_fix()
    
    # 提供故障排除指导
    provide_troubleshooting()
    
    if success:
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")
    
    print("\n📝 测试总结:")
    print("- 工序选择在程序启动时进行，不是在输入序列号后")
    print("- 选择工序后正确加载和显示测试项目")
    print("- 界面信息实时更新，状态显示清晰")
    print("- 单项测试功能正常工作")
    print("- 提供了完善的工序切换功能")
