[2025-07-18 12:23:52] === 开始新的测试 ===
[2025-07-18 12:23:52] 开始测试
[2025-07-18 12:23:52] 工序: wifi
[2025-07-18 12:23:52] 序列号: 111111111
[2025-07-18 12:23:52] 开始运行 wifi 工序测试，共 1 个项目
[2025-07-18 12:23:52] 
开始执行: WiFi测试
[2025-07-18 12:23:52] 执行WiFi测试...
[2025-07-18 12:23:52] 第一步：关闭4G网络...
[2025-07-18 12:23:52] 关闭usb0接口（4G网络）...
[2025-07-18 12:23:52] 执行命令: adb shell ifconfig usb0 down
[2025-07-18 12:23:53] ✅ 4G网络已关闭
[2025-07-18 12:23:55] 第二步：连接WiFi网络...
[2025-07-18 12:23:55] 停止现有的wpa_supplicant进程...
[2025-07-18 12:23:55] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 12:23:55] 清理wpa_supplicant socket文件...
[2025-07-18 12:23:55] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 12:23:55] 关闭wlan0接口...
[2025-07-18 12:23:55] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 12:23:55] 连接WiFi网络...
[2025-07-18 12:23:55] 执行WiFi连接命令...
[2025-07-18 12:23:55] SSID: Orion_SZ_5G
[2025-07-18 12:23:58] WiFi连接命令执行完成，返回数据:
[2025-07-18 12:23:58]   Successfully initialized wpa_supplicant
[2025-07-18 12:23:58]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-18 12:23:58]   1
[2025-07-18 12:23:58]   OK
[2025-07-18 12:23:58]   OK
[2025-07-18 12:23:58]   OK
[2025-07-18 12:23:58]   deleting routers
[2025-07-18 12:23:58]   adding dns ************
[2025-07-18 12:23:58]   adding dns ***********
[2025-07-18 12:23:58]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-18 12:23:58]   SSID: Orion_SZ_5G
[2025-07-18 12:23:58]   freq: 5300
[2025-07-18 12:23:58]   RX: 12088 bytes (104 packets)
[2025-07-18 12:23:58]   TX: 8575 bytes (91 packets)
[2025-07-18 12:23:58]   signal: -50 dBm
[2025-07-18 12:23:58]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-18 12:23:58]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-18 12:23:58]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-18 12:23:58]   link/ether 24:21:5e:c0:3c:0b brd ff:ff:ff:ff:ff:ff
[2025-07-18 12:23:58]   inet 192.168.20.167/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-18 12:23:58]   valid_lft forever preferred_lft forever
[2025-07-18 12:23:58]   inet6 fe80::c5c9:135f:3de4:4a19/64 scope link tentative
[2025-07-18 12:23:58]   valid_lft forever preferred_lft forever
[2025-07-18 12:23:58] ✅ WiFi连接成功
[2025-07-18 12:23:58] 等待网络稳定...
[2025-07-18 12:24:01] 第二步：开始网络发包延时测试...
[2025-07-18 12:24:01] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-18 12:24:01] 正在进行10秒钟的网络延时测试...
[2025-07-18 12:24:11] ping命令执行成功
[2025-07-18 12:24:11] 返回数据:
[2025-07-18 12:24:11]   PING www.a.shifen.com (157.148.69.151) 56(84) bytes of data.
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=1 ttl=54 time=9.52 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=2 ttl=54 time=9.57 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=3 ttl=54 time=15.6 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=4 ttl=54 time=11.4 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=5 ttl=54 time=11.3 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=6 ttl=54 time=10.9 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=7 ttl=54 time=11.2 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=8 ttl=54 time=10.8 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=9 ttl=54 time=9.16 ms
[2025-07-18 12:24:11]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=10 ttl=54 time=10.2 ms
[2025-07-18 12:24:11]   --- www.a.shifen.com ping statistics ---
[2025-07-18 12:24:11]   10 packets transmitted, 10 received, 0% packet loss, time 9013ms
[2025-07-18 12:24:11]   rtt min/avg/max/mdev = 9.160/10.964/15.628/1.733 ms
[2025-07-18 12:24:11] 检测到延时: 9.52 ms
[2025-07-18 12:24:11] 检测到延时: 9.57 ms
[2025-07-18 12:24:11] 检测到延时: 15.6 ms
[2025-07-18 12:24:11] 检测到延时: 11.4 ms
[2025-07-18 12:24:11] 检测到延时: 11.3 ms
[2025-07-18 12:24:11] 检测到延时: 10.9 ms
[2025-07-18 12:24:11] 检测到延时: 11.2 ms
[2025-07-18 12:24:11] 检测到延时: 10.8 ms
[2025-07-18 12:24:11] 检测到延时: 9.16 ms
[2025-07-18 12:24:11] 检测到延时: 10.2 ms
[2025-07-18 12:24:11] ✅ WiFi延时测试成功
[2025-07-18 12:24:11] 发包数量: 10 个
[2025-07-18 12:24:11] 平均延时: 10.96 ms
[2025-07-18 12:24:11] 最小延时: 9.16 ms
[2025-07-18 12:24:11] 最大延时: 15.60 ms
[2025-07-18 12:24:11] 第三步：恢复4G网络...
[2025-07-18 12:24:11] 重新打开usb0接口（4G网络）...
[2025-07-18 12:24:11] 执行命令: adb shell ifconfig usb0 up
[2025-07-18 12:24:11] ✅ 4G网络已恢复
[2025-07-18 12:24:13] 
测试完成 - 通过率: 1/1
[2025-07-18 12:24:13] ✅ 所有测试通过！
[2025-07-18 12:24:13] 测试记录已保存: records/111111111_20250718_122413.json
[2025-07-18 12:24:13] 测试日志已保存: records/111111111_20250718_122413.log

