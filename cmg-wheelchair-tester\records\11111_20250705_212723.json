{"sn": "11111", "timestamp": "2025-07-05T21:27:23.195697", "results": {"device_connection": {"test_name": "设备连接状态", "status": "失败", "message": "未连接设备", "timestamp": "2025-07-05T21:27:14.515650"}, "usb_test": {"test_name": "USB关键器件测试", "status": "失败", "message": "未知的测试类型", "timestamp": "2025-07-05T21:27:15.585821"}, "can_test": {"test_name": "CAN0测试", "status": "失败", "message": "CAN0 down失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T21:27:16.688669"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "无GPS信号", "timestamp": "2025-07-05T21:27:17.896711"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "AT指令发送失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T21:27:21.068379"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成", "timestamp": "2025-07-05T21:27:22.159512"}, "led_test": {"test_name": "按键灯测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}, "torch_test": {"test_name": "手电筒测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}, "light_sensor_test": {"test_name": "光感测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}, "speaker_test": {"test_name": "喇叭测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}, "wifi_test": {"test_name": "WiFi测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:27:14.404697"}}}