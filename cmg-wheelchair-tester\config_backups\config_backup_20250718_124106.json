{"adb_settings": {"timeout_seconds": 30, "enable_detailed_logging": true, "auto_save_results": true}, "wifi_settings": {"ssid": "Orion_SZ_5G", "password": "Orion@2025"}, "work_processes": {"整机半成品功能测试": {"description": "测试组装半成品各硬件功能", "test_ids": ["device_connection", "rom_version_test", "usb_test", "can_test", "gps_test", "4g_test", "key_test", "led_test", "torch_test", "joystick_test", "front_camera_test", "light_sensor_test", "back_camera_test", "speaker_test", "bluetooth_test", "4g_network_test", "wifi_test", "accel_test", "gyro_test", "laser_test", "laser2_test", "odom_test"]}, "右手臂功能测试": {"description": "测试右手臂模块的硬件接口功能", "test_ids": ["device_connection", "rom_version_test", "usb_test", "can_test", "gps_test", "4g_test", "key_test", "led_test", "torch_test", "joystick_test", "front_camera_test", "light_sensor_test", "back_camera_test", "speaker_test", "bluetooth_test", "4g_network_test", "wifi_test"]}, "wifi": {"description": "", "test_ids": ["wifi_test"]}, "4G模组": {"description": "111", "test_ids": ["4g_test"]}}, "test_projects": [{"id": "device_connection", "name": "设备连接状态检测", "description": "检测设备ADB连接状态", "type": "connection_test", "command": "adb devices", "expected_pattern": "device"}, {"id": "rom_version_test", "name": "3568版本测试", "description": "读取ROM版本号", "type": "rom_version_test", "command": "uname -a", "expected_pattern": "rk3568"}, {"id": "usb_test", "name": "USB关键器件检测", "description": "检测系统USB设备数量和ID", "command": "lsusb", "type": "usb_test", "expected_devices": [{"bus": "005", "device": "001", "id": "1d6b:0001"}, {"bus": "003", "device": "001", "id": "1d6b:0002"}, {"bus": "001", "device": "001", "id": "1d6b:0002"}, {"bus": "006", "device": "001", "id": "1d6b:0001"}, {"bus": "001", "device": "002", "id": "0c45:1915"}, {"bus": "004", "device": "001", "id": "1d6b:0002"}, {"bus": "004", "device": "002", "id": "1a86:55ec"}, {"bus": "002", "device": "001", "id": "1d6b:0003"}, {"bus": "003", "device": "003", "id": "2c7c:0901"}]}, {"id": "can_test", "name": "CAN0测试", "description": "CAN总线通信测试", "type": "can_test", "commands": ["ip link set can0 down", "ip link set can0 up type can bitrate 500000 loopback on", "cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff"], "expected_response": "AA 00 CC AA 55 66 AA FF"}, {"id": "gps_test", "name": "GPS测试", "description": "GPS信号检测", "type": "gps_test", "commands": ["cat /dev/ttyUSB4 |grep GPGSV", "cat /dev/ttyUSB4 |grep GNGSV", "cat /dev/ttyUSB4 |grep GBGSV"], "expected_patterns": ["GPGSV", "GNGSV", "GBGSV"]}, {"id": "4g_test", "name": "4G模组版本测试", "description": "4G模组版本信息获取，使用ATI命令获取模组型号", "type": "4g_test", "commands": ["cat /dev/ttyUSB0", "echo -e \"ATI\" > /dev/ttyUSB0"], "expected_pattern": "Revision:"}, {"id": "key_test", "name": "按键测试", "description": "手柄功能按键测试(锁定键、SOS键、喇叭键、档位加、智驾键、静音键、档位减、语音键)", "type": "key_test", "command": "evtest", "device_number": "5", "expected_keys": [656, 657, 658, 659, 660, 661, 662, 663], "required_keys": 8, "manual_check": false}, {"id": "led_test", "name": "按键灯测试", "description": "按键LED灯测试", "type": "led_test", "commands": ["echo 255 > /sys/class/leds/lock_led/brightness", "echo 0 > /sys/class/leds/lock_led/brightness"], "manual_check": true}, {"id": "torch_test", "name": "手电筒测试", "description": "手电筒LED灯测试", "type": "torch_test", "commands": ["echo 255 > /sys/class/leds/torch/brightness", "echo 0 > /sys/class/leds/torch/brightness"], "manual_check": true}, {"id": "joystick_test", "name": "摇杆使能测试", "description": "摇杆功能使能测试", "type": "joystick_test", "command": "cat /sys/class/leds/joystick/brightness", "expected_value": "255"}, {"id": "front_camera_test", "name": "前摄像头测试", "description": "前摄像头拍照测试", "type": "camera_test", "commands": ["mkdir -p /data/camera/", "gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg"], "output_file": "cam0_3840x2160.jpg", "manual_check": true}, {"id": "light_sensor_test", "name": "光感测试", "description": "光线传感器测试", "type": "light_sensor_test", "command": "evtest /dev/input/event1", "expected_pattern": "ABS_MISC", "manual_check": true}, {"id": "back_camera_test", "name": "回充摄像头测试", "description": "回充摄像头拍照测试", "type": "camera_test", "commands": ["v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG", "v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1"], "output_file": "output.jpg", "manual_check": true}, {"id": "speaker_test", "name": "喇叭测试", "description": "音频播放测试", "type": "speaker_test", "command": "tinyplay /usr/data/test.wav", "manual_check": true}, {"id": "bluetooth_test", "name": "蓝牙测试", "description": "蓝牙控制器信息获取测试", "type": "bluetooth_test", "command": "bluetoothctl show", "expected_pattern": "Controller"}, {"id": "4g_network_test", "name": "4G网络测试", "description": "关闭WiFi后使用4G网络进行ping测试，计算延时平均值", "type": "4g_network_test", "test_duration": 10, "ping_target": "www.baidu.com", "expected_pattern": "64 bytes from"}, {"id": "wifi_test", "name": "WiFi测试", "description": "连接WiFi并进行网络发包延时测试", "type": "wifi_test", "wifi_ssid": "Orion_SZ_5G", "wifi_password": "Orion@2025", "test_duration": 10, "expected_pattern": "64 bytes from"}, {"id": "accel_test", "name": "Accel", "description": "加速度传感器FPS测试", "type": "sensor_fps_test", "sensor_name": "Accel", "command": "cat /sdcard/lmv/normal_logs/sensor/sensor_newest", "expected_pattern": "FPS Accel:"}, {"id": "gyro_test", "name": "Gyro", "description": "陀螺仪传感器FPS测试", "type": "sensor_fps_test", "sensor_name": "Gyro", "command": "cat /sdcard/lmv/normal_logs/sensor/sensor_newest", "expected_pattern": "FPS Gyro:"}, {"id": "laser_test", "name": "Laser", "description": "激光传感器FPS测试", "type": "sensor_fps_test", "sensor_name": "Laser", "command": "cat /sdcard/lmv/normal_logs/sensor/sensor_newest", "expected_pattern": "FPS Laser:"}, {"id": "laser2_test", "name": "Laser2", "description": "激光传感器2 FPS测试", "type": "sensor_fps_test", "sensor_name": "Laser2", "command": "cat /sdcard/lmv/normal_logs/sensor/sensor_newest", "expected_pattern": "FPS Laser2:"}, {"id": "odom_test", "name": "<PERSON><PERSON>", "description": "里程计传感器FPS测试", "type": "sensor_fps_test", "sensor_name": "<PERSON><PERSON>", "command": "cat /sdcard/lmv/normal_logs/sensor/sensor_newest", "expected_pattern": "FPS Odom:"}]}