[2025-07-18 10:24:46] === 开始新的测试 ===
[2025-07-18 10:24:46] 开始测试
[2025-07-18 10:24:46] 工序: 右手臂功能测试
[2025-07-18 10:24:46] 序列号: 11111111111111111111
[2025-07-18 10:24:46] 开始运行 右手臂功能测试 工序测试，共 16 个项目
[2025-07-18 10:24:46] 
开始执行: 设备连接状态检测
[2025-07-18 10:24:46] 设备连接测试失败：未检测到设备
[2025-07-18 10:24:47] 
开始执行: 3568版本测试
[2025-07-18 10:24:47] 执行3568版本测试...
[2025-07-18 10:24:47] 读取ROM版本号...
[2025-07-18 10:24:47] 执行命令: adb shell uname -a
[2025-07-18 10:24:47] ❌ uname命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:47] 
开始执行: USB关键器件检测
[2025-07-18 10:24:47] 执行USB设备检测...
[2025-07-18 10:24:47] 执行命令: adb shell lsusb
[2025-07-18 10:24:47] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:48] 
开始执行: CAN0测试
[2025-07-18 10:24:48] 执行CAN0测试流程...
[2025-07-18 10:24:48] 执行命令: adb shell ip link set can0 down
[2025-07-18 10:24:48] CAN0 down失败: error: no devices/emulators found

[2025-07-18 10:24:48] 
开始执行: GPS测试
[2025-07-18 10:24:48] 执行GPS测试...
[2025-07-18 10:24:48] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 10:24:48] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:48] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-18 10:24:48] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:48] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-18 10:24:48] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:48] GPS信号检测失败
[2025-07-18 10:24:49] 
开始执行: 4G模组测试
[2025-07-18 10:24:49] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 10:24:49] 监听线程已启动，等待串口数据...
[2025-07-18 10:24:51] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 10:24:51] AT指令发送失败: error: no devices/emulators found

[2025-07-18 10:24:52] 
开始执行: 按键测试
[2025-07-18 10:24:52] 开始按键测试...
[2025-07-18 10:24:52] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 10:24:54] 按键测试失败 - 只检测到0个按键
[2025-07-18 10:24:56] 
开始执行: 按键灯测试
[2025-07-18 10:24:56] 开始LED背光灯测试...
[2025-07-18 10:24:56] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 10:24:56] LED控制命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:57] 
开始执行: 手电筒测试
[2025-07-18 10:24:57] 开始手电筒LED测试...
[2025-07-18 10:24:57] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 10:24:57] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:58] 
开始执行: 摇杆使能测试
[2025-07-18 10:24:58] 执行摇杆测试...
[2025-07-18 10:24:58] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 10:24:58] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:58] 
开始执行: 前摄像头测试
[2025-07-18 10:24:58] 开始执行前摄像头测试...
[2025-07-18 10:24:58] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 10:24:58] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:24:59] 
开始执行: 光感测试
[2025-07-18 10:24:59] 执行光感测试...
[2025-07-18 10:24:59] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 10:24:59] 光感测试失败 - 未检测到数值变化
[2025-07-18 10:24:59] 
开始执行: 回充摄像头测试
[2025-07-18 10:24:59] 开始执行回充摄像头测试...
[2025-07-18 10:24:59] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 10:24:59] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:25:00] 
开始执行: 喇叭测试
[2025-07-18 10:25:00] 执行喇叭测试...
[2025-07-18 10:25:00] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 10:25:00] 命令执行失败: error: no devices/emulators found

[2025-07-18 10:25:00] 
开始执行: 蓝牙测试
[2025-07-18 10:25:00] 执行蓝牙测试...
[2025-07-18 10:25:00] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 10:25:00] 执行命令: adb shell bluetoothctl show
[2025-07-18 10:25:00] ❌ 命令执行失败: error: no devices/emulators found

[2025-07-18 10:25:00] 
开始执行: WiFi测试
[2025-07-18 10:25:00] 执行WiFi测试...
[2025-07-18 10:25:00] 第一步：连接WiFi网络...
[2025-07-18 10:25:00] 停止现有的wpa_supplicant进程...
[2025-07-18 10:25:00] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 10:25:00] 清理wpa_supplicant socket文件...
[2025-07-18 10:25:00] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 10:25:00] 关闭wlan0接口...
[2025-07-18 10:25:00] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 10:25:00] 连接WiFi网络...
[2025-07-18 10:25:00] 执行WiFi连接命令...
[2025-07-18 10:25:00] SSID: Orion_SZ_5G
[2025-07-18 10:25:00] ❌ WiFi连接失败: error: no devices/emulators found

[2025-07-18 10:25:01] 
测试完成 - 通过率: 0/16
[2025-07-18 10:25:01] ❌ 存在测试失败项！
[2025-07-18 10:25:01] 测试记录已保存: records/11111111111111111111_20250718_102501.json
[2025-07-18 10:25:01] 测试日志已保存: records/11111111111111111111_20250718_102501.log

