{"sn": "111111111111111111", "timestamp": "2025-07-18T10:55:05.753630", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-18T10:54:16.361316"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-18T10:54:16.867829"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-18T10:54:17.528122"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-18T10:54:18.064679"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-18T10:54:21.743355"}, "4g_test": {"test_name": "4G模组测试", "status": "通过", "message": "CCID: 89860114851012535241", "timestamp": "2025-07-18T10:54:32.399156"}, "key_test": {"test_name": "按键测试", "status": "失败", "message": "按键测试失败，只检测到7/8个按键", "timestamp": "2025-07-18T10:54:37.382545"}, "led_test": {"test_name": "按键灯测试", "status": "失败", "message": "LED控制失败", "timestamp": "2025-07-18T10:54:59.146266"}, "torch_test": {"test_name": "手电筒测试", "status": "失败", "message": "手电筒控制失败", "timestamp": "2025-07-18T10:54:59.866392"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-18T10:55:00.809412"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-18T10:55:01.519043"}, "light_sensor_test": {"test_name": "光感测试", "status": "失败", "message": "光感异常", "timestamp": "2025-07-18T10:55:02.437872"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-18T10:55:03.296510"}, "speaker_test": {"test_name": "喇叭测试", "status": "失败", "message": "播放失败", "timestamp": "2025-07-18T10:55:04.123846"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-18T10:55:04.643796"}, "wifi_test": {"test_name": "WiFi测试", "status": "失败", "message": "WiFi连接失败", "timestamp": "2025-07-18T10:55:05.117705"}}}