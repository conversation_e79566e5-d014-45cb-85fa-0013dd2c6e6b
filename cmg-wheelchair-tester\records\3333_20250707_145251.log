[2025-07-07 14:51:32] 开始测试 - SN: 3333
[2025-07-07 14:51:32] 
开始执行: 设备连接状态检测
[2025-07-07 14:51:38] 设备连接测试通过，检测到 1 个设备
[2025-07-07 14:51:38] 设备: ?	device
[2025-07-07 14:51:38] 
开始执行: USB关键器件检测
[2025-07-07 14:51:38] 未知的测试类型：device_match
[2025-07-07 14:51:38] 
开始执行: CAN0测试
[2025-07-07 14:51:38] 执行CAN0测试流程...
[2025-07-07 14:51:38] 执行命令: adb shell ip link set can0 down
[2025-07-07 14:51:39] CAN0已关闭
[2025-07-07 14:51:39] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-07 14:51:39] CAN0已启动
[2025-07-07 14:51:39] CAN监听线程已启动...
[2025-07-07 14:51:40] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-07 14:51:40] CAN测试数据已发送，等待监听返回...
[2025-07-07 14:51:40] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-07 14:51:42] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-07 14:51:42] 
开始执行: GPS测试
[2025-07-07 14:51:43] 执行GPS测试...
[2025-07-07 14:51:43] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-07 14:51:53] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-07 14:51:53] 
开始执行: 4G模组测试
[2025-07-07 14:51:53] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-07 14:51:53] 监听线程已启动，等待串口数据...
[2025-07-07 14:51:55] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-07 14:51:55] AT+CCID指令已发送，等待串口返回...
[2025-07-07 14:51:55] 串口输出: AT+CCID
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: +CME ERROR: 13
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: AT+C
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: +CME ERROR: 58
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: AT+C
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: +CME ERROR: 58
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: AT+C
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: +CME ERROR: 58
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: AT+C
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: +CME ERROR: 58
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: AT+C
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: +CME ERROR: 58
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: AT+C
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:51:55] 串口输出: +CME ERROR: 58
[2025-07-07 14:51:55] 串口输出: 
[2025-07-07 14:52:07] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-07 14:52:08] 
开始执行: 按键测试
[2025-07-07 14:52:08] 执行按键测试...
[2025-07-07 14:52:08] 请按手柄按键进行测试...
[2025-07-07 14:52:08] 
开始执行: 按键灯测试
[2025-07-07 14:52:08] 开始LED背光灯测试...
[2025-07-07 14:52:08] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-07 14:52:08] LED控制命令执行成功
[2025-07-07 14:52:11] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-07 14:52:11] LED灯已关闭
[2025-07-07 14:52:11] LED测试通过 - 背光灯正常点亮
[2025-07-07 14:52:11] 
开始执行: 手电筒测试
[2025-07-07 14:52:11] 开始手电筒LED测试...
[2025-07-07 14:52:11] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-07 14:52:11] 手电筒控制命令执行成功
[2025-07-07 14:52:13] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-07 14:52:13] 手电筒已关闭
[2025-07-07 14:52:13] 手电筒测试通过 - 手电筒正常点亮
[2025-07-07 14:52:14] 
开始执行: 摇杆使能测试
[2025-07-07 14:52:14] 执行摇杆测试...
[2025-07-07 14:52:14] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-07 14:52:14] 命令执行成功
[2025-07-07 14:52:14] 返回数据: 255
[2025-07-07 14:52:14] 摇杆测试通过，值: 255
[2025-07-07 14:52:14] 
开始执行: 前摄像头测试
[2025-07-07 14:52:14] 开始执行前摄像头测试...
[2025-07-07 14:52:15] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-07 14:52:15] 命令执行成功，返回: 无输出
[2025-07-07 14:52:15] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-07 14:52:16] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.320444949
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-07 14:52:16] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-07 14:52:16] 拉取命令执行成功
[2025-07-07 14:52:16] 返回数据: [ 35%] /data/camera/cam0_3840x2160.jpg
[ 71%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 20.7 MB/s (182502 bytes in 0.008s)
[2025-07-07 14:52:16] 图片已保存: cam0_3840x2160.jpg
[2025-07-07 14:52:18] 用户确认结果: 通过
[2025-07-07 14:52:18] 
开始执行: 光感测试
[2025-07-07 14:52:18] 执行光感测试...
[2025-07-07 14:52:19] 执行命令: adb shell evtest /dev/input/event1
[2025-07-07 14:52:29] 光感测试完成 - 检测到数值变化
[2025-07-07 14:52:29] 数值从 6 变化到 1
[2025-07-07 14:52:29] 
开始执行: 回充摄像头测试
[2025-07-07 14:52:29] 开始执行回充摄像头测试...
[2025-07-07 14:52:29] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-07 14:52:30] 命令执行失败: Cannot open device /dev/video20, exiting.

[2025-07-07 14:52:30] 
开始执行: 喇叭测试
[2025-07-07 14:52:30] 执行喇叭测试...
[2025-07-07 14:52:30] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-07 14:52:44] 命令执行成功
[2025-07-07 14:52:44] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-07 14:52:44] 音频播放完成
[2025-07-07 14:52:44] 
开始执行: 蓝牙测试
[2025-07-07 14:52:44] 执行蓝牙测试...
[2025-07-07 14:52:44] 启动蓝牙服务...
[2025-07-07 14:52:44] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-07 14:52:44] 开启蓝牙...
[2025-07-07 14:52:44] 执行命令: adb shell bluetoothctl devices
[2025-07-07 14:52:44] 命令执行成功
[2025-07-07 14:52:44] 无输出数据
[2025-07-07 14:52:44] 
开始执行: WiFi测试
[2025-07-07 14:52:44] 执行WiFi测试...
[2025-07-07 14:52:44] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-07 14:52:45] 命令执行成功，无输出
[2025-07-07 14:52:45] 执行命令 2: adb shell rm -f /var/run/wpa_supplicant/wlan0
[2025-07-07 14:52:45] 命令执行成功，无输出
[2025-07-07 14:52:45] 执行命令 3: adb shell ip link set wlan0 down
[2025-07-07 14:52:45] 命令执行成功，无输出
[2025-07-07 14:52:45] 执行命令 4: adb shell wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && wpa_cli -i wlan0 add_network && wpa_cli -i wlan0 set_network 0 ssid '"Orion_SZ_5G"' && wpa_cli -i wlan0 set_network 0 psk '"Orion@2025"' && wpa_cli -i wlan0 enable_network 0 && udhcpc -i wlan0 && iw wlan0 link && ip addr show wlan0
[2025-07-07 14:52:48] 命令执行成功，返回数据:
[2025-07-07 14:52:48]   Successfully initialized wpa_supplicant
[2025-07-07 14:52:48]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-07 14:52:48]   1
[2025-07-07 14:52:48]   OK
[2025-07-07 14:52:48]   OK
[2025-07-07 14:52:48]   OK
[2025-07-07 14:52:48]   deleting routers
[2025-07-07 14:52:48]   adding dns ************
[2025-07-07 14:52:48]   adding dns ***********
[2025-07-07 14:52:48]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-07 14:52:48]   SSID: Orion_SZ_5G
[2025-07-07 14:52:48]   freq: 5300
[2025-07-07 14:52:48]   RX: 2664 bytes (6 packets)
[2025-07-07 14:52:48]   TX: 1796 bytes (9 packets)
[2025-07-07 14:52:48]   signal: -50 dBm
[2025-07-07 14:52:48]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-07 14:52:48]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-07 14:52:48]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-07 14:52:48]   link/ether 24:21:5e:c0:3d:d1 brd ff:ff:ff:ff:ff:ff
[2025-07-07 14:52:48]   inet 192.168.20.165/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-07 14:52:48]   valid_lft forever preferred_lft forever
[2025-07-07 14:52:48]   inet6 fe80::6e3b:94f:d76d:2750/64 scope link tentative
[2025-07-07 14:52:48]   valid_lft forever preferred_lft forever
[2025-07-07 14:52:48] 执行命令 5: adb shell ping -c 3 www.baidu.com
[2025-07-07 14:52:50] 命令执行成功，返回数据:
[2025-07-07 14:52:50]   PING www.a.shifen.com (157.148.69.186) 56(84) bytes of data.
[2025-07-07 14:52:50]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=1 ttl=54 time=10.1 ms
[2025-07-07 14:52:50]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=2 ttl=54 time=11.0 ms
[2025-07-07 14:52:50]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=3 ttl=54 time=11.6 ms
[2025-07-07 14:52:50]   --- www.a.shifen.com ping statistics ---
[2025-07-07 14:52:50]   3 packets transmitted, 3 received, 0% packet loss, time 2002ms
[2025-07-07 14:52:50]   rtt min/avg/max/mdev = 10.112/10.895/11.574/0.601 ms
[2025-07-07 14:52:50] ✅ ping测试成功，网络连通性正常
[2025-07-07 14:52:50] WiFi连接成功
[2025-07-07 14:52:51] 
测试完成 - 通过率: 10/15
[2025-07-07 14:52:51] ❌ 存在测试失败项！
[2025-07-07 14:52:51] 测试记录已保存: records/3333_20250707_145251.json

