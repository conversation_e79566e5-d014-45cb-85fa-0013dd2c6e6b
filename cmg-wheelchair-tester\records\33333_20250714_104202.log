[2025-07-14 10:40:48] 开始测试 - SN: 33333
[2025-07-14 10:40:48] 
开始执行: 设备连接状态检测
[2025-07-14 10:40:48] 设备连接测试通过，检测到 1 个设备
[2025-07-14 10:40:48] 设备: ?	device
[2025-07-14 10:40:48] 
开始执行: USB关键器件检测
[2025-07-14 10:40:48] 执行USB设备检测...
[2025-07-14 10:40:48] 执行命令: adb shell lsusb
[2025-07-14 10:40:48] 设备返回数据:
[2025-07-14 10:40:48] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-14 10:40:48] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-14 10:40:48] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-14 10:40:48] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-14 10:40:48] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-14 10:40:48] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-14 10:40:48] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-14 10:40:48] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-14 10:40:48] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-14 10:40:48] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-14 10:40:48] 总共解析到 9 个设备
[2025-07-14 10:40:48] ✅ 所有预期的设备ID都已找到
[2025-07-14 10:40:49] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-14 10:40:49] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-14 10:40:49] ✅ USB设备检测通过
[2025-07-14 10:40:49] 
开始执行: CAN0测试
[2025-07-14 10:40:49] 执行CAN0测试流程...
[2025-07-14 10:40:49] 执行命令: adb shell ip link set can0 down
[2025-07-14 10:40:49] CAN0已关闭
[2025-07-14 10:40:49] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-14 10:40:49] CAN0已启动
[2025-07-14 10:40:49] CAN监听线程已启动...
[2025-07-14 10:40:50] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-14 10:40:50] CAN测试数据已发送，等待监听返回...
[2025-07-14 10:40:50] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-14 10:40:52] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-14 10:40:53] 
开始执行: GPS测试
[2025-07-14 10:40:53] 执行GPS测试...
[2025-07-14 10:40:53] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-14 10:41:03] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-14 10:41:03] 
开始执行: 4G模组测试
[2025-07-14 10:41:03] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-14 10:41:03] 监听线程已启动，等待串口数据...
[2025-07-14 10:41:05] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-14 10:41:05] AT+CCID指令已发送，等待串口返回...
[2025-07-14 10:41:05] 串口输出: AT+CCID
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: +CME ERROR: 13
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: AT+C
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: +CME ERROR: 58
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: AT+C
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: +CME ERROR: 58
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: AT+C
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: +CME ERROR: 58
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: AT+C
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:05] 串口输出: +CME ERROR: 58
[2025-07-14 10:41:05] 串口输出: 
[2025-07-14 10:41:17] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-14 10:41:18] 
开始执行: 按键测试
[2025-07-14 10:41:18] 开始按键测试...
[2025-07-14 10:41:18] 执行命令: adb shell evtest /dev/input/event5
[2025-07-14 10:41:21] 原始事件: Event: time 1751299368.774083, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-14 10:41:21] 解析结果: key_code=658, value=1
[2025-07-14 10:41:21] ✓ 检测到喇叭键(658)按下
[2025-07-14 10:41:21] 原始事件: Event: time 1751299368.947437, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-14 10:41:21] 解析结果: key_code=658, value=0
[2025-07-14 10:41:21] ✓ 喇叭键(658)测试通过
[2025-07-14 10:41:22] 原始事件: Event: time 1751299369.574114, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-14 10:41:22] 解析结果: key_code=662, value=1
[2025-07-14 10:41:22] ✓ 检测到档位减(662)按下
[2025-07-14 10:41:22] 原始事件: Event: time 1751299369.797454, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-14 10:41:22] 解析结果: key_code=662, value=0
[2025-07-14 10:41:22] ✓ 档位减(662)测试通过
[2025-07-14 10:41:23] 原始事件: Event: time 1751299370.724061, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-14 10:41:23] 解析结果: key_code=661, value=1
[2025-07-14 10:41:23] ✓ 检测到静音键(661)按下
[2025-07-14 10:41:23] 原始事件: Event: time 1751299370.874081, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-14 10:41:23] 解析结果: key_code=661, value=0
[2025-07-14 10:41:23] ✓ 静音键(661)测试通过
[2025-07-14 10:41:24] 原始事件: Event: time 1751299371.397410, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-14 10:41:24] 解析结果: key_code=663, value=1
[2025-07-14 10:41:24] ✓ 检测到语音键(663)按下
[2025-07-14 10:41:24] 原始事件: Event: time 1751299371.574196, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-14 10:41:24] 解析结果: key_code=663, value=0
[2025-07-14 10:41:24] ✓ 语音键(663)测试通过
[2025-07-14 10:41:25] 原始事件: Event: time 1751299372.124147, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-14 10:41:25] 解析结果: key_code=657, value=1
[2025-07-14 10:41:25] ✓ 检测到SOS键(657)按下
[2025-07-14 10:41:25] 原始事件: Event: time 1751299372.297439, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-14 10:41:25] 解析结果: key_code=657, value=0
[2025-07-14 10:41:25] ✓ SOS键(657)测试通过
[2025-07-14 10:41:26] 原始事件: Event: time 1751299373.627612, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-14 10:41:26] 解析结果: key_code=659, value=1
[2025-07-14 10:41:26] ✓ 检测到档位加(659)按下
[2025-07-14 10:41:26] 原始事件: Event: time 1751299373.724157, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-14 10:41:26] 解析结果: key_code=659, value=0
[2025-07-14 10:41:26] ✓ 档位加(659)测试通过
[2025-07-14 10:41:28] 原始事件: Event: time 1751299375.024206, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-14 10:41:28] 解析结果: key_code=660, value=1
[2025-07-14 10:41:28] ✓ 检测到智驾键(660)按下
[2025-07-14 10:41:28] 原始事件: Event: time 1751299375.174114, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-14 10:41:28] 解析结果: key_code=660, value=0
[2025-07-14 10:41:28] ✓ 智驾键(660)测试通过
[2025-07-14 10:41:29] 原始事件: Event: time 1751299376.524118, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-14 10:41:29] 解析结果: key_code=656, value=1
[2025-07-14 10:41:29] ✓ 检测到锁定键(656)按下
[2025-07-14 10:41:29] 原始事件: Event: time 1751299376.624115, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-14 10:41:29] 解析结果: key_code=656, value=0
[2025-07-14 10:41:29] ✓ 锁定键(656)测试通过
[2025-07-14 10:41:29] 按键测试完成 - 检测到8个按键
[2025-07-14 10:41:32] 
开始执行: 按键灯测试
[2025-07-14 10:41:32] 开始LED背光灯测试...
[2025-07-14 10:41:32] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-14 10:41:32] LED控制命令执行成功
[2025-07-14 10:41:32] 🔧 显示确认对话框: LED测试确认
[2025-07-14 10:41:32] 🔧 对话框窗口已创建
[2025-07-14 10:41:32] 🔧 '是'按钮已创建
[2025-07-14 10:41:32] 🔧 '否'按钮已创建
[2025-07-14 10:41:32] 🔧 对话框显示完成，等待用户响应...
[2025-07-14 10:41:34] 👤 用户选择: 是 (测试通过)
[2025-07-14 10:41:34] 🔧 对话框关闭，用户响应: yes
[2025-07-14 10:41:34] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-14 10:41:34] LED灯已关闭
[2025-07-14 10:41:34] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-14 10:41:35] 
开始执行: 手电筒测试
[2025-07-14 10:41:35] 开始手电筒LED测试...
[2025-07-14 10:41:35] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-14 10:41:35] 手电筒控制命令执行成功
[2025-07-14 10:41:35] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-14 10:41:35] 🔧 对话框窗口已创建
[2025-07-14 10:41:35] 🔧 '是'按钮已创建
[2025-07-14 10:41:35] 🔧 '否'按钮已创建
[2025-07-14 10:41:35] 🔧 对话框显示完成，等待用户响应...
[2025-07-14 10:41:36] 👤 用户选择: 是 (测试通过)
[2025-07-14 10:41:36] 🔧 对话框关闭，用户响应: yes
[2025-07-14 10:41:36] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-14 10:41:36] 手电筒已关闭
[2025-07-14 10:41:36] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-14 10:41:36] 
开始执行: 摇杆使能测试
[2025-07-14 10:41:36] 执行摇杆测试...
[2025-07-14 10:41:36] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-14 10:41:36] 命令执行成功
[2025-07-14 10:41:36] 返回数据: 255
[2025-07-14 10:41:36] 摇杆测试通过，值: 255
[2025-07-14 10:41:37] 
开始执行: 前摄像头测试
[2025-07-14 10:41:37] 开始执行前摄像头测试...
[2025-07-14 10:41:37] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-14 10:41:37] 命令执行成功，返回: 无输出
[2025-07-14 10:41:37] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-14 10:41:38] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.329733575
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-14 10:41:38] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-14 10:41:38] 拉取命令执行成功
[2025-07-14 10:41:38] 返回数据: [ 38%] /data/camera/cam0_3840x2160.jpg
[ 77%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 3.9 MB/s (169855 bytes in 0.042s)
[2025-07-14 10:41:38] 图片已保存: cam0_3840x2160.jpg
[2025-07-14 10:41:39] 用户确认结果: 通过
[2025-07-14 10:41:40] 
开始执行: 光感测试
[2025-07-14 10:41:40] 执行光感测试...
[2025-07-14 10:41:40] 执行命令: adb shell evtest /dev/input/event1
[2025-07-14 10:41:44] 光感测试完成 - 检测到数值变化
[2025-07-14 10:41:44] 数值从 0 变化到 2
[2025-07-14 10:41:44] 
开始执行: 回充摄像头测试
[2025-07-14 10:41:44] 开始执行回充摄像头测试...
[2025-07-14 10:41:44] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-14 10:41:44] 命令执行成功，返回: 无输出
[2025-07-14 10:41:44] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-14 10:41:45] 命令执行成功，返回: 无输出
[2025-07-14 10:41:45] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-14 10:41:46] 拉取命令执行成功
[2025-07-14 10:41:46] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 0.7 MB/s (45336 bytes in 0.061s)
[2025-07-14 10:41:46] 图片已保存: output.jpg
[2025-07-14 10:41:47] 用户确认结果: 通过
[2025-07-14 10:41:47] 
开始执行: 喇叭测试
[2025-07-14 10:41:47] 执行喇叭测试...
[2025-07-14 10:41:47] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-14 10:42:01] 命令执行成功
[2025-07-14 10:42:01] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-14 10:42:01] 音频播放完成
[2025-07-14 10:42:01] 
开始执行: 蓝牙测试
[2025-07-14 10:42:01] 执行蓝牙测试...
[2025-07-14 10:42:01] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-14 10:42:01] 执行命令: adb shell bluetoothctl show
[2025-07-14 10:42:01] 命令执行成功
[2025-07-14 10:42:01] 返回数据:
[2025-07-14 10:42:01]   Controller 40:55:48:07:E1:85 (public)
[2025-07-14 10:42:01]   Name: CMG-1
[2025-07-14 10:42:01]   Alias: CMG-1
[2025-07-14 10:42:01]   Class: 0x006c0000 (7077888)
[2025-07-14 10:42:01]   Powered: yes
[2025-07-14 10:42:01]   PowerState: on
[2025-07-14 10:42:01]   Discoverable: no
[2025-07-14 10:42:01]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-14 10:42:01]   Pairable: yes
[2025-07-14 10:42:01]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:42:01]   Modalias: usb:v1D6Bp0246d0544
[2025-07-14 10:42:01]   Discovering: no
[2025-07-14 10:42:01]   Roles: central
[2025-07-14 10:42:01]   Roles: peripheral
[2025-07-14 10:42:01]   Advertising Features:
[2025-07-14 10:42:01]   ActiveInstances: 0x00 (0)
[2025-07-14 10:42:01]   SupportedInstances: 0x10 (16)
[2025-07-14 10:42:01]   SupportedIncludes: tx-power
[2025-07-14 10:42:01]   SupportedIncludes: appearance
[2025-07-14 10:42:01]   SupportedIncludes: local-name
[2025-07-14 10:42:01]   SupportedSecondaryChannels: 1M
[2025-07-14 10:42:01]   SupportedSecondaryChannels: 2M
[2025-07-14 10:42:01]   SupportedSecondaryChannels: Coded
[2025-07-14 10:42:01]   SupportedCapabilities Key: MaxAdvLen
[2025-07-14 10:42:01]   SupportedCapabilities Value: 0x1f (31)
[2025-07-14 10:42:01]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-14 10:42:01]   SupportedCapabilities Value: 0x1f (31)
[2025-07-14 10:42:01]   SupportedFeatures: CanSetTxPower
[2025-07-14 10:42:01]   SupportedFeatures: HardwareOffload
[2025-07-14 10:42:01]   Advertisement Monitor Features:
[2025-07-14 10:42:01]   SupportedMonitorTypes: or_patterns
[2025-07-14 10:42:01] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-14 10:42:01] 蓝牙控制器MAC地址: 40:55:48:07:E1:85
[2025-07-14 10:42:02] 
开始执行: WiFi测试
[2025-07-14 10:42:02] 执行WiFi测试...
[2025-07-14 10:42:02] 开始网络发包延时测试...
[2025-07-14 10:42:02] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-14 10:42:02] 正在进行10秒钟的网络延时测试...
[2025-07-14 10:42:02] ❌ ping命令执行失败: ping: www.baidu.com: Temporary failure in name resolution

[2025-07-14 10:42:02] 
测试完成 - 通过率: 12/15
[2025-07-14 10:42:02] ❌ 存在测试失败项！
[2025-07-14 10:42:02] 测试记录已保存: records/33333_20250714_104202.json

