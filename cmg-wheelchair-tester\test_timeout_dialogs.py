#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试所有弹窗交互的超时功能
"""

import tkinter as tk
from tkinter import ttk
import time
import threading

def test_timeout_confirmation_dialog():
    """测试超时确认对话框"""
    print("=== 测试超时确认对话框 ===")
    
    root = tk.Tk()
    root.title("超时确认对话框测试")
    root.geometry("600x400")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="超时确认对话框测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    result_var = tk.StringVar(value="等待测试...")
    result_label = ttk.Label(main_frame, textvariable=result_var, font=("Arial", 12))
    result_label.pack(pady=10)
    
    def show_timeout_confirmation(title, message):
        """显示带超时的确认对话框"""
        # 创建自定义对话框
        dialog = tk.Toplevel(root)
        dialog.title(title)
        dialog.geometry("400x200")
        dialog.resizable(False, False)
        dialog.transient(root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        result = {"answer": None, "timeout": False}
        
        # 主框架
        dialog_frame = ttk.Frame(dialog, padding="20")
        dialog_frame.pack(fill=tk.BOTH, expand=True)
        
        # 消息文本
        message_label = ttk.Label(dialog_frame, text=message, justify=tk.CENTER, wraplength=350)
        message_label.pack(pady=(0, 20))
        
        # 倒计时显示
        countdown_var = tk.StringVar(value="剩余时间: 60秒")
        countdown_label = ttk.Label(dialog_frame, textvariable=countdown_var, foreground="red")
        countdown_label.pack(pady=(0, 20))
        
        # 按钮框架
        button_frame = ttk.Frame(dialog_frame)
        button_frame.pack()
        
        def on_yes():
            result["answer"] = "yes"
            dialog.destroy()
        
        def on_no():
            result["answer"] = "no"
            dialog.destroy()
        
        def on_timeout():
            result["timeout"] = True
            result["answer"] = "no"  # 超时默认为失败
            print("⏰ 确认对话框超时 - 1分钟内无响应，自动判定为失败")
            dialog.destroy()
        
        # 按钮
        yes_button = ttk.Button(button_frame, text="是", command=on_yes)
        yes_button.pack(side=tk.LEFT, padx=(0, 10))
        
        no_button = ttk.Button(button_frame, text="否", command=on_no)
        no_button.pack(side=tk.LEFT)
        
        # 倒计时更新
        remaining_time = [60]  # 使用列表以便在嵌套函数中修改
        
        def update_countdown():
            if remaining_time[0] > 0:
                countdown_var.set(f"剩余时间: {remaining_time[0]}秒")
                remaining_time[0] -= 1
                dialog.after(1000, update_countdown)
            else:
                on_timeout()
        
        # 开始倒计时
        update_countdown()
        
        # 绑定键盘事件
        dialog.bind("<Return>", lambda e: on_yes())
        dialog.bind("<Escape>", lambda e: on_no())
        
        # 等待窗口关闭
        dialog.wait_window()
        
        return result["answer"], result["timeout"]
    
    def test_normal_response():
        """测试正常响应"""
        result_var.set("测试正常响应...")
        root.update()
        
        def delayed_response():
            time.sleep(2)  # 2秒后自动点击"是"
            # 这里需要手动点击，所以只是演示
            
        answer, timeout = show_timeout_confirmation(
            "测试确认",
            "这是一个测试确认对话框\n请在2秒内点击'是'或'否'\n\n注意：1分钟内无响应将自动判定为失败"
        )
        
        if timeout:
            result_var.set("测试结果: 超时失败 ❌")
        elif answer == "yes":
            result_var.set("测试结果: 用户选择'是' ✅")
        elif answer == "no":
            result_var.set("测试结果: 用户选择'否' ✅")
        else:
            result_var.set("测试结果: 未知响应 ❓")
    
    def test_timeout_response():
        """测试超时响应（快速超时演示）"""
        result_var.set("测试超时响应...")
        root.update()
        
        # 创建快速超时的对话框（5秒演示）
        dialog = tk.Toplevel(root)
        dialog.title("快速超时测试")
        dialog.geometry("300x150")
        dialog.resizable(False, False)
        dialog.transient(root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        result = {"timeout": False}
        
        # 主框架
        dialog_frame = ttk.Frame(dialog, padding="20")
        dialog_frame.pack(fill=tk.BOTH, expand=True)
        
        # 消息文本
        message_label = ttk.Label(dialog_frame, text="这个对话框将在5秒后自动超时", justify=tk.CENTER)
        message_label.pack(pady=(0, 10))
        
        # 倒计时显示
        countdown_var = tk.StringVar(value="剩余时间: 5秒")
        countdown_label = ttk.Label(dialog_frame, textvariable=countdown_var, foreground="red")
        countdown_label.pack()
        
        def on_timeout():
            result["timeout"] = True
            print("⏰ 快速超时测试 - 5秒内无响应，自动关闭")
            dialog.destroy()
        
        # 倒计时更新
        remaining_time = [5]
        
        def update_countdown():
            if remaining_time[0] > 0:
                countdown_var.set(f"剩余时间: {remaining_time[0]}秒")
                remaining_time[0] -= 1
                dialog.after(1000, update_countdown)
            else:
                on_timeout()
        
        # 开始倒计时
        update_countdown()
        
        # 等待窗口关闭
        dialog.wait_window()
        
        if result["timeout"]:
            result_var.set("快速超时测试: 成功超时 ✅")
        else:
            result_var.set("快速超时测试: 未超时 ❌")
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=20)
    
    normal_button = ttk.Button(
        button_frame,
        text="测试正常响应",
        command=test_normal_response
    )
    normal_button.pack(side=tk.LEFT, padx=(0, 10))
    
    timeout_button = ttk.Button(
        button_frame,
        text="测试超时响应(5秒演示)",
        command=test_timeout_response
    )
    timeout_button.pack(side=tk.LEFT)
    
    # 说明文本
    info_frame = ttk.LabelFrame(main_frame, text="超时功能说明", padding="10")
    info_frame.pack(fill=tk.X, pady=20)
    
    info_text = """超时功能特点：
• 所有弹窗交互测试都增加了1分钟超时判断
• 超时后自动判定为失败，避免测试卡死
• 提供倒计时显示，用户可以看到剩余时间
• 超时时会记录详细的日志信息
• 支持键盘快捷键：Enter确认，Escape取消

适用的测试项目：
• SN输入对话框
• LED测试确认
• 手电筒测试确认  
• 摄像头测试窗口
• 按键测试窗口
• 光感测试窗口"""
    
    info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
    info_label.pack(fill=tk.X)
    
    print("超时确认对话框测试窗口已创建")
    root.mainloop()

if __name__ == "__main__":
    print("弹窗交互超时功能测试")
    print("=" * 50)
    
    test_timeout_confirmation_dialog()
