[2025-07-18 12:29:22] === 开始新的测试 ===
[2025-07-18 12:29:22] 开始测试
[2025-07-18 12:29:22] 工序: 4G模组
[2025-07-18 12:29:22] 序列号: 11
[2025-07-18 12:29:22] 开始运行 4G模组 工序测试，共 1 个项目
[2025-07-18 12:29:22] 
开始执行: 4G模组测试
[2025-07-18 12:29:22] 执行4G模组版本测试...（获取模组型号）
[2025-07-18 12:29:22] 监听线程已启动，等待串口数据...
[2025-07-18 12:29:24] 执行命令: adb shell "echo -e 'ATI\r' > /dev/ttyUSB0"
[2025-07-18 12:29:24] ATI指令已发送，等待串口返回模组版本信息...
[2025-07-18 12:29:24] 串口输出: 
[2025-07-18 12:29:24] 串口输出: Revision: EG912UGLAAR03A15M08
[2025-07-18 12:29:24] 检测到模组版本: EG912UGLAAR03A15M08
[2025-07-18 12:29:26] 测试执行出错: name 'ccid' is not defined
[2025-07-18 12:29:26] 
测试完成 - 通过率: 0/1
[2025-07-18 12:29:26] ❌ 存在测试失败项！
[2025-07-18 12:29:27] 测试记录已保存: records/11_20250718_122927.json

