# 4G网络测试功能总结

## 功能添加

根据用户要求，在WiFi测试前增加了4G网络测试功能，实现以下特性：

1. **测试前关闭WiFi** - 使用 `ifconfig wlan0 down` 确保走4G网络
2. **4G网络ping测试** - ping www.baidu.com 计算10秒延时平均值
3. **测试后恢复WiFi** - 使用 `ifconfig wlan0 up` 重新打开WiFi

## 详细实现内容

### 1. 配置文件修改

#### 新增4G网络测试项目
```json
{
    "id": "4g_network_test",
    "name": "4G网络测试",
    "description": "关闭WiFi后使用4G网络进行ping测试，计算延时平均值",
    "type": "4g_network_test",
    "test_duration": 10,
    "ping_target": "www.baidu.com",
    "expected_pattern": "64 bytes from"
}
```

#### 工序配置更新
在两个工序中都添加了4G网络测试，并确保在WiFi测试前：
- **整机半成品功能测试**：4G网络测试在第16位，WiFi测试在第17位
- **右手臂功能测试**：4G网络测试在第16位，WiFi测试在第17位

### 2. 测试方法实现

#### 完整的4G网络测试流程
```python
def run_4g_network_test(self, test_project):
    """4G网络测试 - 关闭WiFi后使用4G网络进行ping测试"""
    try:
        # 第一步：关闭WiFi网络
        disable_wifi_cmd = "ifconfig wlan0 down"
        result = subprocess.run(
            ["adb", "shell", disable_wifi_cmd],
            capture_output=True, text=True, timeout=10
        )
        
        # 第二步：4G网络ping测试
        ping_target = test_project.get("ping_target", "www.baidu.com")
        test_duration = test_project.get("test_duration", 10)
        
        ping_cmd = f"ping -c {test_duration} {ping_target}"
        result = subprocess.run(
            ["adb", "shell", ping_cmd],
            capture_output=True, text=True, timeout=test_duration + 10
        )
        
        # 第三步：解析延时数据并计算统计
        delay_times = []
        for line in result.stdout.split('\n'):
            if "64 bytes from" in line and "time=" in line:
                time_part = line.split("time=")[1].split("ms")[0].strip()
                delay_time = float(time_part)
                delay_times.append(delay_time)
        
        # 计算平均延时
        avg_delay = sum(delay_times) / len(delay_times)
        min_delay = min(delay_times)
        max_delay = max(delay_times)
        
        # 判断测试结果（至少80%的包成功）
        if len(delay_times) >= test_duration * 0.8:
            return True  # 测试通过
        else:
            return False  # 测试失败
            
    finally:
        # 第四步：恢复WiFi网络
        enable_wifi_cmd = "ifconfig wlan0 up"
        subprocess.run(["adb", "shell", enable_wifi_cmd], timeout=10)
```

### 3. 测试分发集成

在测试分发方法中添加了4G网络测试的调用：
```python
elif test_project["type"] == "4g_network_test":
    return self.run_4g_network_test(test_project)
```

## 测试流程详解

### 1. 网络切换流程
```
WiFi网络 → 关闭WiFi → 4G网络 → ping测试 → 恢复WiFi → WiFi网络
```

### 2. 具体执行步骤

#### 步骤1：关闭WiFi网络
- **命令**：`adb shell ifconfig wlan0 down`
- **目的**：确保网络流量走4G网络而不是WiFi
- **等待时间**：3秒，等待4G网络连接

#### 步骤2：4G网络ping测试
- **命令**：`adb shell ping -c 10 www.baidu.com`
- **参数**：
  - `-c 10`：发送10个ping包
  - `www.baidu.com`：ping目标（可配置）
- **超时**：测试时长 + 10秒

#### 步骤3：数据解析和统计
- **提取延时**：从ping输出中提取 `time=xx.x ms` 的数值
- **计算统计**：
  - 平均延时：所有延时的平均值
  - 最小延时：最小的延时值
  - 最大延时：最大的延时值
  - 成功率：成功ping包数量 / 总包数

#### 步骤4：恢复WiFi网络
- **命令**：`adb shell ifconfig wlan0 up`
- **目的**：恢复WiFi连接，为后续WiFi测试做准备
- **等待时间**：2秒，等待WiFi重新连接

### 3. 测试结果判断

#### 通过条件
- 至少80%的ping包成功返回（10个包中至少8个成功）
- 能够正确解析延时数据

#### 失败情况
- ping命令执行失败
- 丢包率超过20%
- 无法解析延时数据
- 网络连接超时

## 日志输出示例

### 成功案例
```
[14:30:15] 执行4G网络测试...
[14:30:15] 第一步：关闭WiFi网络...
[14:30:16] WiFi已关闭，等待4G网络连接...
[14:30:19] 第二步：使用4G网络进行ping测试...
[14:30:19] ping目标: www.baidu.com
[14:30:19] 测试时长: 10秒
[14:30:20] 64 bytes from 14.215.177.38: icmp_seq=1 ttl=54 time=45.2 ms
[14:30:21] 64 bytes from 14.215.177.38: icmp_seq=2 ttl=54 time=42.8 ms
...
[14:30:29] 4G网络延时统计:
[14:30:29]   成功ping包: 10 个
[14:30:29]   平均延时: 44.5 ms
[14:30:29]   最小延时: 38.1 ms
[14:30:29]   最大延时: 52.3 ms
[14:30:29] 4G网络测试通过
[14:30:29] 第三步：重新打开WiFi网络...
[14:30:30] WiFi已重新打开
```

### 失败案例
```
[14:30:15] 执行4G网络测试...
[14:30:15] 第一步：关闭WiFi网络...
[14:30:16] WiFi已关闭，等待4G网络连接...
[14:30:19] 第二步：使用4G网络进行ping测试...
[14:30:29] 4G网络ping测试失败: Network unreachable
[14:30:29] 4G网络测试失败：4G网络不通
[14:30:29] 第三步：重新打开WiFi网络...
[14:30:30] WiFi已重新打开
```

## 配置参数说明

### 可配置参数
- **test_duration**：测试时长（秒），默认10秒
- **ping_target**：ping目标地址，默认 www.baidu.com
- **expected_pattern**：期望的ping输出模式，默认 "64 bytes from"

### 参数调整建议
- **测试时长**：可根据需要调整为5-30秒
- **ping目标**：可使用其他稳定的服务器，如 8.8.8.8
- **成功率阈值**：当前设置为80%，可根据网络环境调整

## 错误处理机制

### 1. 网络切换失败
- 关闭WiFi失败：记录错误并返回测试失败
- 恢复WiFi失败：记录警告但不影响测试结果

### 2. ping测试失败
- 命令执行失败：记录错误信息
- 超时处理：设置合理的超时时间
- 数据解析失败：提供详细的错误信息

### 3. 异常恢复
- 使用 `finally` 块确保WiFi一定会被重新打开
- 即使测试过程中出现异常，也会尝试恢复WiFi

## 测试验证

### 验证工具
- `test_4g_network.py`：4G网络测试功能验证和模拟
- `python main.py`：实际程序测试

### 验证结果
```
✅ 找到4G网络测试项目
✅ 工序配置包含4G网络测试
✅ 4G网络测试在WiFi测试前
✅ 测试流程完整实现
✅ 错误处理机制完善
```

### 实际测试步骤
1. **启动程序**：`python main.py`
2. **选择工序**：选择包含4G网络测试的工序
3. **执行测试**：观察4G网络测试的执行过程
4. **验证结果**：确认延时统计和WiFi恢复

## 与WiFi测试的关系

### 测试顺序
1. **4G网络测试**：关闭WiFi → 4G ping测试 → 恢复WiFi
2. **WiFi测试**：连接WiFi → WiFi ping测试

### 网络状态管理
- 4G测试完成后WiFi已恢复，为WiFi测试做好准备
- 两个测试相互独立，互不影响
- 确保网络环境的正确切换

## 总结

通过添加4G网络测试功能，测试程序现在能够：

✅ **完整的网络测试覆盖**：同时测试4G和WiFi网络
✅ **准确的网络切换**：确保测试使用正确的网络连接
✅ **详细的延时统计**：提供平均、最小、最大延时数据
✅ **可靠的错误处理**：确保网络状态正确恢复
✅ **灵活的配置选项**：支持自定义测试参数

4G网络测试功能已成功集成到测试流程中，为设备的网络功能提供了更全面的验证。
