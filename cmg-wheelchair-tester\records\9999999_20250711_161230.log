[2025-07-11 16:09:37] 开始测试 - SN: 9999999
[2025-07-11 16:09:37] 
开始执行: 设备连接状态检测
[2025-07-11 16:09:38] 设备连接测试通过，检测到 1 个设备
[2025-07-11 16:09:38] 设备: ?	device
[2025-07-11 16:09:38] 
开始执行: USB关键器件检测
[2025-07-11 16:09:38] 执行USB设备检测...
[2025-07-11 16:09:38] 执行命令: adb shell lsusb
[2025-07-11 16:09:38] 设备返回数据:
[2025-07-11 16:09:38] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-11 16:09:38] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-11 16:09:38] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-11 16:09:38] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-11 16:09:38] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-11 16:09:38] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-11 16:09:38] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-11 16:09:38] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-11 16:09:38] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-11 16:09:38] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-11 16:09:38] 总共解析到 9 个设备
[2025-07-11 16:09:38] ✅ 所有预期的设备ID都已找到
[2025-07-11 16:09:38] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 16:09:38] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 16:09:38] ✅ USB设备检测通过
[2025-07-11 16:09:39] 
开始执行: CAN0测试
[2025-07-11 16:09:39] 执行CAN0测试流程...
[2025-07-11 16:09:39] 执行命令: adb shell ip link set can0 down
[2025-07-11 16:09:39] CAN0已关闭
[2025-07-11 16:09:39] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-11 16:09:39] CAN0已启动
[2025-07-11 16:09:39] CAN监听线程已启动...
[2025-07-11 16:09:40] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-11 16:09:40] CAN测试数据已发送，等待监听返回...
[2025-07-11 16:09:40] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-11 16:09:42] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-11 16:09:42] 
开始执行: GPS测试
[2025-07-11 16:09:42] 执行GPS测试...
[2025-07-11 16:09:42] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-11 16:09:52] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-11 16:09:54] 
开始执行: 4G模组测试
[2025-07-11 16:09:54] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-11 16:09:54] 监听线程已启动，等待串口数据...
[2025-07-11 16:09:56] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-11 16:09:56] AT+CCID指令已发送，等待串口返回...
[2025-07-11 16:09:56] 串口输出: AT+CCID
[2025-07-11 16:09:56] 串口输出: 
[2025-07-11 16:09:56] 串口输出: 
[2025-07-11 16:09:56] 串口输出: +CCID: 89860114851012535241
[2025-07-11 16:09:58] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-11 16:09:59] 
开始执行: 按键测试
[2025-07-11 16:09:59] 开始按键测试...
[2025-07-11 16:10:00] 执行命令: adb shell evtest /dev/input/event5
[2025-07-11 16:11:02] 按键测试失败 - 只检测到0个按键
[2025-07-11 16:11:04] 
开始执行: 按键灯测试
[2025-07-11 16:11:05] 开始LED背光灯测试...
[2025-07-11 16:11:05] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-11 16:11:05] LED控制命令执行成功
[2025-07-11 16:11:35] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-11 16:11:35] LED灯已关闭
[2025-07-11 16:11:35] LED测试通过 - 背光灯正常点亮
[2025-07-11 16:11:36] 
开始执行: 手电筒测试
[2025-07-11 16:11:36] 开始手电筒LED测试...
[2025-07-11 16:11:36] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-11 16:11:36] 手电筒控制命令执行成功
[2025-07-11 16:11:37] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-11 16:11:37] 手电筒已关闭
[2025-07-11 16:11:37] 手电筒测试通过 - 手电筒正常点亮
[2025-07-11 16:11:37] 
开始执行: 摇杆使能测试
[2025-07-11 16:11:37] 执行摇杆测试...
[2025-07-11 16:11:37] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-11 16:11:37] 命令执行成功
[2025-07-11 16:11:37] 返回数据: 255
[2025-07-11 16:11:37] 摇杆测试通过，值: 255
[2025-07-11 16:11:37] 
开始执行: 前摄像头测试
[2025-07-11 16:11:38] 开始执行前摄像头测试...
[2025-07-11 16:11:38] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-11 16:11:38] 命令执行成功，返回: 无输出
[2025-07-11 16:11:38] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-11 16:11:39] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.321600515
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-11 16:11:39] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-11 16:11:39] 拉取命令执行成功
[2025-07-11 16:11:39] 返回数据: [ 38%] /data/camera/cam0_3840x2160.jpg
[ 76%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 22.1 MB/s (171142 bytes in 0.007s)
[2025-07-11 16:11:39] 图片已保存: cam0_3840x2160.jpg
[2025-07-11 16:11:47] 用户确认结果: 通过
[2025-07-11 16:11:48] 
开始执行: 光感测试
[2025-07-11 16:11:48] 执行光感测试...
[2025-07-11 16:11:48] 执行命令: adb shell evtest /dev/input/event1
[2025-07-11 16:11:59] 光感测试完成 - 检测到数值变化
[2025-07-11 16:11:59] 数值从 2 变化到 0
[2025-07-11 16:12:00] 
开始执行: 回充摄像头测试
[2025-07-11 16:12:00] 开始执行回充摄像头测试...
[2025-07-11 16:12:00] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-11 16:12:00] 命令执行成功，返回: 无输出
[2025-07-11 16:12:00] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-11 16:12:01] 命令执行成功，返回: 无输出
[2025-07-11 16:12:01] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-11 16:12:01] 拉取命令执行成功
[2025-07-11 16:12:01] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 12.5 MB/s (45912 bytes in 0.004s)
[2025-07-11 16:12:01] 图片已保存: output.jpg
[2025-07-11 16:12:03] 用户确认结果: 通过
[2025-07-11 16:12:04] 
开始执行: 喇叭测试
[2025-07-11 16:12:04] 执行喇叭测试...
[2025-07-11 16:12:04] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-11 16:12:18] 命令执行成功
[2025-07-11 16:12:18] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-11 16:12:18] 音频播放完成
[2025-07-11 16:12:19] 
开始执行: 蓝牙测试
[2025-07-11 16:12:20] 执行蓝牙测试...
[2025-07-11 16:12:20] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-11 16:12:20] 执行命令: adb shell bluetoothctl show
[2025-07-11 16:12:20] 命令执行成功
[2025-07-11 16:12:20] 返回数据:
[2025-07-11 16:12:20]   Controller 24:21:5E:C0:2F:D9 (public)
[2025-07-11 16:12:20]   Name: CMG-1
[2025-07-11 16:12:20]   Alias: CMG-1
[2025-07-11 16:12:20]   Class: 0x006c0000 (7077888)
[2025-07-11 16:12:20]   Powered: yes
[2025-07-11 16:12:20]   PowerState: on
[2025-07-11 16:12:20]   Discoverable: no
[2025-07-11 16:12:20]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-11 16:12:20]   Pairable: yes
[2025-07-11 16:12:20]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-11 16:12:20]   Modalias: usb:v1D6Bp0246d0544
[2025-07-11 16:12:20]   Discovering: no
[2025-07-11 16:12:20]   Roles: central
[2025-07-11 16:12:20]   Roles: peripheral
[2025-07-11 16:12:20]   Advertising Features:
[2025-07-11 16:12:20]   ActiveInstances: 0x00 (0)
[2025-07-11 16:12:20]   SupportedInstances: 0x10 (16)
[2025-07-11 16:12:20]   SupportedIncludes: tx-power
[2025-07-11 16:12:20]   SupportedIncludes: appearance
[2025-07-11 16:12:20]   SupportedIncludes: local-name
[2025-07-11 16:12:20]   SupportedSecondaryChannels: 1M
[2025-07-11 16:12:20]   SupportedSecondaryChannels: 2M
[2025-07-11 16:12:20]   SupportedSecondaryChannels: Coded
[2025-07-11 16:12:20]   SupportedCapabilities Key: MaxAdvLen
[2025-07-11 16:12:20]   SupportedCapabilities Value: 0x1f (31)
[2025-07-11 16:12:20]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-11 16:12:20]   SupportedCapabilities Value: 0x1f (31)
[2025-07-11 16:12:20]   SupportedFeatures: CanSetTxPower
[2025-07-11 16:12:20]   SupportedFeatures: HardwareOffload
[2025-07-11 16:12:20]   Advertisement Monitor Features:
[2025-07-11 16:12:20]   SupportedMonitorTypes: or_patterns
[2025-07-11 16:12:20] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-11 16:12:20] 蓝牙控制器MAC地址: 24:21:5E:C0:2F:D9
[2025-07-11 16:12:20] 
开始执行: WiFi测试
[2025-07-11 16:12:20] 执行WiFi测试...
[2025-07-11 16:12:20] 开始网络发包延时测试...
[2025-07-11 16:12:20] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-11 16:12:20] 正在进行10秒钟的网络延时测试...
[2025-07-11 16:12:29] ping命令执行成功
[2025-07-11 16:12:29] 返回数据:
[2025-07-11 16:12:29]   PING www.baidu.com(2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207)) 56 data bytes
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=1 ttl=52 time=64.4 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=2 ttl=52 time=42.0 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=3 ttl=52 time=35.3 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=4 ttl=52 time=30.6 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=5 ttl=52 time=32.2 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=6 ttl=52 time=23.5 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=7 ttl=52 time=22.2 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=8 ttl=52 time=23.1 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=9 ttl=52 time=27.2 ms
[2025-07-11 16:12:29]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=10 ttl=52 time=23.1 ms
[2025-07-11 16:12:29]   --- www.baidu.com ping statistics ---
[2025-07-11 16:12:29]   10 packets transmitted, 10 received, 0% packet loss, time 9012ms
[2025-07-11 16:12:29]   rtt min/avg/max/mdev = 22.158/32.358/64.405/12.301 ms
[2025-07-11 16:12:29] 检测到延时: 64.4 ms
[2025-07-11 16:12:29] 检测到延时: 42.0 ms
[2025-07-11 16:12:29] 检测到延时: 35.3 ms
[2025-07-11 16:12:29] 检测到延时: 30.6 ms
[2025-07-11 16:12:29] 检测到延时: 32.2 ms
[2025-07-11 16:12:29] 检测到延时: 23.5 ms
[2025-07-11 16:12:29] 检测到延时: 22.2 ms
[2025-07-11 16:12:29] 检测到延时: 23.1 ms
[2025-07-11 16:12:29] 检测到延时: 27.2 ms
[2025-07-11 16:12:29] 检测到延时: 23.1 ms
[2025-07-11 16:12:29] ✅ WiFi延时测试成功
[2025-07-11 16:12:29] 发包数量: 10 个
[2025-07-11 16:12:29] 平均延时: 32.36 ms
[2025-07-11 16:12:29] 最小延时: 22.20 ms
[2025-07-11 16:12:29] 最大延时: 64.40 ms
[2025-07-11 16:12:30] 
测试完成 - 通过率: 13/15
[2025-07-11 16:12:30] ❌ 存在测试失败项！
[2025-07-11 16:12:30] 测试记录已保存: records/9999999_20250711_161230.json

