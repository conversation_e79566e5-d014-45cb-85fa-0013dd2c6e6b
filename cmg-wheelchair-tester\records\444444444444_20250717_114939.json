{"sn": "444444444444", "timestamp": "2025-07-17T11:49:39.602428", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-17T11:48:07.500391"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-17T11:48:07.835230"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-17T11:48:08.176694"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-17T11:48:08.824422"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-17T11:48:12.613410"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-17T11:48:23.246086"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-17T11:48:38.057624"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-17T11:48:46.809587"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-17T11:48:49.123443"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-17T11:48:50.599317"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-17T11:48:51.146068"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-17T11:48:54.744542"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-17T11:49:02.557135"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-17T11:49:05.685287"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 40:55:48:07:E4:2D", "timestamp": "2025-07-17T11:49:19.865474"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "平均延时: 12.65ms (优秀)", "timestamp": "2025-07-17T11:49:20.151877"}, "accel_test": {"test_name": "Accel", "status": "通过", "message": "191.6 FPS", "timestamp": "2025-07-17T11:49:36.341785"}, "gyro_test": {"test_name": "Gyro", "status": "通过", "message": "193.2 FPS", "timestamp": "2025-07-17T11:49:36.949336"}, "laser_test": {"test_name": "Laser", "status": "通过", "message": "4006.4 FPS", "timestamp": "2025-07-17T11:49:37.515238"}, "laser2_test": {"test_name": "Laser2", "status": "通过", "message": "3988.8 FPS", "timestamp": "2025-07-17T11:49:38.014508"}, "odom_test": {"test_name": "<PERSON><PERSON>", "status": "通过", "message": "100.4 FPS", "timestamp": "2025-07-17T11:49:38.855766"}}}