[2025-07-18 10:37:26] === 开始新的测试 ===
[2025-07-18 10:37:26] 开始测试
[2025-07-18 10:37:26] 工序: 右手臂功能测试
[2025-07-18 10:37:26] 序列号: 111111111111111
[2025-07-18 10:37:26] 开始运行 右手臂功能测试 工序测试，共 16 个项目
[2025-07-18 10:37:27] 
开始执行: 设备连接状态检测
[2025-07-18 10:37:27] 设备连接测试通过，检测到 1 个设备
[2025-07-18 10:37:27] 设备: ?	device
[2025-07-18 10:37:27] 
开始执行: 3568版本测试
[2025-07-18 10:37:27] 执行3568版本测试...
[2025-07-18 10:37:27] 读取ROM版本号...
[2025-07-18 10:37:27] 执行命令: adb shell uname -a
[2025-07-18 10:37:27] uname命令执行成功
[2025-07-18 10:37:27] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 10:37:27] ✅ 3568版本测试成功
[2025-07-18 10:37:28] ROM版本号: Jul 9 12:01:12
[2025-07-18 10:37:28] ✅ 确认为RK3568平台
[2025-07-18 10:37:28] 
开始执行: USB关键器件检测
[2025-07-18 10:37:28] 执行USB设备检测...
[2025-07-18 10:37:28] 执行命令: adb shell lsusb
[2025-07-18 10:37:28] 设备返回数据:
[2025-07-18 10:37:28] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 10:37:28] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 10:37:28] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 10:37:28] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 10:37:28] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 10:37:28] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 10:37:28] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 10:37:28] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 10:37:28] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 10:37:28] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 10:37:28] 总共解析到 9 个设备
[2025-07-18 10:37:28] ✅ 所有预期的设备ID都已找到
[2025-07-18 10:37:28] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 10:37:28] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 10:37:28] ✅ USB设备检测通过
[2025-07-18 10:37:29] 
开始执行: CAN0测试
[2025-07-18 10:37:29] 执行CAN0测试流程...
[2025-07-18 10:37:29] 执行命令: adb shell ip link set can0 down
[2025-07-18 10:37:29] CAN0已关闭
[2025-07-18 10:37:29] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 10:37:29] CAN0已启动
[2025-07-18 10:37:29] CAN监听线程已启动...
[2025-07-18 10:37:30] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 10:37:30] CAN测试数据已发送，等待监听返回...
[2025-07-18 10:37:30] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 10:37:32] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 10:37:32] 
开始执行: GPS测试
[2025-07-18 10:37:32] 执行GPS测试...
[2025-07-18 10:37:32] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 10:37:42] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 10:37:43] 
开始执行: 4G模组测试
[2025-07-18 10:37:43] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 10:37:43] 监听线程已启动，等待串口数据...
[2025-07-18 10:37:45] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 10:37:45] AT+CCID指令已发送，等待串口返回...
[2025-07-18 10:37:45] 串口输出: AT+CCID
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: +CME ERROR: 13
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: AT+C
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: +CME ERROR: 58
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: AT+C
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: +CME ERROR: 58
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: AT+C
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: +CME ERROR: 58
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: AT+C
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: +CME ERROR: 58
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: AT+C
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: +CME ERROR: 58
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: AT+C
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:45] 串口输出: +CME ERROR: 58
[2025-07-18 10:37:45] 串口输出: 
[2025-07-18 10:37:57] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-18 10:37:58] 
开始执行: 按键测试
[2025-07-18 10:37:58] 开始按键测试...
[2025-07-18 10:37:58] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 10:38:17] 原始事件: Event: time 1751299378.318128, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-18 10:38:17] 解析结果: key_code=662, value=1
[2025-07-18 10:38:17] ✓ 检测到档位减(662)按下
[2025-07-18 10:38:17] 原始事件: Event: time 1751299378.491450, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-18 10:38:17] 解析结果: key_code=662, value=0
[2025-07-18 10:38:17] ✓ 档位减(662)测试通过
[2025-07-18 10:38:18] 原始事件: Event: time 1751299378.818028, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-18 10:38:18] 解析结果: key_code=658, value=1
[2025-07-18 10:38:18] ✓ 检测到喇叭键(658)按下
[2025-07-18 10:38:18] 原始事件: Event: time 1751299378.968029, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-18 10:38:18] 解析结果: key_code=658, value=0
[2025-07-18 10:38:18] ✓ 喇叭键(658)测试通过
[2025-07-18 10:38:20] 原始事件: Event: time 1751299381.041571, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-18 10:38:20] 解析结果: key_code=659, value=1
[2025-07-18 10:38:20] ✓ 检测到档位加(659)按下
[2025-07-18 10:38:20] 原始事件: Event: time 1751299381.191344, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-18 10:38:20] 解析结果: key_code=659, value=0
[2025-07-18 10:38:20] ✓ 档位加(659)测试通过
[2025-07-18 10:38:21] 原始事件: Event: time 1751299381.641365, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-18 10:38:21] 解析结果: key_code=660, value=1
[2025-07-18 10:38:21] ✓ 检测到智驾键(660)按下
[2025-07-18 10:38:21] 原始事件: Event: time 1751299381.818191, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-18 10:38:21] 解析结果: key_code=660, value=0
[2025-07-18 10:38:21] ✓ 智驾键(660)测试通过
[2025-07-18 10:38:21] 原始事件: Event: time 1751299382.191395, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-18 10:38:21] 解析结果: key_code=656, value=1
[2025-07-18 10:38:21] ✓ 检测到锁定键(656)按下
[2025-07-18 10:38:21] 原始事件: Event: time 1751299382.318068, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-18 10:38:21] 解析结果: key_code=656, value=0
[2025-07-18 10:38:21] ✓ 锁定键(656)测试通过
[2025-07-18 10:38:22] 原始事件: Event: time 1751299382.818063, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 10:38:22] 解析结果: key_code=663, value=1
[2025-07-18 10:38:22] ✓ 检测到语音键(663)按下
[2025-07-18 10:38:22] 原始事件: Event: time 1751299382.991378, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 10:38:22] 解析结果: key_code=663, value=0
[2025-07-18 10:38:22] ✓ 语音键(663)测试通过
[2025-07-18 10:38:22] 原始事件: Event: time 1751299383.318037, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-18 10:38:22] 解析结果: key_code=657, value=1
[2025-07-18 10:38:22] ✓ 检测到SOS键(657)按下
[2025-07-18 10:38:22] 原始事件: Event: time 1751299383.468039, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-18 10:38:22] 解析结果: key_code=657, value=0
[2025-07-18 10:38:22] ✓ SOS键(657)测试通过
[2025-07-18 10:38:23] 原始事件: Event: time 1751299384.291498, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-18 10:38:23] 解析结果: key_code=657, value=1
[2025-07-18 10:38:23] ✓ 检测到SOS键(657)按下
[2025-07-18 10:38:24] 原始事件: Event: time 1751299384.518011, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-18 10:38:24] 解析结果: key_code=657, value=0
[2025-07-18 10:38:24] ✓ SOS键(657)测试通过
[2025-07-18 10:38:25] 原始事件: Event: time 1751299385.747613, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 10:38:25] 解析结果: key_code=663, value=1
[2025-07-18 10:38:25] ✓ 检测到语音键(663)按下
[2025-07-18 10:38:25] 原始事件: Event: time 1751299385.891598, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 10:38:25] 解析结果: key_code=663, value=0
[2025-07-18 10:38:25] ✓ 语音键(663)测试通过
[2025-07-18 10:38:25] 原始事件: Event: time 1751299386.468103, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-18 10:38:25] 解析结果: key_code=659, value=1
[2025-07-18 10:38:25] ✓ 检测到档位加(659)按下
[2025-07-18 10:38:26] 原始事件: Event: time 1751299386.641301, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-18 10:38:26] 解析结果: key_code=659, value=0
[2025-07-18 10:38:26] ✓ 档位加(659)测试通过
[2025-07-18 10:38:26] 原始事件: Event: time 1751299387.191473, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-18 10:38:26] 解析结果: key_code=661, value=1
[2025-07-18 10:38:26] ✓ 检测到静音键(661)按下
[2025-07-18 10:38:26] 原始事件: Event: time 1751299387.391368, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-18 10:38:26] 解析结果: key_code=661, value=0
[2025-07-18 10:38:26] ✓ 静音键(661)测试通过
[2025-07-18 10:38:26] 按键测试完成 - 检测到8个按键
[2025-07-18 10:38:29] 
开始执行: 按键灯测试
[2025-07-18 10:38:29] 开始LED背光灯测试...
[2025-07-18 10:38:29] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 10:38:29] LED控制命令执行成功
[2025-07-18 10:38:29] 🔧 显示确认对话框: LED测试确认
[2025-07-18 10:38:29] 🔧 对话框窗口已创建
[2025-07-18 10:38:29] 🔧 '是'按钮已创建
[2025-07-18 10:38:29] 🔧 '否'按钮已创建
[2025-07-18 10:38:29] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 10:38:33] 👤 用户选择: 是 (测试通过)
[2025-07-18 10:38:33] 🔧 对话框关闭，用户响应: yes
[2025-07-18 10:38:33] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-18 10:38:33] LED灯已关闭
[2025-07-18 10:38:33] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-18 10:38:34] 
开始执行: 手电筒测试
[2025-07-18 10:38:34] 开始手电筒LED测试...
[2025-07-18 10:38:34] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 10:38:34] 手电筒控制命令执行成功
[2025-07-18 10:38:34] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-18 10:38:34] 🔧 对话框窗口已创建
[2025-07-18 10:38:34] 🔧 '是'按钮已创建
[2025-07-18 10:38:34] 🔧 '否'按钮已创建
[2025-07-18 10:38:34] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 10:38:35] 👤 用户选择: 是 (测试通过)
[2025-07-18 10:38:35] 🔧 对话框关闭，用户响应: yes
[2025-07-18 10:38:35] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-18 10:38:35] 手电筒已关闭
[2025-07-18 10:38:35] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-18 10:38:35] 
开始执行: 摇杆使能测试
[2025-07-18 10:38:35] 执行摇杆测试...
[2025-07-18 10:38:35] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 10:38:35] 命令执行成功
[2025-07-18 10:38:35] 返回数据: 255
[2025-07-18 10:38:35] 摇杆测试通过，值: 255
[2025-07-18 10:38:36] 
开始执行: 前摄像头测试
[2025-07-18 10:38:36] 开始执行前摄像头测试...
[2025-07-18 10:38:36] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 10:38:36] 命令执行成功，返回: 无输出
[2025-07-18 10:38:36] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-18 10:38:37] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.325285835
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-18 10:38:37] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-18 10:38:37] 拉取命令执行成功
[2025-07-18 10:38:37] 返回数据: [ 38%] /data/camera/cam0_3840x2160.jpg
[ 76%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 23.8 MB/s (171559 bytes in 0.007s)
[2025-07-18 10:38:37] 图片已保存: cam0_3840x2160.jpg
[2025-07-18 10:38:40] 用户确认结果: 通过
[2025-07-18 10:38:41] 
开始执行: 光感测试
[2025-07-18 10:38:41] 执行光感测试...
[2025-07-18 10:38:41] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 10:38:45] 光感测试完成 - 检测到数值变化
[2025-07-18 10:38:45] 数值从 0 变化到 5
[2025-07-18 10:38:45] 
开始执行: 回充摄像头测试
[2025-07-18 10:38:45] 开始执行回充摄像头测试...
[2025-07-18 10:38:45] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 10:38:45] 命令执行成功，返回: 无输出
[2025-07-18 10:38:45] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-18 10:38:46] 命令执行成功，返回: 无输出
[2025-07-18 10:38:46] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-18 10:38:46] 拉取命令执行成功
[2025-07-18 10:38:46] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 15.7 MB/s (41624 bytes in 0.003s)
[2025-07-18 10:38:46] 图片已保存: output.jpg
[2025-07-18 10:38:53] 用户确认结果: 通过
[2025-07-18 10:38:53] 
开始执行: 喇叭测试
[2025-07-18 10:38:53] 执行喇叭测试...
[2025-07-18 10:38:53] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 10:39:07] 命令执行成功
[2025-07-18 10:39:07] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-18 10:39:07] 音频播放完成
[2025-07-18 10:39:08] 
开始执行: 蓝牙测试
[2025-07-18 10:39:08] 执行蓝牙测试...
[2025-07-18 10:39:08] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 10:39:08] 执行命令: adb shell bluetoothctl show
[2025-07-18 10:39:08] 命令执行成功
[2025-07-18 10:39:08] 返回数据:
[2025-07-18 10:39:08]   Controller 24:21:5E:C0:2A:83 (public)
[2025-07-18 10:39:08]   Name: CMG-1
[2025-07-18 10:39:08]   Alias: CMG-1
[2025-07-18 10:39:08]   Class: 0x006c0000 (7077888)
[2025-07-18 10:39:08]   Powered: yes
[2025-07-18 10:39:08]   PowerState: on
[2025-07-18 10:39:08]   Discoverable: no
[2025-07-18 10:39:08]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-18 10:39:08]   Pairable: yes
[2025-07-18 10:39:08]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:39:08]   Modalias: usb:v1D6Bp0246d0544
[2025-07-18 10:39:08]   Discovering: no
[2025-07-18 10:39:08]   Roles: central
[2025-07-18 10:39:08]   Roles: peripheral
[2025-07-18 10:39:08]   Advertising Features:
[2025-07-18 10:39:08]   ActiveInstances: 0x00 (0)
[2025-07-18 10:39:08]   SupportedInstances: 0x10 (16)
[2025-07-18 10:39:08]   SupportedIncludes: tx-power
[2025-07-18 10:39:08]   SupportedIncludes: appearance
[2025-07-18 10:39:08]   SupportedIncludes: local-name
[2025-07-18 10:39:08]   SupportedSecondaryChannels: 1M
[2025-07-18 10:39:08]   SupportedSecondaryChannels: 2M
[2025-07-18 10:39:08]   SupportedSecondaryChannels: Coded
[2025-07-18 10:39:08]   SupportedCapabilities Key: MaxAdvLen
[2025-07-18 10:39:08]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 10:39:08]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-18 10:39:08]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 10:39:08]   SupportedFeatures: CanSetTxPower
[2025-07-18 10:39:08]   SupportedFeatures: HardwareOffload
[2025-07-18 10:39:08]   Advertisement Monitor Features:
[2025-07-18 10:39:08]   SupportedMonitorTypes: or_patterns
[2025-07-18 10:39:08] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-18 10:39:08] 蓝牙控制器MAC地址: 24:21:5E:C0:2A:83
[2025-07-18 10:39:08] 
开始执行: WiFi测试
[2025-07-18 10:39:08] 执行WiFi测试...
[2025-07-18 10:39:08] 第一步：连接WiFi网络...
[2025-07-18 10:39:08] 停止现有的wpa_supplicant进程...
[2025-07-18 10:39:08] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 10:39:08] 清理wpa_supplicant socket文件...
[2025-07-18 10:39:08] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 10:39:08] 关闭wlan0接口...
[2025-07-18 10:39:08] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 10:39:08] 连接WiFi网络...
[2025-07-18 10:39:08] 执行WiFi连接命令...
[2025-07-18 10:39:08] SSID: Orion_SZ_5G
[2025-07-18 10:39:12] WiFi连接命令执行完成，返回数据:
[2025-07-18 10:39:12]   Successfully initialized wpa_supplicant
[2025-07-18 10:39:12]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-18 10:39:12]   1
[2025-07-18 10:39:12]   OK
[2025-07-18 10:39:12]   OK
[2025-07-18 10:39:12]   OK
[2025-07-18 10:39:12]   deleting routers
[2025-07-18 10:39:12]   adding dns ************
[2025-07-18 10:39:12]   adding dns ***********
[2025-07-18 10:39:12]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-18 10:39:12]   SSID: Orion_SZ_5G
[2025-07-18 10:39:12]   freq: 5300
[2025-07-18 10:39:12]   RX: 3466 bytes (13 packets)
[2025-07-18 10:39:12]   TX: 2498 bytes (19 packets)
[2025-07-18 10:39:12]   signal: -50 dBm
[2025-07-18 10:39:12]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-18 10:39:12]   tx bitrate: 258.0 MBit/s 40MHz HE-MCS 10 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-18 10:39:12]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-18 10:39:12]   link/ether 24:21:5e:c0:36:44 brd ff:ff:ff:ff:ff:ff
[2025-07-18 10:39:12]   inet 192.168.20.188/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-18 10:39:12]   valid_lft forever preferred_lft forever
[2025-07-18 10:39:12]   inet6 fe80::125d:94ab:ea3:ce58/64 scope link
[2025-07-18 10:39:12]   valid_lft forever preferred_lft forever
[2025-07-18 10:39:12] ✅ WiFi连接成功
[2025-07-18 10:39:12] 等待网络稳定...
[2025-07-18 10:39:15] 第二步：开始网络发包延时测试...
[2025-07-18 10:39:15] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-18 10:39:15] 正在进行10秒钟的网络延时测试...
[2025-07-18 10:39:24] ping命令执行成功
[2025-07-18 10:39:24] 返回数据:
[2025-07-18 10:39:24]   PING www.a.shifen.com (157.148.69.151) 56(84) bytes of data.
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=1 ttl=54 time=10.5 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=2 ttl=54 time=11.3 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=3 ttl=54 time=10.9 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=4 ttl=54 time=11.0 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=5 ttl=54 time=11.6 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=6 ttl=54 time=11.7 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=7 ttl=54 time=10.4 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=8 ttl=54 time=10.9 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=9 ttl=54 time=12.4 ms
[2025-07-18 10:39:24]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=10 ttl=54 time=10.1 ms
[2025-07-18 10:39:24]   --- www.a.shifen.com ping statistics ---
[2025-07-18 10:39:24]   10 packets transmitted, 10 received, 0% packet loss, time 9011ms
[2025-07-18 10:39:24]   rtt min/avg/max/mdev = 10.143/11.087/12.379/0.644 ms
[2025-07-18 10:39:24] 检测到延时: 10.5 ms
[2025-07-18 10:39:24] 检测到延时: 11.3 ms
[2025-07-18 10:39:24] 检测到延时: 10.9 ms
[2025-07-18 10:39:24] 检测到延时: 11.0 ms
[2025-07-18 10:39:24] 检测到延时: 11.6 ms
[2025-07-18 10:39:24] 检测到延时: 11.7 ms
[2025-07-18 10:39:24] 检测到延时: 10.4 ms
[2025-07-18 10:39:24] 检测到延时: 10.9 ms
[2025-07-18 10:39:24] 检测到延时: 12.4 ms
[2025-07-18 10:39:24] 检测到延时: 10.1 ms
[2025-07-18 10:39:24] ✅ WiFi延时测试成功
[2025-07-18 10:39:24] 发包数量: 10 个
[2025-07-18 10:39:24] 平均延时: 11.08 ms
[2025-07-18 10:39:24] 最小延时: 10.10 ms
[2025-07-18 10:39:24] 最大延时: 12.40 ms
[2025-07-18 10:39:25] 
测试完成 - 通过率: 14/16
[2025-07-18 10:39:25] ❌ 存在测试失败项！
[2025-07-18 10:39:25] 测试记录已保存: records/111111111111111_20250718_103925.json
[2025-07-18 10:39:25] 测试日志已保存: records/111111111111111_20250718_103925.log

