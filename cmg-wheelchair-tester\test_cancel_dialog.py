#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试序列号输入弹窗取消功能
验证：
1. 点击"取消"按钮只关闭弹窗，不退出程序
2. 可以查看测试结果和进行调试
3. 可以手动点击开始测试按钮
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time
import random

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}}

def center_dialog_on_parent(parent, dialog):
    """将对话框居中显示在父窗口上"""
    try:
        parent.update_idletasks()
        dialog.update_idletasks()
        
        root_x = parent.winfo_x()
        root_y = parent.winfo_y()
        root_width = parent.winfo_width()
        root_height = parent.winfo_height()
        
        dialog_width = dialog.winfo_width()
        dialog_height = dialog.winfo_height()
        
        x = root_x + (root_width - dialog_width) // 2
        y = root_y + (root_height - dialog_height) // 2
        
        screen_width = dialog.winfo_screenwidth()
        screen_height = dialog.winfo_screenheight()
        
        x = max(0, min(x, screen_width - dialog_width))
        y = max(0, min(y, screen_height - dialog_height))
        
        dialog.geometry(f"+{x}+{y}")
    except Exception as e:
        print(f"居中对话框时出错: {e}")

def test_cancel_dialog():
    """测试序列号输入弹窗取消功能"""
    print("=== 测试序列号输入弹窗取消功能 ===")
    
    config = load_config()
    work_processes = config.get("work_processes", {})
    test_projects = config.get("test_projects", [])
    
    if not work_processes:
        print("❌ 没有找到工序配置")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title("序列号输入弹窗取消功能测试")
    root.geometry("1000x700")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 1000) // 2
    y = (screen_height - 700) // 2
    root.geometry(f"1000x700+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="序列号输入弹窗取消功能测试", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 修改说明
    change_frame = ttk.LabelFrame(main_frame, text="功能修改说明", padding="15")
    change_frame.pack(fill=tk.X, pady=(0, 20))
    
    change_text = """🔧 序列号输入弹窗取消功能修改:

修改前:
  • 点击"退出程序"按钮会退出整个程序
  • 无法返回主界面查看测试结果
  • 不便于调试和确认测试项目

修改后:
  ✅ 点击"取消"按钮只关闭弹窗，不退出程序
  ✅ 返回主界面，可以查看测试结果
  ✅ 可以进行单项测试和调试
  ✅ 可以手动点击"开始测试"按钮继续

🎯 使用场景:
  • 测试完成后想查看详细结果再决定是否继续
  • 发现问题需要进行单项测试调试
  • 暂时不想继续测试，但不想退出程序
  • 需要确认测试项目状态"""
    
    ttk.Label(change_frame, text=change_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 创建左右分栏
    content_frame = ttk.Frame(main_frame)
    content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 左侧：测试项目列表
    left_frame = ttk.LabelFrame(content_frame, text="测试项目列表", padding="10")
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
    
    # 创建测试项目树
    columns = ("data", "result")
    test_tree = ttk.Treeview(left_frame, columns=columns, show="tree headings", height=12)
    
    # 设置列标题
    test_tree.heading("#0", text="测试项目", anchor=tk.W)
    test_tree.heading("data", text="测试数据", anchor=tk.W)
    test_tree.heading("result", text="测试结果", anchor=tk.CENTER)
    
    # 设置列宽
    test_tree.column("#0", width=180, minwidth=150)
    test_tree.column("data", width=150, minwidth=100)
    test_tree.column("result", width=80, minwidth=60)
    
    # 设置样式
    test_tree.tag_configure("pass_result", foreground="#28a745", font=("Microsoft YaHei UI", 10, "bold"))
    test_tree.tag_configure("fail_result", foreground="#dc3545", font=("Microsoft YaHei UI", 10, "bold"))
    test_tree.tag_configure("testing_result", foreground="#ffc107", font=("Microsoft YaHei UI", 10, "bold"))
    
    # 添加滚动条
    tree_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=test_tree.yview)
    test_tree.configure(yscrollcommand=tree_scrollbar.set)
    
    test_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 右侧：日志和控制
    right_frame = ttk.Frame(content_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    
    # 状态显示
    status_frame = ttk.Frame(right_frame)
    status_frame.pack(fill=tk.X, pady=(0, 10))
    
    status_var = tk.StringVar(value="准备开始测试...")
    status_label = ttk.Label(status_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack()
    
    # 日志显示
    log_frame = ttk.LabelFrame(right_frame, text="测试日志", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    log_text = tk.Text(log_frame, height=10, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message, color=None):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if color:
            start_pos = log_text.index(tk.END + "-1c")
            log_text.insert(tk.END, log_entry)
            end_pos = log_text.index(tk.END + "-1c")
            log_text.tag_add(color, start_pos, end_pos)
            log_text.tag_config(color, foreground=color)
        else:
            log_text.insert(tk.END, log_entry)
        
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 测试状态
    test_state = {
        "selected_process": "整机半成品功能测试",
        "current_serial_number": None,
        "test_count": 0,
        "running": False,
        "test_results": {}
    }
    
    # 初始化测试项目
    def init_test_projects():
        """初始化测试项目"""
        # 清空现有项目
        for item in test_tree.get_children():
            test_tree.delete(item)
        
        # 获取当前工序的测试项目
        if test_state["selected_process"] in work_processes:
            test_ids = work_processes[test_state["selected_process"]]["test_ids"]
            
            for test_id in test_ids:
                # 查找项目名称
                project_name = test_id
                for project in test_projects:
                    if project["id"] == test_id:
                        project_name = project["name"]
                        break
                
                # 创建测试项目
                item = test_tree.insert("", "end", text=project_name)
                test_tree.set(item, "data", "")
                test_tree.set(item, "result", "")
                
                # 初始化测试结果
                test_state["test_results"][test_id] = {
                    "name": project_name,
                    "status": "未测试",
                    "message": "",
                    "item": item
                }
    
    def update_test_item(test_id, status, message=""):
        """更新测试项目状态"""
        if test_id in test_state["test_results"]:
            result = test_state["test_results"][test_id]
            item = result["item"]
            
            result["status"] = status
            result["message"] = message
            
            test_tree.set(item, "data", message)
            
            if status == "通过":
                test_tree.set(item, "result", "PASS")
                test_tree.item(item, tags=("pass_result",))
            elif status == "失败":
                test_tree.set(item, "result", "FAIL")
                test_tree.item(item, tags=("fail_result",))
            elif status == "测试中":
                test_tree.set(item, "result", "测试中")
                test_tree.item(item, tags=("testing_result",))
            else:
                test_tree.set(item, "result", "")
                test_tree.item(item, tags=())
    
    def show_serial_input_dialog(title="输入序列号", prompt="请输入设备序列号"):
        """显示序列号输入对话框"""
        dialog = tk.Toplevel(root)
        dialog.title(title)
        dialog.geometry("500x280")
        dialog.transient(root)
        dialog.grab_set()
        dialog.resizable(False, False)
        
        # 强制更新窗口尺寸信息
        root.update()
        dialog.update()
        
        # 等待窗口完全创建后居中
        root.after(10, lambda: center_dialog_on_parent(root, dialog))
        
        # 禁止关闭窗口
        dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        result = {"confirmed": False, "serial_number": ""}
        
        main_frame = ttk.Frame(dialog, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text=title, 
                               font=("Microsoft YaHei UI", 14, "bold"))
        title_label.pack(pady=(0, 20))
        
        # 当前工序信息
        process_info = f"当前工序: {test_state['selected_process']}"
        info_label = ttk.Label(main_frame, text=process_info, 
                              font=("Microsoft YaHei UI", 10), 
                              foreground="blue")
        info_label.pack(pady=(0, 20))
        
        # 序列号输入区域
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=(0, 25))
        input_frame.columnconfigure(1, weight=1)
        
        # 输入标签
        ttk.Label(input_frame, text="设备序列号:", 
                 font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)
        
        # 序列号输入框
        serial_var = tk.StringVar()
        # 自动生成序列号
        auto_serial = f"CMG202412{test_state['test_count']+1:04d}"
        serial_var.set(auto_serial)
        
        serial_entry = ttk.Entry(input_frame, textvariable=serial_var, 
                               font=("Microsoft YaHei UI", 11), width=20)
        serial_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
        serial_entry.focus()
        serial_entry.select_range(0, tk.END)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def confirm_serial():
            """确认序列号并开始测试"""
            serial_number = serial_var.get().strip()
            if serial_number:
                result["confirmed"] = True
                result["serial_number"] = serial_number
                test_state["current_serial_number"] = serial_number
                dialog.destroy()
                
                # 开始测试
                start_test_simulation(serial_number)
            else:
                messagebox.showwarning("警告", "请输入序列号")
                serial_entry.focus()
        
        def cancel_serial():
            """取消输入，只关闭弹窗"""
            result["confirmed"] = False
            dialog.destroy()
            log_message("用户取消序列号输入，返回主界面", "orange")
            log_message("可以查看测试结果或手动点击开始测试", "blue")
            status_var.set("已取消序列号输入，可以查看结果或继续测试")
        
        # 绑定回车键
        serial_entry.bind("<Return>", lambda e: confirm_serial())
        
        # 按钮区域居中布局
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)
        
        # 按钮布局：开始测试在左边，取消在右边
        start_btn = ttk.Button(button_container, text="开始测试", 
                             command=confirm_serial, 
                             style="Accent.TButton",
                             width=15)
        start_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        cancel_btn = ttk.Button(button_container, text="取消", 
                              command=cancel_serial,
                              width=15)
        cancel_btn.pack(side=tk.LEFT)
        
        # 等待对话框关闭
        dialog.wait_window()
        
        return result["confirmed"], result["serial_number"]
    
    def start_test_simulation(serial_number):
        """开始测试模拟"""
        test_state["running"] = True
        test_state["test_count"] += 1
        
        status_var.set(f"正在测试设备: {serial_number}")
        
        # 清空测试结果
        log_message("=== 开始新的测试 ===", "blue")
        log_message(f"工序: {test_state['selected_process']}")
        log_message(f"序列号: {serial_number}")
        
        for test_id in test_state["test_results"]:
            update_test_item(test_id, "未测试", "")
        
        def simulate_test_progress():
            test_ids = list(test_state["test_results"].keys())
            
            for i, test_id in enumerate(test_ids):
                if not test_state["running"]:
                    break
                
                result = test_state["test_results"][test_id]
                test_name = result["name"]
                
                log_message(f"[{i+1}/{len(test_ids)}] 测试 {test_name}")
                update_test_item(test_id, "测试中", "正在测试...")
                
                time.sleep(0.5)
                
                # 随机生成测试结果
                is_pass = random.random() > 0.2  # 80%通过率
                
                if is_pass:
                    update_test_item(test_id, "通过", "测试通过")
                    log_message(f"{test_name} - 通过 ✅", "green")
                else:
                    update_test_item(test_id, "失败", "测试失败")
                    log_message(f"{test_name} - 失败 ❌", "red")
            
            if test_state["running"]:
                # 统计结果
                passed = sum(1 for r in test_state["test_results"].values() if r["status"] == "通过")
                failed = sum(1 for r in test_state["test_results"].values() if r["status"] == "失败")
                total = len(test_state["test_results"])
                
                log_message(f"设备 {serial_number} 测试完成")
                log_message(f"通过: {passed}, 失败: {failed}, 通过率: {passed/total*100:.1f}%")
                status_var.set(f"测试完成 - 通过率: {passed/total*100:.1f}%")
                
                test_state["running"] = False
                
                # 模拟测试完成后弹出序列号输入
                root.after(2000, lambda: show_serial_input_dialog("输入新序列号", "请输入下一个设备的序列号"))
        
        threading.Thread(target=simulate_test_progress, daemon=True).start()
    
    def manual_start_test():
        """手动开始测试"""
        if test_state["current_serial_number"]:
            start_test_simulation(test_state["current_serial_number"])
        else:
            confirmed, serial_number = show_serial_input_dialog("输入序列号", "请输入设备序列号")
            if confirmed:
                pass  # 测试会在confirm_serial中自动开始
    
    # 控制按钮
    control_frame = ttk.Frame(right_frame)
    control_frame.pack(fill=tk.X)
    
    def stop_test():
        """停止测试"""
        test_state["running"] = False
        log_message("测试已停止")
        status_var.set("测试已停止")
    
    def reset_demo():
        """重置演示"""
        test_state["running"] = False
        test_state["test_count"] = 0
        test_state["current_serial_number"] = None
        log_text.delete(1.0, tk.END)
        status_var.set("准备开始测试...")
        init_test_projects()
        log_message("演示已重置")
    
    def show_input_dialog():
        """显示序列号输入对话框"""
        confirmed, serial_number = show_serial_input_dialog("输入序列号", "请输入设备序列号")
        if not confirmed:
            log_message("演示取消功能：点击取消只关闭弹窗，程序继续运行", "green")
    
    ttk.Button(control_frame, text="开始测试", 
              command=manual_start_test, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="显示输入对话框", 
              command=show_input_dialog, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="停止测试", 
              command=stop_test, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="重置演示", 
              command=reset_demo, width=12).pack(side=tk.RIGHT, padx=5)
    
    # 说明文档
    instruction_frame = ttk.LabelFrame(main_frame, text="测试说明", padding="10")
    instruction_frame.pack(fill=tk.X)
    
    instruction_text = """📋 测试说明:

1. 取消功能测试:
   • 点击"显示输入对话框"弹出序列号输入窗口
   • 点击"取消"按钮，观察是否只关闭弹窗而不退出程序
   • 程序应该继续运行，可以查看测试结果

2. 实际使用场景:
   • 测试完成后弹出序列号输入窗口
   • 如果想查看测试结果或进行调试，点击"取消"
   • 返回主界面查看测试项目状态
   • 可以进行单项测试或手动开始测试

3. 验证要点:
   • 点击"取消"后程序不退出
   • 可以查看和操作测试项目
   • 可以手动点击"开始测试"继续"""
    
    ttk.Label(instruction_frame, text=instruction_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 9)).pack(fill=tk.X)
    
    # 初始化
    init_test_projects()
    log_message("序列号输入弹窗取消功能测试已准备就绪")
    log_message("点击'显示输入对话框'测试取消功能")
    log_message("修改后点击'取消'只关闭弹窗，不退出程序")
    
    print("序列号输入弹窗取消功能测试界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("序列号输入弹窗取消功能测试")
    print("=" * 50)
    
    print("🔧 修改内容:")
    print("  1. 点击'取消'按钮只关闭弹窗，不退出程序")
    print("  2. 返回主界面，可以查看测试结果")
    print("  3. 可以进行单项测试和调试")
    print("  4. 可以手动点击开始测试按钮继续")
    
    # 创建测试界面
    test_cancel_dialog()
    
    print("\n测试完成")
