{"sn": "2222", "timestamp": "2025-07-05T14:14:51.037816", "results": {"usb_test": {"test_name": "USB关键器件测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T14:14:43.488088"}, "can_test": {"test_name": "CAN0测试", "status": "失败", "message": "CAN0 down失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T14:14:44.575434"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "无GPS信号", "timestamp": "2025-07-05T14:14:45.799580"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "AT指令发送失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T14:14:48.946701"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成", "timestamp": "2025-07-05T14:14:50.017153"}, "led_test": {"test_name": "按键灯测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}, "torch_test": {"test_name": "手电筒测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}, "light_sensor_test": {"test_name": "光感测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}, "speaker_test": {"test_name": "喇叭测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}, "wifi_test": {"test_name": "WiFi测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T14:14:43.378344"}}}