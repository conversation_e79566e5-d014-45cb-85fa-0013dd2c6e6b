[2025-07-18 11:41:53] === 开始新的测试 ===
[2025-07-18 11:41:53] 开始测试
[2025-07-18 11:41:53] 工序: 右手臂功能测试
[2025-07-18 11:41:53] 序列号: None
[2025-07-18 11:41:53] 开始运行 右手臂功能测试 工序测试，共 17 个项目
[2025-07-18 11:41:53] 
开始执行: 设备连接状态检测
[2025-07-18 11:41:53] 设备连接测试通过，检测到 1 个设备
[2025-07-18 11:41:53] 设备: ?	device
[2025-07-18 11:41:54] 
开始执行: 3568版本测试
[2025-07-18 11:41:54] 执行3568版本测试...
[2025-07-18 11:41:54] 读取ROM版本号...
[2025-07-18 11:41:54] 执行命令: adb shell uname -a
[2025-07-18 11:41:54] uname命令执行成功
[2025-07-18 11:41:54] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 11:41:54] ✅ 3568版本测试成功
[2025-07-18 11:41:54] ROM版本号: Jul 9 12:01:12
[2025-07-18 11:41:54] ✅ 确认为RK3568平台
[2025-07-18 11:41:55] 
开始执行: USB关键器件检测
[2025-07-18 11:41:55] 执行USB设备检测...
[2025-07-18 11:41:55] 执行命令: adb shell lsusb
[2025-07-18 11:41:55] 设备返回数据:
[2025-07-18 11:41:55] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 11:41:55] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 11:41:55] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 11:41:55] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 11:41:55] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 11:41:55] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 11:41:55] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 11:41:55] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 11:41:55] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 11:41:55] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 11:41:55] 总共解析到 9 个设备
[2025-07-18 11:41:55] ✅ 所有预期的设备ID都已找到
[2025-07-18 11:41:55] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 11:41:55] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 11:41:55] ✅ USB设备检测通过
[2025-07-18 11:41:55] 
开始执行: CAN0测试
[2025-07-18 11:41:55] 执行CAN0测试流程...
[2025-07-18 11:41:55] 执行命令: adb shell ip link set can0 down
[2025-07-18 11:41:55] CAN0已关闭
[2025-07-18 11:41:55] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 11:41:55] CAN0已启动
[2025-07-18 11:41:55] CAN监听线程已启动...
[2025-07-18 11:41:56] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 11:41:57] CAN测试数据已发送，等待监听返回...
[2025-07-18 11:41:57] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 11:41:59] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 11:41:59] 
开始执行: GPS测试
[2025-07-18 11:41:59] 执行GPS测试...
[2025-07-18 11:41:59] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 11:42:09] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 11:42:10] 
开始执行: 4G模组测试
[2025-07-18 11:42:10] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 11:42:10] 监听线程已启动，等待串口数据...
[2025-07-18 11:42:12] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 11:42:12] AT+CCID指令已发送，等待串口返回...
[2025-07-18 11:42:12] 串口输出: AT+CCID
[2025-07-18 11:42:12] 串口输出: 
[2025-07-18 11:42:12] 串口输出: 
[2025-07-18 11:42:12] 串口输出: +CCID: 89860114851012535241
[2025-07-18 11:42:14] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-18 11:42:14] 
开始执行: 按键测试
[2025-07-18 11:42:14] 开始按键测试...
[2025-07-18 11:42:14] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 11:42:17] 原始事件: Event: time 1751328036.431869, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-18 11:42:17] 解析结果: key_code=662, value=1
[2025-07-18 11:42:17] ✓ 检测到档位减(662)按下
[2025-07-18 11:42:17] 原始事件: Event: time 1751328036.631867, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-18 11:42:17] 解析结果: key_code=662, value=0
[2025-07-18 11:42:17] ✓ 档位减(662)测试通过
[2025-07-18 11:42:18] 原始事件: Event: time 1752810139.052864, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-18 11:42:18] 解析结果: key_code=658, value=1
[2025-07-18 11:42:18] ✓ 检测到喇叭键(658)按下
[2025-07-18 11:42:18] 原始事件: Event: time 1752810139.326240, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-18 11:42:18] 解析结果: key_code=658, value=0
[2025-07-18 11:42:18] ✓ 喇叭键(658)测试通过
[2025-07-18 11:42:21] 原始事件: Event: time 1752810142.252733, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-18 11:42:21] 解析结果: key_code=661, value=1
[2025-07-18 11:42:21] ✓ 检测到静音键(661)按下
[2025-07-18 11:42:22] 原始事件: Event: time 1752810142.456092, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-18 11:42:22] 解析结果: key_code=661, value=0
[2025-07-18 11:42:22] ✓ 静音键(661)测试通过
[2025-07-18 11:42:22] 原始事件: Event: time 1752810143.276121, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 11:42:22] 解析结果: key_code=663, value=1
[2025-07-18 11:42:22] ✓ 检测到语音键(663)按下
[2025-07-18 11:42:23] 原始事件: Event: time 1752810143.452753, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 11:42:23] 解析结果: key_code=663, value=0
[2025-07-18 11:42:23] ✓ 语音键(663)测试通过
[2025-07-18 11:42:23] 原始事件: Event: time 1752810144.326078, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-18 11:42:23] 解析结果: key_code=657, value=1
[2025-07-18 11:42:23] ✓ 检测到SOS键(657)按下
[2025-07-18 11:42:24] 原始事件: Event: time 1752810144.552740, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-18 11:42:24] 解析结果: key_code=657, value=0
[2025-07-18 11:42:24] ✓ SOS键(657)测试通过
[2025-07-18 11:42:25] 原始事件: Event: time 1752810145.432789, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-18 11:42:25] 解析结果: key_code=656, value=1
[2025-07-18 11:42:25] ✓ 检测到锁定键(656)按下
[2025-07-18 11:42:25] 原始事件: Event: time 1752810145.576075, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-18 11:42:25] 解析结果: key_code=656, value=0
[2025-07-18 11:42:25] ✓ 锁定键(656)测试通过
[2025-07-18 11:42:26] 原始事件: Event: time 1752810147.429526, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-18 11:42:26] 解析结果: key_code=659, value=1
[2025-07-18 11:42:26] ✓ 检测到档位加(659)按下
[2025-07-18 11:42:27] 原始事件: Event: time 1752810147.652742, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-18 11:42:27] 解析结果: key_code=659, value=0
[2025-07-18 11:42:27] ✓ 档位加(659)测试通过
[2025-07-18 11:42:28] 原始事件: Event: time 1752810149.029439, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-18 11:42:28] 解析结果: key_code=660, value=1
[2025-07-18 11:42:28] ✓ 检测到智驾键(660)按下
[2025-07-18 11:42:28] 原始事件: Event: time 1752810149.279559, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-18 11:42:28] 解析结果: key_code=660, value=0
[2025-07-18 11:42:28] ✓ 智驾键(660)测试通过
[2025-07-18 11:42:29] 按键测试完成 - 检测到8个按键
[2025-07-18 11:42:31] 
开始执行: 按键灯测试
[2025-07-18 11:42:31] 开始LED背光灯测试...
[2025-07-18 11:42:31] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:42:31] LED控制命令执行成功
[2025-07-18 11:42:31] 🔧 显示确认对话框: LED测试确认
[2025-07-18 11:42:32] 🔧 对话框窗口已创建
[2025-07-18 11:42:32] 🔧 '是'按钮已创建
[2025-07-18 11:42:32] 🔧 '否'按钮已创建
[2025-07-18 11:42:32] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 11:42:35] 👤 用户选择: 是 (测试通过)
[2025-07-18 11:42:35] 🔧 对话框关闭，用户响应: yes
[2025-07-18 11:42:35] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:42:35] LED灯已关闭
[2025-07-18 11:42:35] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-18 11:42:35] 
开始执行: 手电筒测试
[2025-07-18 11:42:35] 开始手电筒LED测试...
[2025-07-18 11:42:35] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 11:42:35] 手电筒控制命令执行成功
[2025-07-18 11:42:35] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-18 11:42:35] 🔧 对话框窗口已创建
[2025-07-18 11:42:35] 🔧 '是'按钮已创建
[2025-07-18 11:42:35] 🔧 '否'按钮已创建
[2025-07-18 11:42:35] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 11:42:36] 👤 用户选择: 是 (测试通过)
[2025-07-18 11:42:36] 🔧 对话框关闭，用户响应: yes
[2025-07-18 11:42:36] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-18 11:42:36] 手电筒已关闭
[2025-07-18 11:42:36] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-18 11:42:37] 
开始执行: 摇杆使能测试
[2025-07-18 11:42:37] 执行摇杆测试...
[2025-07-18 11:42:37] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 11:42:37] 命令执行成功
[2025-07-18 11:42:37] 返回数据: 255
[2025-07-18 11:42:37] 摇杆测试通过，值: 255
[2025-07-18 11:42:37] 
开始执行: 前摄像头测试
[2025-07-18 11:42:37] 开始执行前摄像头测试...
[2025-07-18 11:42:38] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 11:42:38] 命令执行成功，返回: 无输出
[2025-07-18 11:42:38] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-18 11:42:39] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.322041449
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-18 11:42:39] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-18 11:42:39] 拉取命令执行成功
[2025-07-18 11:42:39] 返回数据: [ 38%] /data/camera/cam0_3840x2160.jpg
[ 77%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 9.3 MB/s (168862 bytes in 0.017s)
[2025-07-18 11:42:39] 图片已保存: cam0_3840x2160.jpg
[2025-07-18 11:42:40] 用户确认结果: 通过
[2025-07-18 11:42:41] 
开始执行: 光感测试
[2025-07-18 11:42:41] 执行光感测试...
[2025-07-18 11:42:41] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 11:42:44] 光感测试完成 - 检测到数值变化
[2025-07-18 11:42:44] 数值从 5 变化到 0
[2025-07-18 11:42:45] 
开始执行: 回充摄像头测试
[2025-07-18 11:42:45] 开始执行回充摄像头测试...
[2025-07-18 11:42:45] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 11:42:45] 命令执行成功，返回: 无输出
[2025-07-18 11:42:45] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-18 11:42:46] 命令执行成功，返回: 无输出
[2025-07-18 11:42:46] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-18 11:42:46] 拉取命令执行成功
[2025-07-18 11:42:46] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 39.0 MB/s (42360 bytes in 0.001s)
[2025-07-18 11:42:46] 图片已保存: output.jpg
[2025-07-18 11:42:47] 用户确认结果: 通过
[2025-07-18 11:42:47] 
开始执行: 喇叭测试
[2025-07-18 11:42:47] 执行喇叭测试...
[2025-07-18 11:42:47] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 11:43:01] 命令执行成功
[2025-07-18 11:43:01] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-18 11:43:01] 音频播放完成
[2025-07-18 11:43:02] 
开始执行: 蓝牙测试
[2025-07-18 11:43:02] 执行蓝牙测试...
[2025-07-18 11:43:02] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 11:43:02] 执行命令: adb shell bluetoothctl show
[2025-07-18 11:43:02] 命令执行成功
[2025-07-18 11:43:02] 返回数据:
[2025-07-18 11:43:02]   Controller 24:21:5E:C0:30:4A (public)
[2025-07-18 11:43:02]   Name: CMG-1
[2025-07-18 11:43:02]   Alias: CMG-1
[2025-07-18 11:43:02]   Class: 0x006c0000 (7077888)
[2025-07-18 11:43:02]   Powered: yes
[2025-07-18 11:43:02]   PowerState: on
[2025-07-18 11:43:02]   Discoverable: no
[2025-07-18 11:43:02]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-18 11:43:02]   Pairable: yes
[2025-07-18 11:43:02]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-18 11:43:02]   Modalias: usb:v1D6Bp0246d0544
[2025-07-18 11:43:02]   Discovering: no
[2025-07-18 11:43:02]   Roles: central
[2025-07-18 11:43:02]   Roles: peripheral
[2025-07-18 11:43:02]   Advertising Features:
[2025-07-18 11:43:02]   ActiveInstances: 0x00 (0)
[2025-07-18 11:43:02]   SupportedInstances: 0x10 (16)
[2025-07-18 11:43:02]   SupportedIncludes: tx-power
[2025-07-18 11:43:02]   SupportedIncludes: appearance
[2025-07-18 11:43:02]   SupportedIncludes: local-name
[2025-07-18 11:43:02]   SupportedSecondaryChannels: 1M
[2025-07-18 11:43:02]   SupportedSecondaryChannels: 2M
[2025-07-18 11:43:02]   SupportedSecondaryChannels: Coded
[2025-07-18 11:43:02]   SupportedCapabilities Key: MaxAdvLen
[2025-07-18 11:43:02]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 11:43:02]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-18 11:43:02]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 11:43:02]   SupportedFeatures: CanSetTxPower
[2025-07-18 11:43:02]   SupportedFeatures: HardwareOffload
[2025-07-18 11:43:02]   Advertisement Monitor Features:
[2025-07-18 11:43:02]   SupportedMonitorTypes: or_patterns
[2025-07-18 11:43:02] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-18 11:43:02] 蓝牙控制器MAC地址: 24:21:5E:C0:30:4A
[2025-07-18 11:43:03] 
开始执行: 4G网络测试
[2025-07-18 11:43:03] 执行4G网络测试...
[2025-07-18 11:43:03] 第一步：关闭WiFi网络...
[2025-07-18 11:43:03] WiFi已关闭，等待4G网络连接...
[2025-07-18 11:43:06] 第二步：使用4G网络进行ping测试...
[2025-07-18 11:43:06] ping目标: www.baidu.com
[2025-07-18 11:43:06] 测试时长: 10秒
[2025-07-18 11:43:16] ping测试输出:
[2025-07-18 11:43:16]   PING www.baidu.com(2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207)) 56 data bytes
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=1 ttl=52 time=233 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=2 ttl=52 time=145 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=3 ttl=52 time=67.4 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=4 ttl=52 time=391 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=5 ttl=52 time=107 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=6 ttl=52 time=215 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=7 ttl=52 time=240 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=8 ttl=52 time=102 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=9 ttl=52 time=127 ms
[2025-07-18 11:43:16]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=10 ttl=52 time=91.1 ms
[2025-07-18 11:43:16]   --- www.baidu.com ping statistics ---
[2025-07-18 11:43:16]   10 packets transmitted, 10 received, 0% packet loss, time 9002ms
[2025-07-18 11:43:16]   rtt min/avg/max/mdev = 67.409/171.878/390.547/93.372 ms
[2025-07-18 11:43:16] 4G网络延时统计:
[2025-07-18 11:43:16]   成功ping包: 10 个
[2025-07-18 11:43:16]   平均延时: 171.8 ms
[2025-07-18 11:43:16]   最小延时: 67.4 ms
[2025-07-18 11:43:16]   最大延时: 391.0 ms
[2025-07-18 11:43:16] 4G网络测试通过
[2025-07-18 11:43:16] 第三步：重新打开WiFi网络...
[2025-07-18 11:43:16] WiFi已重新打开
[2025-07-18 11:43:19] 
开始执行: WiFi测试
[2025-07-18 11:43:19] 执行WiFi测试...
[2025-07-18 11:43:19] 第一步：关闭4G网络...
[2025-07-18 11:43:19] 4G网络已关闭，等待网络切换...
[2025-07-18 11:43:19] WiFi测试出错: cannot access local variable 'time' where it is not associated with a value
[2025-07-18 11:43:19] 第三步：重新打开4G网络...
[2025-07-18 11:43:19] 4G网络已重新打开
[2025-07-18 11:43:19] 重新打开4G网络时出错: cannot access local variable 'time' where it is not associated with a value
[2025-07-18 11:43:19] 
测试完成 - 通过率: 15/17
[2025-07-18 11:43:19] ❌ 存在测试失败项！

