[2025-07-14 10:01:17] 开始测试 - SN: 2222233
[2025-07-14 10:01:17] 
开始执行: 设备连接状态检测
[2025-07-14 10:01:17] 设备连接测试通过，检测到 1 个设备
[2025-07-14 10:01:17] 设备: ?	device
[2025-07-14 10:01:17] 
开始执行: USB关键器件检测
[2025-07-14 10:01:17] 执行USB设备检测...
[2025-07-14 10:01:17] 执行命令: adb shell lsusb
[2025-07-14 10:01:17] 设备返回数据:
[2025-07-14 10:01:17] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-14 10:01:17] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-14 10:01:17] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-14 10:01:17] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-14 10:01:17] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-14 10:01:17] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-14 10:01:17] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-14 10:01:17] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-14 10:01:18] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-14 10:01:18] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-14 10:01:18] 总共解析到 9 个设备
[2025-07-14 10:01:18] ✅ 所有预期的设备ID都已找到
[2025-07-14 10:01:18] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-14 10:01:18] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-14 10:01:18] ✅ USB设备检测通过
[2025-07-14 10:01:18] 
开始执行: CAN0测试
[2025-07-14 10:01:18] 执行CAN0测试流程...
[2025-07-14 10:01:18] 执行命令: adb shell ip link set can0 down
[2025-07-14 10:01:18] CAN0已关闭
[2025-07-14 10:01:18] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-14 10:01:18] CAN0已启动
[2025-07-14 10:01:18] CAN监听线程已启动...
[2025-07-14 10:01:19] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-14 10:01:19] CAN测试数据已发送，等待监听返回...
[2025-07-14 10:01:19] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-14 10:01:21] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-14 10:01:22] 
开始执行: GPS测试
[2025-07-14 10:01:22] 执行GPS测试...
[2025-07-14 10:01:22] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-14 10:01:32] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-14 10:01:32] 
开始执行: 4G模组测试
[2025-07-14 10:01:32] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-14 10:01:32] 监听线程已启动，等待串口数据...
[2025-07-14 10:01:34] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-14 10:01:34] AT+CCID指令已发送，等待串口返回...
[2025-07-14 10:01:34] 串口输出: AT+CCID
[2025-07-14 10:01:34] 串口输出: 
[2025-07-14 10:01:34] 串口输出: 
[2025-07-14 10:01:34] 串口输出: +CME ERROR: 13
[2025-07-14 10:01:34] 串口输出: 
[2025-07-14 10:01:34] 串口输出: AT+C
[2025-07-14 10:01:34] 串口输出: 
[2025-07-14 10:01:34] 串口输出: 
[2025-07-14 10:01:34] 串口输出: +CME ERROR: 58
[2025-07-14 10:01:34] 串口输出: 
[2025-07-14 10:01:46] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-14 10:01:47] 
开始执行: 按键测试
[2025-07-14 10:01:47] 开始按键测试...
[2025-07-14 10:01:47] 执行命令: adb shell evtest /dev/input/event5
[2025-07-14 10:02:31] 原始事件: Event: time 1751299277.460942, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-14 10:02:31] 解析结果: key_code=662, value=1
[2025-07-14 10:02:31] ✓ 检测到档位减(662)按下
[2025-07-14 10:02:31] 原始事件: Event: time 1751299277.634352, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-14 10:02:31] 解析结果: key_code=662, value=0
[2025-07-14 10:02:31] ✓ 档位减(662)测试通过
[2025-07-14 10:02:31] 原始事件: Event: time 1751299278.114294, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-14 10:02:31] 解析结果: key_code=658, value=1
[2025-07-14 10:02:31] ✓ 检测到喇叭键(658)按下
[2025-07-14 10:02:32] 原始事件: Event: time 1751299278.284428, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-14 10:02:32] 解析结果: key_code=658, value=0
[2025-07-14 10:02:32] ✓ 喇叭键(658)测试通过
[2025-07-14 10:02:33] 原始事件: Event: time 1751299280.084280, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-14 10:02:33] 解析结果: key_code=663, value=1
[2025-07-14 10:02:33] ✓ 检测到语音键(663)按下
[2025-07-14 10:02:33] 原始事件: Event: time 1751299280.110943, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-14 10:02:33] 解析结果: key_code=661, value=1
[2025-07-14 10:02:33] ✓ 检测到静音键(661)按下
[2025-07-14 10:02:34] 原始事件: Event: time 1751299280.284342, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-14 10:02:34] 解析结果: key_code=663, value=0
[2025-07-14 10:02:34] ✓ 语音键(663)测试通过
[2025-07-14 10:02:34] 原始事件: Event: time 1751299280.314352, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-14 10:02:34] 解析结果: key_code=661, value=0
[2025-07-14 10:02:34] ✓ 静音键(661)测试通过
[2025-07-14 10:02:36] 原始事件: Event: time 1751299282.584212, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-14 10:02:36] 解析结果: key_code=657, value=1
[2025-07-14 10:02:36] ✓ 检测到SOS键(657)按下
[2025-07-14 10:02:36] 原始事件: Event: time 1751299282.764425, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-14 10:02:36] 解析结果: key_code=657, value=0
[2025-07-14 10:02:36] ✓ SOS键(657)测试通过
[2025-07-14 10:02:36] 原始事件: Event: time 1751299283.184284, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-14 10:02:36] 解析结果: key_code=656, value=1
[2025-07-14 10:02:36] ✓ 检测到锁定键(656)按下
[2025-07-14 10:02:37] 原始事件: Event: time 1751299283.360924, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-14 10:02:37] 解析结果: key_code=656, value=0
[2025-07-14 10:02:37] ✓ 锁定键(656)测试通过
[2025-07-14 10:02:37] 原始事件: Event: time 1751299284.034281, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-14 10:02:37] 解析结果: key_code=660, value=1
[2025-07-14 10:02:37] ✓ 检测到智驾键(660)按下
[2025-07-14 10:02:37] 原始事件: Event: time 1751299284.210945, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-14 10:02:37] 解析结果: key_code=660, value=0
[2025-07-14 10:02:37] ✓ 智驾键(660)测试通过
[2025-07-14 10:02:38] 原始事件: Event: time 1751299285.134410, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-14 10:02:38] 解析结果: key_code=659, value=1
[2025-07-14 10:02:38] ✓ 检测到档位加(659)按下
[2025-07-14 10:02:39] 原始事件: Event: time 1751299285.310965, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-14 10:02:39] 解析结果: key_code=659, value=0
[2025-07-14 10:02:39] ✓ 档位加(659)测试通过
[2025-07-14 10:02:39] 按键测试完成 - 检测到8个按键
[2025-07-14 10:02:41] 
开始执行: 按键灯测试
[2025-07-14 10:02:41] 开始LED背光灯测试...
[2025-07-14 10:02:41] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-14 10:02:41] LED控制命令执行成功
[2025-07-14 10:02:56] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-14 10:02:57] LED灯已关闭
[2025-07-14 10:02:57] LED测试失败 - 背光灯未正常点亮
[2025-07-14 10:02:57] 
开始执行: 手电筒测试
[2025-07-14 10:02:57] 开始手电筒LED测试...
[2025-07-14 10:02:57] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-14 10:02:57] 手电筒控制命令执行成功
[2025-07-14 10:03:03] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-14 10:03:03] 手电筒已关闭
[2025-07-14 10:03:03] 手电筒测试失败 - 手电筒未正常点亮
[2025-07-14 10:03:03] 
开始执行: 摇杆使能测试
[2025-07-14 10:03:03] 执行摇杆测试...
[2025-07-14 10:03:03] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-14 10:03:03] 命令执行成功
[2025-07-14 10:03:03] 返回数据: 255
[2025-07-14 10:03:03] 摇杆测试通过，值: 255
[2025-07-14 10:03:03] 
开始执行: 前摄像头测试
[2025-07-14 10:03:03] 开始执行前摄像头测试...
[2025-07-14 10:03:04] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-14 10:03:04] 命令执行成功，返回: 无输出
[2025-07-14 10:03:04] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-14 10:03:04] 命令执行失败: ERROR: from element /GstPipeline:pipeline0/GstV4l2Src:v4l2src0: Could not open device '/dev/video0' for reading and writing.
Additional debug info:
../sys/v4l2/v4l2_calls.c(622): gst_v4l2_open (): /GstPipeline:pipeline0/GstV4l2Src:v4l2src0:
system error: Input/output error
ERROR: pipeline doesn't want to preroll.
Failed to set pipeline to PAUSED.

[2025-07-14 10:03:05] 
开始执行: 光感测试
[2025-07-14 10:03:05] 执行光感测试...
[2025-07-14 10:03:05] 执行命令: adb shell evtest /dev/input/event1
[2025-07-14 10:03:19] 光感测试完成 - 检测到数值变化
[2025-07-14 10:03:19] 数值从 3 变化到 4
[2025-07-14 10:03:19] 
开始执行: 回充摄像头测试
[2025-07-14 10:03:19] 开始执行回充摄像头测试...
[2025-07-14 10:03:19] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-14 10:03:19] 命令执行成功，返回: 无输出
[2025-07-14 10:03:19] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-14 10:03:20] 命令执行成功，返回: 无输出
[2025-07-14 10:03:20] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-14 10:03:20] 拉取命令执行成功
[2025-07-14 10:03:20] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 4.0 MB/s (18688 bytes in 0.004s)
[2025-07-14 10:03:20] 图片已保存: output.jpg
[2025-07-14 10:03:22] 用户确认结果: 通过
[2025-07-14 10:03:22] 
开始执行: 喇叭测试
[2025-07-14 10:03:22] 执行喇叭测试...
[2025-07-14 10:03:22] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-14 10:03:36] 命令执行成功
[2025-07-14 10:03:36] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-14 10:03:36] 音频播放完成
[2025-07-14 10:03:36] 
开始执行: 蓝牙测试
[2025-07-14 10:03:36] 执行蓝牙测试...
[2025-07-14 10:03:36] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-14 10:03:36] 执行命令: adb shell bluetoothctl show
[2025-07-14 10:03:37] 命令执行成功
[2025-07-14 10:03:37] 返回数据:
[2025-07-14 10:03:37]   Controller 40:55:48:07:E4:2D (public)
[2025-07-14 10:03:37]   Name: CMG-1
[2025-07-14 10:03:37]   Alias: CMG-1
[2025-07-14 10:03:37]   Class: 0x006c0000 (7077888)
[2025-07-14 10:03:37]   Powered: yes
[2025-07-14 10:03:37]   PowerState: on
[2025-07-14 10:03:37]   Discoverable: no
[2025-07-14 10:03:37]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-14 10:03:37]   Pairable: yes
[2025-07-14 10:03:37]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-14 10:03:37]   Modalias: usb:v1D6Bp0246d0544
[2025-07-14 10:03:37]   Discovering: no
[2025-07-14 10:03:37]   Roles: central
[2025-07-14 10:03:37]   Roles: peripheral
[2025-07-14 10:03:37]   Advertising Features:
[2025-07-14 10:03:37]   ActiveInstances: 0x00 (0)
[2025-07-14 10:03:37]   SupportedInstances: 0x10 (16)
[2025-07-14 10:03:37]   SupportedIncludes: tx-power
[2025-07-14 10:03:37]   SupportedIncludes: appearance
[2025-07-14 10:03:37]   SupportedIncludes: local-name
[2025-07-14 10:03:37]   SupportedSecondaryChannels: 1M
[2025-07-14 10:03:37]   SupportedSecondaryChannels: 2M
[2025-07-14 10:03:37]   SupportedSecondaryChannels: Coded
[2025-07-14 10:03:37]   SupportedCapabilities Key: MaxAdvLen
[2025-07-14 10:03:37]   SupportedCapabilities Value: 0x1f (31)
[2025-07-14 10:03:37]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-14 10:03:37]   SupportedCapabilities Value: 0x1f (31)
[2025-07-14 10:03:37]   SupportedFeatures: CanSetTxPower
[2025-07-14 10:03:37]   SupportedFeatures: HardwareOffload
[2025-07-14 10:03:37]   Advertisement Monitor Features:
[2025-07-14 10:03:37]   SupportedMonitorTypes: or_patterns
[2025-07-14 10:03:37] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-14 10:03:37] 蓝牙控制器MAC地址: 40:55:48:07:E4:2D
[2025-07-14 10:03:37] 
开始执行: WiFi测试
[2025-07-14 10:03:37] 执行WiFi测试...
[2025-07-14 10:03:37] 开始网络发包延时测试...
[2025-07-14 10:03:37] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-14 10:03:37] 正在进行10秒钟的网络延时测试...
[2025-07-14 10:03:37] ❌ ping命令执行失败: ping: www.baidu.com: Temporary failure in name resolution

[2025-07-14 10:03:37] 
测试完成 - 通过率: 9/15
[2025-07-14 10:03:37] ❌ 存在测试失败项！
[2025-07-14 10:03:37] 测试记录已保存: records/2222233_20250714_100337.json
[2025-07-14 10:03:37] 测试日志已保存: records/2222233_20250714_100337.log

