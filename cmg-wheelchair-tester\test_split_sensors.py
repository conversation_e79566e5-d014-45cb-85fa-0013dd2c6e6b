#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试拆分后的传感器FPS测试功能
"""

import subprocess
import re
import time

def test_adb_connection():
    """测试ADB连接"""
    print("=== 测试ADB连接 ===")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        print(f"ADB devices返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("ADB连接正常")
            devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            connected_devices = [line for line in devices if 'device' in line]
            
            if connected_devices:
                print(f"已连接设备: {len(connected_devices)}个")
                for device in connected_devices:
                    print(f"  {device}")
                return True
            else:
                print("❌ 没有连接的设备")
                return False
        else:
            print(f"❌ ADB连接失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ ADB连接测试出错: {str(e)}")
        return False

def parse_single_sensor_fps_data(log_content, target_sensor):
    """解析单个传感器的FPS数据"""
    try:
        import re
        
        print(f"开始解析{target_sensor}传感器FPS数据...")
        
        # 查找包含"sensor fps:"关键字段的行
        lines = log_content.split('\n')
        sensor_values = []
        
        # 找到所有包含"sensor fps:"的行及其后续的FPS数据
        for i, line in enumerate(lines):
            if "sensor fps:" in line:
                # 从下一行开始查找目标传感器的FPS数据，最多查找10行
                for j in range(i + 1, min(i + 11, len(lines))):
                    fps_line = lines[j].strip()
                    if not fps_line:
                        continue
                    
                    # 匹配目标传感器的FPS数据格式: FPS Accel: 184
                    fps_pattern = f"FPS\\s+{re.escape(target_sensor)}:\\s+(\\d+)"
                    fps_match = re.match(fps_pattern, fps_line)
                    if fps_match:
                        fps_value = int(fps_match.group(1))
                        sensor_values.append(fps_value)
                        break  # 找到目标传感器数据后跳出内层循环
                    elif fps_line.startswith('FPS '):
                        # 如果遇到其他传感器的FPS行，继续查找
                        continue
                    else:
                        # 如果遇到非FPS行，结束这个section
                        break
        
        print(f"找到 {len(sensor_values)} 个{target_sensor}传感器FPS数据点")
        
        if not sensor_values:
            return None
        
        # 取最近5条数据（如果不足5条则取全部）
        recent_values = sensor_values[-5:]
        print(f"使用最近 {len(recent_values)} 条数据计算平均值: {recent_values}")
        
        # 计算平均值
        avg_fps = sum(recent_values) / len(recent_values)
        print(f"{target_sensor}传感器平均FPS: {avg_fps:.1f}")
        
        return avg_fps
        
    except Exception as e:
        print(f"解析{target_sensor}传感器FPS数据出错: {str(e)}")
        return None

def test_single_sensor_parsing():
    """测试单个传感器解析功能"""
    print("\n=== 测试单个传感器解析功能 ===")
    
    # 模拟传感器日志数据
    sample_log = """
I0701 00:23:05.936022(1404319) sensor_service.cc:223] sensor fps:
FPS Accel: 180
FPS Gyro: 182
FPS Laser: 3900
FPS Laser2: 3850
FPS Odom: 98

I0701 00:23:08.936022(1404319) sensor_service.cc:223] sensor fps:
FPS Accel: 184
FPS Gyro: 186
FPS Laser: 3973
FPS Laser2: 3959
FPS Odom: 100

I0701 00:23:11.936022(1404319) sensor_service.cc:223] sensor fps:
FPS Accel: 188
FPS Gyro: 190
FPS Laser: 4000
FPS Laser2: 3980
FPS Odom: 102
"""
    
    # 测试5个传感器
    sensors = ["Accel", "Gyro", "Laser", "Laser2", "Odom"]
    
    print("示例日志数据:")
    print(sample_log)
    
    results = {}
    for sensor in sensors:
        print(f"\n--- 测试{sensor}传感器 ---")
        avg_fps = parse_single_sensor_fps_data(sample_log, sensor)
        
        if avg_fps is not None:
            print(f"✅ {sensor}传感器解析成功: {avg_fps:.1f} FPS")
            results[sensor] = avg_fps
        else:
            print(f"❌ {sensor}传感器解析失败")
            results[sensor] = None
    
    print(f"\n=== 解析结果汇总 ===")
    for sensor, fps in results.items():
        if fps is not None:
            print(f"  {sensor}: {fps:.1f} FPS")
        else:
            print(f"  {sensor}: 解析失败")
    
    return all(fps is not None for fps in results.values())

def test_real_sensor_commands():
    """测试真实的传感器命令"""
    print("\n=== 测试真实传感器命令 ===")
    
    sensors = ["Accel", "Gyro", "Laser", "Laser2", "Odom"]
    results = {}
    
    try:
        # 执行cat命令读取传感器日志
        command = "cat /sdcard/lmv/normal_logs/sensor/sensor_newest"
        print(f"执行命令: adb shell {command}")
        
        result = subprocess.run(['adb', 'shell', command], 
                              capture_output=True, text=True, timeout=15)
        
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print("✅ 传感器日志读取成功")
            print(f"日志长度: {len(output)} 字符")
            
            # 测试每个传感器
            for sensor in sensors:
                print(f"\n--- 测试{sensor}传感器 ---")
                avg_fps = parse_single_sensor_fps_data(output, sensor)
                
                if avg_fps is not None:
                    print(f"✅ {sensor}传感器测试成功: {avg_fps:.1f} FPS")
                    results[sensor] = f"{avg_fps:.1f} FPS"
                else:
                    print(f"❌ {sensor}传感器测试失败")
                    results[sensor] = "未找到FPS数据"
            
            return True, results
        else:
            print(f"❌ 传感器日志读取失败: {result.stderr}")
            return False, {}
            
    except Exception as e:
        print(f"❌ 传感器命令测试出错: {str(e)}")
        return False, {}

def test_complete_split_sensor_flow():
    """测试完整的拆分传感器测试流程"""
    print("\n=== 测试完整的拆分传感器测试流程 ===")
    
    print("步骤1: 测试单个传感器解析算法...")
    parsing_success = test_single_sensor_parsing()
    
    if not parsing_success:
        print("❌ 单个传感器解析算法测试失败")
        return False
    
    print("\n步骤2: 测试真实设备...")
    sensor_success, results = test_real_sensor_commands()
    
    if sensor_success:
        print(f"\n🎉 拆分传感器测试成功！")
        print("各传感器测试结果:")
        for sensor, result in results.items():
            print(f"  {sensor}: {result}")
        return True
    else:
        print(f"\n❌ 拆分传感器测试失败")
        return False

def display_test_project_structure():
    """显示测试项目结构"""
    print("\n=== 拆分后的测试项目结构 ===")
    
    projects = [
        {"id": "accel_test", "name": "Accel", "description": "加速度传感器FPS测试"},
        {"id": "gyro_test", "name": "Gyro", "description": "陀螺仪传感器FPS测试"},
        {"id": "laser_test", "name": "Laser", "description": "激光传感器FPS测试"},
        {"id": "laser2_test", "name": "Laser2", "description": "激光传感器2 FPS测试"},
        {"id": "odom_test", "name": "Odom", "description": "里程计传感器FPS测试"}
    ]
    
    for i, project in enumerate(projects, 17):  # 从第17个测试项目开始
        print(f"{i}. **{project['name']}** - {project['description']}")
    
    print("\n每个传感器测试项目都是独立的，显示格式如：")
    print("  Accel: 184.0 FPS")
    print("  Gyro: 186.0 FPS")
    print("  Laser: 3973.0 FPS")
    print("  Laser2: 3959.0 FPS")
    print("  Odom: 100.0 FPS")

if __name__ == "__main__":
    print("拆分传感器FPS测试功能验证")
    print("=" * 50)
    
    # 显示测试项目结构
    display_test_project_structure()
    
    # 测试ADB连接
    if not test_adb_connection():
        print("\n❌ ADB连接失败，无法进行传感器测试")
        exit(1)
    
    # 测试完整的拆分传感器测试流程
    success = test_complete_split_sensor_flow()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 拆分传感器FPS测试功能验证成功！")
    else:
        print("❌ 拆分传感器FPS测试功能验证失败")
    
    print("\n测试完成")
