// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 获取所有输入元素
    const inputs = document.querySelectorAll('input[type="radio"], input[type="checkbox"]');
    
    // 为每个输入元素添加change事件监听器
    inputs.forEach(input => {
        input.addEventListener('change', updateSummary);
    });

    // 为保存按钮添加点击事件
    const saveButton = document.querySelector('.btn-save');
    saveButton.addEventListener('click', saveConfiguration);
});

// 更新汇总信息
function updateSummary() {
    // 更新产品外观
    const appearanceInput = document.querySelector('input[name="appearance"]:checked');
    document.getElementById('appearance-summary').textContent = 
        appearanceInput.nextElementSibling.textContent;

    // 更新功能模块
    const modulesInputs = document.querySelectorAll('input[name="modules"]:checked');
    const modulesList = document.getElementById('modules-summary');
    modulesList.innerHTML = '';
    modulesInputs.forEach(input => {
        const li = document.createElement('li');
        li.textContent = input.nextElementSibling.textContent;
        modulesList.appendChild(li);
    });

    // 更新电池型号
    const batteryInput = document.querySelector('input[name="battery"]:checked');
    document.getElementById('battery-summary').textContent = 
        batteryInput.nextElementSibling.textContent;

    // 更新充电方式
    const chargingInput = document.querySelector('input[name="charging"]:checked');
    document.getElementById('charging-summary').textContent = 
        chargingInput.nextElementSibling.textContent;

    // 更新出货地
    const shippingInput = document.querySelector('input[name="shipping"]:checked');
    document.getElementById('shipping-summary').textContent = 
        shippingInput.nextElementSibling.textContent;

    // 更新包装方式
    const packagingInput = document.querySelector('input[name="packaging"]:checked');
    document.getElementById('packaging-summary').textContent = 
        packagingInput.nextElementSibling.textContent;
}

// 保存配置
function saveConfiguration() {
    // 收集所有选中的选项
    const config = {
        appearance: document.querySelector('input[name="appearance"]:checked').value,
        modules: Array.from(document.querySelectorAll('input[name="modules"]:checked')).map(input => input.value),
        battery: document.querySelector('input[name="battery"]:checked').value,
        charging: document.querySelector('input[name="charging"]:checked').value,
        shipping: document.querySelector('input[name="shipping"]:checked').value,
        packaging: document.querySelector('input[name="packaging"]:checked').value
    };

    // 这里可以添加保存配置的逻辑
    // 例如：发送到服务器、保存到本地存储等
    console.log('保存配置:', config);
    
    // 显示保存成功提示
    alert('配置已保存！');
} 