#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的对话框按钮测试
"""

import tkinter as tk
from tkinter import ttk

def create_simple_dialog():
    """创建简单的确认对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 创建对话框
    dialog = tk.Toplevel(root)
    dialog.title("测试确认对话框")
    dialog.geometry("500x300")
    dialog.resizable(False, False)
    
    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
    y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
    dialog.geometry(f"+{x}+{y}")
    
    result = {"answer": None}
    
    # 主框架
    main_frame = ttk.Frame(dialog, padding="30")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(
        main_frame, 
        text="LED测试确认", 
        font=("Arial", 16, "bold")
    )
    title_label.pack(pady=(0, 20))
    
    # 消息文本
    message_label = ttk.Label(
        main_frame, 
        text="请确认：\n1. 背光灯是否已点亮？\n2. 背光灯颜色是否为蓝色？", 
        justify=tk.CENTER,
        font=("Arial", 12)
    )
    message_label.pack(pady=(0, 20))
    
    # 倒计时显示
    countdown_var = tk.StringVar(value="剩余时间: 60秒")
    countdown_label = ttk.Label(
        main_frame, 
        textvariable=countdown_var, 
        foreground="red",
        font=("Arial", 12, "bold")
    )
    countdown_label.pack(pady=(0, 30))
    
    # 按钮框架
    button_frame = ttk.Frame(main_frame)
    button_frame.pack()
    
    def on_yes():
        result["answer"] = "yes"
        print("用户选择: 是")
        dialog.destroy()
    
    def on_no():
        result["answer"] = "no"
        print("用户选择: 否")
        dialog.destroy()
    
    def on_timeout():
        result["answer"] = "timeout"
        print("对话框超时")
        dialog.destroy()
    
    # 创建按钮
    yes_button = ttk.Button(
        button_frame, 
        text="是 (通过)", 
        command=on_yes,
        width=15
    )
    yes_button.pack(side=tk.LEFT, padx=(0, 20))
    
    no_button = ttk.Button(
        button_frame, 
        text="否 (失败)", 
        command=on_no,
        width=15
    )
    no_button.pack(side=tk.LEFT)
    
    print("按钮已创建:")
    print(f"- 是按钮: {yes_button}")
    print(f"- 否按钮: {no_button}")
    
    # 倒计时更新
    remaining_time = [10]  # 10秒倒计时
    
    def update_countdown():
        if remaining_time[0] > 0:
            countdown_var.set(f"剩余时间: {remaining_time[0]}秒")
            remaining_time[0] -= 1
            dialog.after(1000, update_countdown)
        else:
            on_timeout()
    
    # 开始倒计时
    update_countdown()
    
    # 绑定键盘事件
    dialog.bind("<Return>", lambda e: on_yes())
    dialog.bind("<Escape>", lambda e: on_no())
    
    # 强制更新显示
    dialog.update()
    
    print("对话框已显示，等待用户响应...")
    
    # 等待窗口关闭
    dialog.wait_window()
    
    print(f"对话框关闭，结果: {result['answer']}")
    
    root.destroy()
    return result["answer"]

def test_button_visibility():
    """测试按钮可见性"""
    print("=== 测试按钮可见性 ===")
    
    # 创建主窗口
    root = tk.Tk()
    root.title("按钮可见性测试")
    root.geometry("400x300")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="按钮可见性测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    def show_dialog():
        result = create_simple_dialog()
        result_label.config(text=f"结果: {result}")
    
    test_button = ttk.Button(
        main_frame,
        text="显示确认对话框",
        command=show_dialog
    )
    test_button.pack(pady=20)
    
    result_label = ttk.Label(main_frame, text="点击按钮测试对话框", font=("Arial", 12))
    result_label.pack(pady=10)
    
    # 说明
    info_text = """测试步骤：
1. 点击"显示确认对话框"按钮
2. 查看弹出的对话框是否有"是"和"否"按钮
3. 测试按钮点击功能
4. 测试倒计时超时功能

如果看不到按钮，可能的原因：
- 窗口大小不够
- 布局问题
- 字体渲染问题"""
    
    info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
    info_label.pack(pady=20)
    
    print("主测试窗口已创建")
    root.mainloop()

if __name__ == "__main__":
    print("简单对话框按钮测试")
    print("=" * 50)
    
    test_button_visibility()
