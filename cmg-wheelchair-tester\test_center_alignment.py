#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结果列居中对齐效果
"""

import tkinter as tk
from tkinter import ttk
import time

def test_center_alignment():
    """测试结果列居中对齐"""
    print("=== 测试结果列居中对齐 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("结果列居中对齐测试")
    root.geometry("800x600")
    
    # 设置样式
    style = ttk.Style()
    
    # 设置默认字体
    default_font = ("Microsoft YaHei UI", 10)
    root.option_add("*Font", default_font)
    
    # 设置全局背景色和前景色
    style.configure(".", 
        background="#ffffff",    # 白色背景
        foreground="#333333"     # 深灰色文字
    )
    
    # 设置树形视图样式
    style.configure("Treeview", 
        font=("Microsoft YaHei UI", 10),
        background="#ffffff",    # 白色背景
        fieldbackground="#ffffff",# 白色背景
        foreground="#333333"     # 深灰色文字
    )
    style.configure("Treeview.Heading", 
        font=("Microsoft YaHei UI", 10, "bold"),
        background="#e9ecef",    # 浅灰色背景
        foreground="#333333"     # 深灰色文字
    )
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="结果列居中对齐测试", font=("Microsoft YaHei UI", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 创建测试项目列表
    test_frame = ttk.LabelFrame(main_frame, text="测试项目列表", padding="10")
    test_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    # 创建树形视图
    test_tree = ttk.Treeview(
        test_frame,
        columns=("data", "result"),
        show="tree headings",
        selectmode="browse",
        height=15
    )
    
    test_tree.heading("#0", text="测试项目")
    test_tree.heading("data", text="测试数据")
    test_tree.heading("result", text="测试结果")
    test_tree.column("#0", width=250, minwidth=250)
    test_tree.column("data", width=200, minwidth=200)
    test_tree.column("result", width=100, minwidth=100, anchor="center")  # 结果列居中
    test_tree.pack(fill=tk.BOTH, expand=True)
    
    # 自定义结果列样式设置方法
    def set_result_cell_style(item, result_text):
        """设置结果列的样式"""
        if result_text == "PASS":
            styled_result = f"PASS"
            test_tree.set(item, "result", styled_result)
            test_tree.tag_configure("pass_style", 
                                   background="#28a745", 
                                   foreground="#000000",
                                   font=("Microsoft YaHei UI", 10, "bold"))
            test_tree.item(item, tags=("pass_style",))
        elif result_text == "FAIL":
            styled_result = f"FAIL"
            test_tree.set(item, "result", styled_result)
            test_tree.tag_configure("fail_style", 
                                   background="#dc3545", 
                                   foreground="#000000",
                                   font=("Microsoft YaHei UI", 10, "bold"))
            test_tree.item(item, tags=("fail_style",))
        elif result_text == "测试中":
            styled_result = f"测试中"
            test_tree.set(item, "result", styled_result)
            test_tree.tag_configure("testing_style", 
                                   background="#ffc107", 
                                   foreground="#000000",
                                   font=("Microsoft YaHei UI", 10, "bold"))
            test_tree.item(item, tags=("testing_style",))
        else:
            test_tree.set(item, "result", result_text)
            test_tree.item(item, tags=())
    
    # 测试数据 - 包含不同长度的结果文本
    test_projects = [
        {"name": "USB关键器件测试", "data": "检测到9个USB设备", "result": "PASS"},
        {"name": "CAN0测试", "data": "CAN数据发送成功", "result": "PASS"},
        {"name": "GPS测试", "data": "命令执行失败", "result": "FAIL"},
        {"name": "4G模组测试", "data": "返回模组版本信息", "result": "PASS"},
        {"name": "按键测试", "data": "检测到8个按键", "result": "PASS"},
        {"name": "按键灯测试", "data": "LED控制失败", "result": "FAIL"},
        {"name": "手电筒测试", "data": "正在测试中...", "result": "测试中"},
        {"name": "摇杆使能测试", "data": "返回值匹配", "result": "PASS"},
        {"name": "前摄像头测试", "data": "图片拉取失败", "result": "FAIL"},
        {"name": "光感测试", "data": "检测到数值变化", "result": "PASS"},
        {"name": "回充摄像头测试", "data": "拍照失败", "result": "FAIL"},
        {"name": "喇叭测试", "data": "音频播放完成", "result": "PASS"},
        {"name": "蓝牙测试", "data": "MAC: 24:21:5E:C0:30:F3", "result": "PASS"},
        {"name": "WiFi测试", "data": "平均延时: 11.93ms", "result": "PASS"},
    ]
    
    # 添加测试项目到树形视图
    items = []
    for i, project in enumerate(test_projects):
        item = test_tree.insert("", "end", text=project["name"])
        test_tree.set(item, "data", project["data"])
        set_result_cell_style(item, project["result"])
        items.append(item)
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    def test_different_results():
        """测试不同长度的结果文本"""
        test_results = ["PASS", "FAIL", "测试中", "P", "F", "通过", "失败", "OK", "ERROR", "SUCCESS"]
        
        for i, item in enumerate(items):
            if i < len(test_results):
                set_result_cell_style(item, test_results[i])
        root.update()
    
    def test_alignment_comparison():
        """测试对齐方式对比"""
        # 创建对比窗口
        compare_window = tk.Toplevel(root)
        compare_window.title("对齐方式对比")
        compare_window.geometry("900x400")
        
        # 左侧：左对齐
        left_frame = ttk.LabelFrame(compare_window, text="左对齐 (默认)", padding="10")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 5), pady=10)
        
        left_tree = ttk.Treeview(left_frame, columns=("result",), show="tree headings", height=10)
        left_tree.heading("#0", text="测试项目")
        left_tree.heading("result", text="测试结果")
        left_tree.column("#0", width=200)
        left_tree.column("result", width=100, anchor="w")  # 左对齐
        left_tree.pack(fill=tk.BOTH, expand=True)
        
        # 右侧：居中对齐
        right_frame = ttk.LabelFrame(compare_window, text="居中对齐 (新样式)", padding="10")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(5, 10), pady=10)
        
        right_tree = ttk.Treeview(right_frame, columns=("result",), show="tree headings", height=10)
        right_tree.heading("#0", text="测试项目")
        right_tree.heading("result", text="测试结果")
        right_tree.column("#0", width=200)
        right_tree.column("result", width=100, anchor="center")  # 居中对齐
        right_tree.pack(fill=tk.BOTH, expand=True)
        
        # 添加测试数据
        test_data = [
            ("USB测试", "PASS"),
            ("CAN测试", "FAIL"),
            ("GPS测试", "测试中"),
            ("4G测试", "P"),
            ("按键测试", "F"),
            ("WiFi测试", "OK"),
            ("蓝牙测试", "ERROR"),
        ]
        
        for name, result in test_data:
            # 左对齐树
            left_item = left_tree.insert("", "end", text=name)
            left_tree.set(left_item, "result", result)
            
            # 居中对齐树
            right_item = right_tree.insert("", "end", text=name)
            right_tree.set(right_item, "result", result)
    
    def reset_results():
        """重置为原始结果"""
        for i, item in enumerate(items):
            project = test_projects[i]
            set_result_cell_style(item, project["result"])
        root.update()
    
    # 按钮
    test_button = ttk.Button(
        button_frame,
        text="测试不同结果",
        command=test_different_results
    )
    test_button.pack(side=tk.LEFT, padx=(0, 10))
    
    compare_button = ttk.Button(
        button_frame,
        text="对齐方式对比",
        command=test_alignment_comparison
    )
    compare_button.pack(side=tk.LEFT, padx=(0, 10))
    
    reset_button = ttk.Button(
        button_frame,
        text="重置结果",
        command=reset_results
    )
    reset_button.pack(side=tk.LEFT)
    
    # 说明文本
    info_frame = ttk.LabelFrame(main_frame, text="居中对齐说明", padding="10")
    info_frame.pack(fill=tk.X, pady=10)
    
    info_text = """结果列居中对齐特点：
• 使用 anchor="center" 参数设置列内容居中
• 适用于短文本内容，如 PASS、FAIL、测试中等
• 提供更好的视觉平衡和专业外观
• 特别适合状态类信息的显示

点击上方按钮可以测试不同的显示效果。"""
    
    info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
    info_label.pack(fill=tk.X)
    
    print("结果列居中对齐测试窗口已创建")
    print("特点：")
    print("- 结果列使用 anchor='center' 实现居中对齐")
    print("- 适合显示状态类信息")
    print("- 提供更好的视觉效果")
    
    root.mainloop()

if __name__ == "__main__":
    print("结果列居中对齐测试工具")
    print("=" * 50)
    
    test_center_alignment()
