[2025-07-18 11:36:09] === 开始新的测试 ===
[2025-07-18 11:36:09] 开始测试
[2025-07-18 11:36:09] 工序: 右手臂功能测试
[2025-07-18 11:36:09] 序列号: None
[2025-07-18 11:36:09] 开始运行 右手臂功能测试 工序测试，共 17 个项目
[2025-07-18 11:36:09] 
开始执行: 设备连接状态检测
[2025-07-18 11:36:09] 设备连接测试失败：未检测到设备
[2025-07-18 11:36:09] 
开始执行: 3568版本测试
[2025-07-18 11:36:09] 执行3568版本测试...
[2025-07-18 11:36:09] 读取ROM版本号...
[2025-07-18 11:36:09] 执行命令: adb shell uname -a
[2025-07-18 11:36:09] ❌ uname命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:10] 
开始执行: USB关键器件检测
[2025-07-18 11:36:10] 执行USB设备检测...
[2025-07-18 11:36:10] 执行命令: adb shell lsusb
[2025-07-18 11:36:10] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:11] 
开始执行: CAN0测试
[2025-07-18 11:36:11] 执行CAN0测试流程...
[2025-07-18 11:36:11] 执行命令: adb shell ip link set can0 down
[2025-07-18 11:36:11] CAN0 down失败: error: no devices/emulators found

[2025-07-18 11:36:11] 
开始执行: GPS测试
[2025-07-18 11:36:11] 执行GPS测试...
[2025-07-18 11:36:11] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 11:36:11] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:11] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-18 11:36:12] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:12] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-18 11:36:12] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:12] GPS信号检测失败
[2025-07-18 11:36:12] 
开始执行: 4G模组测试
[2025-07-18 11:36:12] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 11:36:12] 监听线程已启动，等待串口数据...
[2025-07-18 11:36:14] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 11:36:14] AT指令发送失败: error: no devices/emulators found

[2025-07-18 11:36:15] 
开始执行: 按键测试
[2025-07-18 11:36:15] 开始按键测试...
[2025-07-18 11:36:15] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 11:36:17] 按键测试失败 - 只检测到0个按键
[2025-07-18 11:36:20] 
开始执行: 按键灯测试
[2025-07-18 11:36:20] 开始LED背光灯测试...
[2025-07-18 11:36:20] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:36:20] LED控制命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:20] 
开始执行: 手电筒测试
[2025-07-18 11:36:20] 开始手电筒LED测试...
[2025-07-18 11:36:20] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 11:36:21] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:21] 
开始执行: 摇杆使能测试
[2025-07-18 11:36:21] 执行摇杆测试...
[2025-07-18 11:36:21] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 11:36:21] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:22] 
开始执行: 前摄像头测试
[2025-07-18 11:36:22] 开始执行前摄像头测试...
[2025-07-18 11:36:22] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 11:36:22] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:22] 
开始执行: 光感测试
[2025-07-18 11:36:22] 执行光感测试...
[2025-07-18 11:36:22] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 11:36:23] 光感测试失败 - 未检测到数值变化
[2025-07-18 11:36:23] 
开始执行: 回充摄像头测试
[2025-07-18 11:36:23] 开始执行回充摄像头测试...
[2025-07-18 11:36:23] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 11:36:23] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:24] 
开始执行: 喇叭测试
[2025-07-18 11:36:24] 执行喇叭测试...
[2025-07-18 11:36:24] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 11:36:24] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:24] 
开始执行: 蓝牙测试
[2025-07-18 11:36:24] 执行蓝牙测试...
[2025-07-18 11:36:24] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 11:36:24] 执行命令: adb shell bluetoothctl show
[2025-07-18 11:36:24] ❌ 命令执行失败: error: no devices/emulators found

[2025-07-18 11:36:25] 
开始执行: 4G网络测试
[2025-07-18 11:36:25] 执行4G网络测试...
[2025-07-18 11:36:25] 第一步：关闭WiFi网络...
[2025-07-18 11:36:25] 关闭WiFi失败: error: no devices/emulators found

[2025-07-18 11:36:25] 第三步：重新打开WiFi网络...
[2025-07-18 11:36:25] 重新打开WiFi失败: error: no devices/emulators found

[2025-07-18 11:36:28] 
开始执行: WiFi测试
[2025-07-18 11:36:28] 执行WiFi测试...
[2025-07-18 11:36:28] 第一步：关闭4G网络...
[2025-07-18 11:36:28] 关闭4G网络失败: error: no devices/emulators found

[2025-07-18 11:36:28] 第三步：重新打开4G网络...
[2025-07-18 11:36:28] 重新打开4G网络失败: error: no devices/emulators found

[2025-07-18 11:36:28] 重新打开4G网络时出错: cannot access local variable 'time' where it is not associated with a value
[2025-07-18 11:36:28] 
测试完成 - 通过率: 0/17
[2025-07-18 11:36:28] ❌ 存在测试失败项！

