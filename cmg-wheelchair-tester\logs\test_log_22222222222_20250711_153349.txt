[2025-07-11 15:32:07] 开始测试 - SN: 22222222222
[2025-07-11 15:32:08] 
开始执行: 设备连接状态检测
[2025-07-11 15:32:08] 设备连接测试通过，检测到 1 个设备
[2025-07-11 15:32:08] 设备: ?	device
[2025-07-11 15:32:08] 
开始执行: USB关键器件检测
[2025-07-11 15:32:08] 执行USB设备检测...
[2025-07-11 15:32:08] 执行命令: adb shell lsusb
[2025-07-11 15:32:08] 设备返回数据:
[2025-07-11 15:32:08] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-11 15:32:08] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-11 15:32:08] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-11 15:32:08] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-11 15:32:08] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-11 15:32:08] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-11 15:32:08] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-11 15:32:08] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-11 15:32:08] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-11 15:32:08] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-11 15:32:08] 总共解析到 9 个设备
[2025-07-11 15:32:08] ✅ 所有预期的设备ID都已找到
[2025-07-11 15:32:08] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 15:32:08] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 15:32:08] ✅ USB设备检测通过
[2025-07-11 15:32:08] 
开始执行: CAN0测试
[2025-07-11 15:32:08] 执行CAN0测试流程...
[2025-07-11 15:32:08] 执行命令: adb shell ip link set can0 down
[2025-07-11 15:32:08] CAN0已关闭
[2025-07-11 15:32:08] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-11 15:32:08] CAN0已启动
[2025-07-11 15:32:08] CAN监听线程已启动...
[2025-07-11 15:32:09] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-11 15:32:09] CAN测试数据已发送，等待监听返回...
[2025-07-11 15:32:09] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-11 15:32:11] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-11 15:32:12] 
开始执行: GPS测试
[2025-07-11 15:32:12] 执行GPS测试...
[2025-07-11 15:32:12] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-11 15:32:22] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-11 15:32:22] 
开始执行: 4G模组测试
[2025-07-11 15:32:23] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-11 15:32:23] 监听线程已启动，等待串口数据...
[2025-07-11 15:32:25] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-11 15:32:25] AT+CCID指令已发送，等待串口返回...
[2025-07-11 15:32:25] 串口输出: AT+CCID
[2025-07-11 15:32:25] 串口输出: 
[2025-07-11 15:32:25] 串口输出: 
[2025-07-11 15:32:25] 串口输出: +CCID: 89860114851012535241
[2025-07-11 15:32:27] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-11 15:32:28] 
开始执行: 按键测试
[2025-07-11 15:32:28] 开始按键测试...
[2025-07-11 15:32:28] 执行命令: adb shell evtest /dev/input/event5
[2025-07-11 15:32:30] 原始事件: Event: time 1752219152.088148, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 15:32:30] 解析结果: key_code=662, value=1
[2025-07-11 15:32:30] ✓ 检测到档位减(662)按下
[2025-07-11 15:32:31] 原始事件: Event: time 1752219152.258322, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 15:32:31] 解析结果: key_code=662, value=0
[2025-07-11 15:32:31] ✓ 档位减(662)测试通过
[2025-07-11 15:32:31] 原始事件: Event: time 1752219153.008248, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-11 15:32:31] 解析结果: key_code=658, value=1
[2025-07-11 15:32:31] ✓ 检测到喇叭键(658)按下
[2025-07-11 15:32:32] 原始事件: Event: time 1752219153.208178, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-11 15:32:32] 解析结果: key_code=658, value=0
[2025-07-11 15:32:32] ✓ 喇叭键(658)测试通过
[2025-07-11 15:32:34] 原始事件: Event: time 1752219155.184942, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-11 15:32:34] 解析结果: key_code=661, value=1
[2025-07-11 15:32:34] ✓ 检测到静音键(661)按下
[2025-07-11 15:32:34] 原始事件: Event: time 1752219155.408140, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-11 15:32:34] 解析结果: key_code=661, value=0
[2025-07-11 15:32:34] ✓ 静音键(661)测试通过
[2025-07-11 15:32:35] 原始事件: Event: time 1752219156.734796, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 15:32:35] 解析结果: key_code=663, value=1
[2025-07-11 15:32:35] ✓ 检测到语音键(663)按下
[2025-07-11 15:32:35] 原始事件: Event: time 1752219156.884735, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 15:32:35] 解析结果: key_code=663, value=0
[2025-07-11 15:32:35] ✓ 语音键(663)测试通过
[2025-07-11 15:32:36] 原始事件: Event: time 1752219157.908330, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 15:32:36] 解析结果: key_code=657, value=1
[2025-07-11 15:32:36] ✓ 检测到SOS键(657)按下
[2025-07-11 15:32:36] 原始事件: Event: time 1752219158.084813, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 15:32:36] 解析结果: key_code=657, value=0
[2025-07-11 15:32:36] ✓ SOS键(657)测试通过
[2025-07-11 15:32:39] 原始事件: Event: time 1752219160.308061, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-11 15:32:39] 解析结果: key_code=659, value=1
[2025-07-11 15:32:39] ✓ 检测到档位加(659)按下
[2025-07-11 15:32:39] 原始事件: Event: time 1752219160.484851, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-11 15:32:39] 解析结果: key_code=659, value=0
[2025-07-11 15:32:39] ✓ 档位加(659)测试通过
[2025-07-11 15:32:40] 原始事件: Event: time 1752219161.834786, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 15:32:40] 解析结果: key_code=660, value=1
[2025-07-11 15:32:40] ✓ 检测到智驾键(660)按下
[2025-07-11 15:32:40] 原始事件: Event: time 1752219162.058132, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 15:32:40] 解析结果: key_code=660, value=0
[2025-07-11 15:32:40] ✓ 智驾键(660)测试通过
[2025-07-11 15:32:42] 原始事件: Event: time 1752219163.558298, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 15:32:42] 解析结果: key_code=662, value=1
[2025-07-11 15:32:42] ✓ 检测到档位减(662)按下
[2025-07-11 15:32:42] 原始事件: Event: time 1752219163.808058, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 15:32:42] 解析结果: key_code=662, value=0
[2025-07-11 15:32:42] ✓ 档位减(662)测试通过
[2025-07-11 15:32:43] 原始事件: Event: time 1752219164.708167, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-11 15:32:43] 解析结果: key_code=658, value=1
[2025-07-11 15:32:43] ✓ 检测到喇叭键(658)按下
[2025-07-11 15:32:43] 原始事件: Event: time 1752219164.908137, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-11 15:32:43] 解析结果: key_code=658, value=0
[2025-07-11 15:32:43] ✓ 喇叭键(658)测试通过
[2025-07-11 15:32:47] 原始事件: Event: time 1752219168.134834, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 15:32:47] 解析结果: key_code=662, value=1
[2025-07-11 15:32:47] ✓ 检测到档位减(662)按下
[2025-07-11 15:32:47] 原始事件: Event: time 1752219168.584951, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 15:32:47] 解析结果: key_code=662, value=0
[2025-07-11 15:32:47] ✓ 档位减(662)测试通过
[2025-07-11 15:32:51] 原始事件: Event: time 1752219172.634811, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-11 15:32:51] 解析结果: key_code=659, value=1
[2025-07-11 15:32:51] ✓ 检测到档位加(659)按下
[2025-07-11 15:32:51] 原始事件: Event: time 1752219172.884952, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-11 15:32:51] 解析结果: key_code=659, value=0
[2025-07-11 15:32:51] ✓ 档位加(659)测试通过
[2025-07-11 15:32:56] 原始事件: Event: time 1752219177.584784, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 15:32:56] 解析结果: key_code=662, value=1
[2025-07-11 15:32:56] ✓ 检测到档位减(662)按下
[2025-07-11 15:32:56] 原始事件: Event: time 1752219177.858124, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 15:32:56] 解析结果: key_code=662, value=0
[2025-07-11 15:32:56] ✓ 档位减(662)测试通过
[2025-07-11 15:32:57] 原始事件: Event: time 1752219178.558147, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 15:32:57] 解析结果: key_code=662, value=1
[2025-07-11 15:32:57] ✓ 检测到档位减(662)按下
[2025-07-11 15:32:57] 原始事件: Event: time 1752219178.688119, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 15:32:57] 解析结果: key_code=662, value=0
[2025-07-11 15:32:57] ✓ 档位减(662)测试通过
[2025-07-11 15:32:58] 原始事件: Event: time 1752219179.508242, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-11 15:32:58] 解析结果: key_code=658, value=1
[2025-07-11 15:32:58] ✓ 检测到喇叭键(658)按下
[2025-07-11 15:32:58] 原始事件: Event: time 1752219179.711470, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-11 15:32:58] 解析结果: key_code=658, value=0
[2025-07-11 15:32:58] ✓ 喇叭键(658)测试通过
[2025-07-11 15:33:00] 原始事件: Event: time 1752219181.808997, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 15:33:00] 解析结果: key_code=656, value=1
[2025-07-11 15:33:00] ✓ 检测到锁定键(656)按下
[2025-07-11 15:33:00] 原始事件: Event: time 1752219182.084833, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 15:33:00] 解析结果: key_code=656, value=0
[2025-07-11 15:33:00] ✓ 锁定键(656)测试通过
[2025-07-11 15:33:01] 按键测试完成 - 检测到8个按键
[2025-07-11 15:33:03] 
开始执行: 按键灯测试
[2025-07-11 15:33:04] 开始LED背光灯测试...
[2025-07-11 15:33:04] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-11 15:33:04] LED控制命令执行成功
[2025-07-11 15:33:11] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-11 15:33:11] LED灯已关闭
[2025-07-11 15:33:11] LED测试通过 - 背光灯正常点亮
[2025-07-11 15:33:11] 
开始执行: 手电筒测试
[2025-07-11 15:33:11] 开始手电筒LED测试...
[2025-07-11 15:33:11] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-11 15:33:11] 手电筒控制命令执行成功
[2025-07-11 15:33:13] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-11 15:33:13] 手电筒已关闭
[2025-07-11 15:33:13] 手电筒测试通过 - 手电筒正常点亮
[2025-07-11 15:33:13] 
开始执行: 摇杆使能测试
[2025-07-11 15:33:13] 执行摇杆测试...
[2025-07-11 15:33:13] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-11 15:33:13] 命令执行成功
[2025-07-11 15:33:13] 返回数据: 255
[2025-07-11 15:33:13] 摇杆测试通过，值: 255
[2025-07-11 15:33:13] 
开始执行: 前摄像头测试
[2025-07-11 15:33:14] 开始执行前摄像头测试...
[2025-07-11 15:33:14] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-11 15:33:14] 命令执行成功，返回: 无输出
[2025-07-11 15:33:14] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-11 15:33:15] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.312245115
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-11 15:33:15] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-11 15:33:15] 拉取命令执行成功
[2025-07-11 15:33:15] 返回数据: [ 37%] /data/camera/cam0_3840x2160.jpg
[ 75%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 21.0 MB/s (173626 bytes in 0.008s)
[2025-07-11 15:33:15] 图片已保存: cam0_3840x2160.jpg
[2025-07-11 15:33:16] 用户确认结果: 通过
[2025-07-11 15:33:17] 
开始执行: 光感测试
[2025-07-11 15:33:17] 执行光感测试...
[2025-07-11 15:33:17] 执行命令: adb shell evtest /dev/input/event1
[2025-07-11 15:33:23] 光感测试完成 - 检测到数值变化
[2025-07-11 15:33:23] 数值从 2 变化到 1
[2025-07-11 15:33:23] 
开始执行: 回充摄像头测试
[2025-07-11 15:33:23] 开始执行回充摄像头测试...
[2025-07-11 15:33:23] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-11 15:33:24] 命令执行成功，返回: 无输出
[2025-07-11 15:33:24] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-11 15:33:24] 命令执行成功，返回: 无输出
[2025-07-11 15:33:24] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-11 15:33:25] 拉取命令执行成功
[2025-07-11 15:33:25] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 14.9 MB/s (46920 bytes in 0.003s)
[2025-07-11 15:33:25] 图片已保存: output.jpg
[2025-07-11 15:33:32] 用户确认结果: 通过
[2025-07-11 15:33:33] 
开始执行: 喇叭测试
[2025-07-11 15:33:33] 执行喇叭测试...
[2025-07-11 15:33:33] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-11 15:33:47] 命令执行成功
[2025-07-11 15:33:47] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-11 15:33:47] 音频播放完成
[2025-07-11 15:33:48] 
开始执行: 蓝牙测试
[2025-07-11 15:33:48] 执行蓝牙测试...
[2025-07-11 15:33:48] 启动蓝牙服务...
[2025-07-11 15:33:48] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-11 15:33:48] 开启蓝牙...
[2025-07-11 15:33:48] 开启蓝牙失败: 
[2025-07-11 15:33:48] 执行命令: adb shell bluetoothctl devices
[2025-07-11 15:33:48] 命令执行成功
[2025-07-11 15:33:48] 返回数据:
[2025-07-11 15:33:48]   No default controller available
[2025-07-11 15:33:48] ❌ 蓝牙测试失败，未发现蓝牙设备
[2025-07-11 15:33:48] 
开始执行: WiFi测试
[2025-07-11 15:33:48] 执行WiFi测试...
[2025-07-11 15:33:48] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-11 15:33:48] 命令执行失败: 
[2025-07-11 15:33:49] 
测试完成 - 通过率: 12/15
[2025-07-11 15:33:49] ❌ 存在测试失败项！
[2025-07-11 15:33:49] 测试记录已保存: records/22222222222_20250711_153349.json
[2025-07-11 15:33:49] 测试日志已保存: records/22222222222_20250711_153349.log

