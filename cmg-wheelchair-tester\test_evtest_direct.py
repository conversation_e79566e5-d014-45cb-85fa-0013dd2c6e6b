#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试evtest命令
"""

import subprocess
import time

def test_evtest_direct():
    """直接测试evtest /dev/input/event5"""
    print("=== 直接测试evtest /dev/input/event5 ===")
    try:
        print("执行: adb shell evtest /dev/input/event5")
        print("请在10秒内按按键...")
        
        result = subprocess.run(
            ['adb', 'shell', 'timeout', '10', 'evtest', '/dev/input/event5'],
            capture_output=True,
            text=True,
            timeout=15
        )
        
        print(f"返回码: {result.returncode}")
        print(f"标准输出:")
        print(result.stdout)
        
        if result.stderr:
            print(f"错误输出:")
            print(result.stderr)
            
    except subprocess.TimeoutExpired:
        print("命令超时")
    except Exception as e:
        print(f"执行失败: {e}")

def test_cat_event5():
    """测试直接读取event5设备"""
    print("\n=== 测试cat /dev/input/event5 ===")
    try:
        print("执行: adb shell timeout 5 cat /dev/input/event5")
        print("请在5秒内按按键...")
        
        result = subprocess.run(
            ['adb', 'shell', 'timeout', '5', 'cat', '/dev/input/event5'],
            capture_output=True,
            timeout=10
        )
        
        print(f"返回码: {result.returncode}")
        print(f"输出长度: {len(result.stdout)} 字节")
        
        if result.stdout:
            print("检测到输入事件!")
        else:
            print("没有检测到输入事件")
            
        if result.stderr:
            print(f"错误: {result.stderr.decode('utf-8', errors='ignore')}")
            
    except Exception as e:
        print(f"测试失败: {e}")

def test_hexdump_event5():
    """使用hexdump测试event5"""
    print("\n=== 使用hexdump测试event5 ===")
    try:
        print("执行: adb shell timeout 5 hexdump -C /dev/input/event5")
        print("请在5秒内按按键...")
        
        result = subprocess.run(
            ['adb', 'shell', 'timeout', '5', 'hexdump', '-C', '/dev/input/event5'],
            capture_output=True,
            text=True,
            timeout=10
        )
        
        print(f"返回码: {result.returncode}")
        if result.stdout:
            print("hexdump输出:")
            print(result.stdout)
        else:
            print("没有hexdump输出")
            
        if result.stderr:
            print(f"错误: {result.stderr}")
            
    except Exception as e:
        print(f"hexdump测试失败: {e}")

def check_evtest_availability():
    """检查evtest是否可用"""
    print("\n=== 检查evtest可用性 ===")
    try:
        result = subprocess.run(
            ['adb', 'shell', 'which', 'evtest'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print(f"evtest路径: {result.stdout.strip()}")
        else:
            print("evtest命令不可用")
            
        # 检查版本
        result = subprocess.run(
            ['adb', 'shell', 'evtest', '--version'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print(f"evtest版本: {result.stdout.strip()}")
        else:
            print("无法获取evtest版本")
            
    except Exception as e:
        print(f"检查evtest可用性失败: {e}")

def test_input_event_info():
    """获取输入事件设备信息"""
    print("\n=== 获取输入事件设备信息 ===")
    try:
        # 检查event5的信息
        result = subprocess.run(
            ['adb', 'shell', 'cat', '/sys/class/input/event5/device/name'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print(f"event5设备名称: {result.stdout.strip()}")
        else:
            print("无法获取event5设备名称")
            
        # 检查capabilities
        result = subprocess.run(
            ['adb', 'shell', 'cat', '/proc/bus/input/devices'],
            capture_output=True,
            text=True,
            timeout=5
        )
        
        if result.returncode == 0:
            print("输入设备信息:")
            lines = result.stdout.split('\n')
            in_gpio_keys = False
            for line in lines:
                if 'gpio-keys' in line:
                    in_gpio_keys = True
                    print(f"  {line}")
                elif in_gpio_keys and line.strip():
                    print(f"  {line}")
                elif in_gpio_keys and not line.strip():
                    break
                    
    except Exception as e:
        print(f"获取设备信息失败: {e}")

if __name__ == "__main__":
    print("evtest直接测试")
    print("=" * 50)
    
    # 检查evtest可用性
    check_evtest_availability()
    
    # 获取设备信息
    test_input_event_info()
    
    # 直接测试evtest
    test_evtest_direct()
    
    # 测试cat方法
    test_cat_event5()
    
    # 测试hexdump方法
    test_hexdump_event5()
    
    print("\n测试完成")
