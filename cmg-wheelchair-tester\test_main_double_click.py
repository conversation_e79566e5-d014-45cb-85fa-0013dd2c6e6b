#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试主程序的双击功能
启动主程序并测试双击是否工作
"""

import subprocess
import time
import sys
import os

def test_main_double_click():
    """测试主程序的双击功能"""
    print("=== 测试主程序双击功能 ===")
    
    # 检查文件是否存在
    if not os.path.exists('main.py'):
        print("❌ main.py 文件不存在")
        return False
    
    if not os.path.exists('config.json'):
        print("❌ config.json 文件不存在")
        return False
    
    print("✅ 文件检查通过")
    
    # 提供测试指导
    print("\n📋 测试步骤:")
    print("1. 主程序将在3秒后启动")
    print("2. 选择一个工序（如'整机半成品功能测试'）")
    print("3. 尝试双击测试项目列表中的任意项目")
    print("4. 观察日志输出是否有以下信息:")
    print("   - '🔧 run_single_test 方法被调用'")
    print("   - '🔧 选中的项目: ...'")
    print("   - '开始单项测试: ...'")
    print("5. 如果双击不工作，尝试右键菜单")
    print("6. 关闭程序窗口完成测试")
    
    print("\n⏰ 3秒后启动主程序...")
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    try:
        print("🚀 启动主程序...")
        
        # 启动主程序
        process = subprocess.Popen(
            [sys.executable, "main.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        print("✅ 主程序已启动")
        print("📝 请按照上述步骤进行测试")
        print("🔍 观察程序窗口中的日志输出")
        print("⚠️ 如果看到调试信息，说明事件绑定正常工作")
        
        # 等待程序结束
        stdout, stderr = process.communicate()
        
        print(f"\n📊 程序退出，返回码: {process.returncode}")
        
        if stdout:
            print("📤 标准输出:")
            print(stdout)
        
        if stderr:
            print("❌ 错误输出:")
            print(stderr)
        
        return process.returncode == 0
        
    except Exception as e:
        print(f"❌ 启动主程序失败: {e}")
        return False

def provide_troubleshooting():
    """提供故障排除指导"""
    print("\n🔧 故障排除指导:")
    
    print("\n1. 如果双击不工作:")
    print("   - 确保先选中了测试项目")
    print("   - 尝试双击项目名称部分，不要点击数据列")
    print("   - 检查日志是否有'🔧 单击事件触发'信息")
    print("   - 如果有单击信息但没有双击信息，可能是双击速度问题")
    
    print("\n2. 如果右键菜单工作:")
    print("   - 说明事件绑定基本正常")
    print("   - 问题可能在双击事件的处理上")
    print("   - 可以使用右键菜单作为替代方案")
    
    print("\n3. 如果完全没有事件响应:")
    print("   - 检查是否正确选择了工序")
    print("   - 确保测试项目列表有内容")
    print("   - 重启程序重试")
    
    print("\n4. 调试信息说明:")
    print("   - '🔧 单击事件触发' - 说明单击事件正常")
    print("   - '🔧 按钮释放事件触发' - 说明按钮事件正常")
    print("   - '🔧 run_single_test 方法被调用' - 说明双击事件正常")
    print("   - '🔧 选中的项目: ...' - 说明项目选择正常")
    
    print("\n5. 如果仍然有问题:")
    print("   - 检查Python和tkinter版本")
    print("   - 尝试在不同的操作系统上测试")
    print("   - 查看是否有其他程序干扰")

def check_environment():
    """检查环境"""
    print("\n🔍 环境检查:")
    
    # 检查Python版本
    print(f"Python版本: {sys.version}")
    
    # 检查tkinter
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        print("✅ tkinter 可用")
        root.destroy()
    except Exception as e:
        print(f"❌ tkinter 不可用: {e}")
    
    # 检查其他依赖
    try:
        import json
        print("✅ json 模块可用")
    except Exception as e:
        print(f"❌ json 模块不可用: {e}")
    
    try:
        import subprocess
        print("✅ subprocess 模块可用")
    except Exception as e:
        print(f"❌ subprocess 模块不可用: {e}")

if __name__ == "__main__":
    print("主程序双击功能测试")
    print("=" * 50)
    
    # 检查环境
    check_environment()
    
    # 运行测试
    success = test_main_double_click()
    
    # 提供故障排除指导
    provide_troubleshooting()
    
    if success:
        print("\n✅ 测试完成")
    else:
        print("\n❌ 测试失败")
    
    print("\n📝 测试总结:")
    print("- 如果看到调试信息，说明双击功能正常")
    print("- 如果没有调试信息，说明双击事件没有触发")
    print("- 可以使用右键菜单作为替代方案")
    print("- 已添加详细的调试信息帮助排查问题")
