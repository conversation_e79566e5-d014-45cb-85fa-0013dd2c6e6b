#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单项测试功能
验证：
1. 双击测试项目是否能运行单项测试
2. 右键菜单是否正常显示
3. 单项测试结果是否正确更新
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time
import random

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}, "test_projects": []}

def test_single_test_function():
    """测试单项测试功能"""
    print("=== 测试单项测试功能 ===")
    
    config = load_config()
    test_projects = config.get("test_projects", [])
    work_processes = config.get("work_processes", {})
    
    if not test_projects:
        print("❌ 没有找到测试项目")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title("单项测试功能验证")
    root.geometry("1000x700")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 1000) // 2
    y = (screen_height - 700) // 2
    root.geometry(f"1000x700+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="单项测试功能验证", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 功能说明
    info_frame = ttk.LabelFrame(main_frame, text="单项测试功能说明", padding="15")
    info_frame.pack(fill=tk.X, pady=(0, 20))
    
    info_text = """🔧 单项测试功能:

1. 双击测试项目:
   • 直接运行该项目的测试
   • 实时更新测试状态和结果
   • 显示测试数据和结果

2. 右键菜单:
   • 右键点击测试项目显示菜单
   • 选择"运行测试"执行单项测试
   • 选择"查看详情"查看测试信息

3. 测试状态更新:
   • 测试中: 黄色显示"测试中"
   • 测试通过: 绿色显示"PASS"
   • 测试失败: 红色显示"FAIL"

4. 使用场景:
   • 某个项目测试失败，可以单独重测
   • 调试特定功能时，只测试相关项目
   • 验证修复效果时，重测问题项目"""
    
    ttk.Label(info_frame, text=info_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 创建左右分栏
    content_frame = ttk.Frame(main_frame)
    content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 左侧：测试项目列表
    left_frame = ttk.LabelFrame(content_frame, text="测试项目列表 (双击或右键测试)", padding="10")
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
    
    # 创建测试项目树
    columns = ("data", "result")
    test_tree = ttk.Treeview(left_frame, columns=columns, show="tree headings", height=15)
    
    # 设置列标题
    test_tree.heading("#0", text="测试项目", anchor=tk.W)
    test_tree.heading("data", text="测试数据", anchor=tk.W)
    test_tree.heading("result", text="测试结果", anchor=tk.CENTER)
    
    # 设置列宽
    test_tree.column("#0", width=200, minwidth=150)
    test_tree.column("data", width=180, minwidth=100)
    test_tree.column("result", width=80, minwidth=60)
    
    # 设置样式
    test_tree.tag_configure("pass_result", foreground="#28a745", font=("Microsoft YaHei UI", 10, "bold"))
    test_tree.tag_configure("fail_result", foreground="#dc3545", font=("Microsoft YaHei UI", 10, "bold"))
    test_tree.tag_configure("testing_result", foreground="#ffc107", font=("Microsoft YaHei UI", 10, "bold"))
    
    # 添加滚动条
    tree_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=test_tree.yview)
    test_tree.configure(yscrollcommand=tree_scrollbar.set)
    
    test_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 右侧：日志和控制
    right_frame = ttk.Frame(content_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    
    # 状态显示
    status_var = tk.StringVar(value="准备测试...")
    status_label = ttk.Label(right_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack(pady=(0, 15))
    
    # 日志显示
    log_frame = ttk.LabelFrame(right_frame, text="测试日志", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    log_text = tk.Text(log_frame, height=12, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message, color=None):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if color:
            start_pos = log_text.index(tk.END + "-1c")
            log_text.insert(tk.END, log_entry)
            end_pos = log_text.index(tk.END + "-1c")
            log_text.tag_add(color, start_pos, end_pos)
            log_text.tag_config(color, foreground=color)
        else:
            log_text.insert(tk.END, log_entry)
        
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 测试状态
    test_state = {
        "selected_process": "整机半成品功能测试",
        "running": False,
        "test_results": {}
    }
    
    # 初始化测试项目
    def init_test_projects():
        """初始化测试项目"""
        # 清空现有项目
        for item in test_tree.get_children():
            test_tree.delete(item)
        
        # 获取当前工序的测试项目
        if test_state["selected_process"] in work_processes:
            test_ids = work_processes[test_state["selected_process"]]["test_ids"]
            
            for test_id in test_ids:
                # 查找项目名称
                project_name = test_id
                for project in test_projects:
                    if project["id"] == test_id:
                        project_name = project["name"]
                        break
                
                # 创建测试项目
                item = test_tree.insert("", "end", text=project_name)
                test_tree.set(item, "data", "")
                test_tree.set(item, "result", "")
                
                # 初始化测试结果
                test_state["test_results"][test_id] = {
                    "name": project_name,
                    "status": "未测试",
                    "message": "",
                    "item": item
                }
    
    def update_test_item(test_id, status, message=""):
        """更新测试项目状态"""
        if test_id in test_state["test_results"]:
            result = test_state["test_results"][test_id]
            item = result["item"]
            
            result["status"] = status
            result["message"] = message
            
            test_tree.set(item, "data", message)
            
            if status == "通过":
                test_tree.set(item, "result", "PASS")
                test_tree.item(item, tags=("pass_result",))
            elif status == "失败":
                test_tree.set(item, "result", "FAIL")
                test_tree.item(item, tags=("fail_result",))
            elif status == "测试中":
                test_tree.set(item, "result", "测试中")
                test_tree.item(item, tags=("testing_result",))
            else:
                test_tree.set(item, "result", "")
                test_tree.item(item, tags=())
    
    def run_single_test_simulation(item_id):
        """运行单项测试模拟"""
        if test_state["running"]:
            log_message("已有测试在运行中，请等待完成", "orange")
            return
        
        test_state["running"] = True
        
        # 获取测试项目信息
        test_name = test_tree.item(item_id, "text")
        test_id = None
        
        for tid, result in test_state["test_results"].items():
            if result["item"] == item_id:
                test_id = tid
                break
        
        if not test_id:
            log_message("未找到测试项目信息", "red")
            test_state["running"] = False
            return
        
        log_message(f"开始单项测试: {test_name}", "blue")
        status_var.set(f"正在测试: {test_name}")
        
        # 更新状态为测试中
        update_test_item(test_id, "测试中", "正在测试...")
        
        def simulate_test():
            try:
                # 模拟测试过程
                time.sleep(2)  # 模拟测试时间
                
                # 随机生成测试结果
                is_pass = random.random() > 0.3  # 70%通过率
                
                if is_pass:
                    # 生成模拟测试数据
                    if "fps" in test_name.lower():
                        message = f"{random.randint(180, 200)} FPS"
                    elif "延时" in test_name or "网络" in test_name:
                        message = f"平均延时: {random.randint(20, 50)}ms"
                    elif "电压" in test_name:
                        message = f"{random.uniform(3.2, 3.8):.1f}V"
                    else:
                        message = "测试通过"
                    
                    update_test_item(test_id, "通过", message)
                    log_message(f"{test_name} - 测试通过 ✅", "green")
                    log_message(f"测试数据: {message}")
                else:
                    message = "测试失败"
                    update_test_item(test_id, "失败", message)
                    log_message(f"{test_name} - 测试失败 ❌", "red")
                
                status_var.set("测试完成")
                
            except Exception as e:
                log_message(f"测试出错: {str(e)}", "red")
                update_test_item(test_id, "失败", f"错误: {str(e)}")
            finally:
                test_state["running"] = False
        
        threading.Thread(target=simulate_test, daemon=True).start()
    
    def on_double_click(event):
        """双击事件处理"""
        item = test_tree.selection()
        if item:
            log_message("双击测试项目，开始单项测试", "blue")
            run_single_test_simulation(item[0])
    
    def show_test_menu(event):
        """显示右键菜单"""
        item = test_tree.selection()
        if item:
            test_menu.post(event.x_root, event.y_root)
    
    def run_menu_test():
        """从菜单运行测试"""
        item = test_tree.selection()
        if item:
            log_message("从右键菜单运行测试", "blue")
            run_single_test_simulation(item[0])
    
    def show_test_details():
        """显示测试详情"""
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            test_data = test_tree.set(item[0], "data")
            test_result = test_tree.set(item[0], "result")
            
            details = f"测试项目: {test_name}\n"
            details += f"测试数据: {test_data if test_data else '无'}\n"
            details += f"测试结果: {test_result if test_result else '未测试'}"
            
            messagebox.showinfo("测试详情", details)
    
    # 创建右键菜单
    test_menu = tk.Menu(root, tearoff=0)
    test_menu.add_command(label="运行测试", command=run_menu_test)
    test_menu.add_command(label="查看详情", command=show_test_details)
    
    # 绑定事件
    test_tree.bind("<Double-Button-1>", on_double_click)  # 双击
    test_tree.bind("<Button-3>", show_test_menu)  # 右键
    
    def reset_all_tests():
        """重置所有测试"""
        for test_id in test_state["test_results"]:
            update_test_item(test_id, "未测试", "")
        log_message("所有测试已重置")
        status_var.set("准备测试...")
    
    def run_random_tests():
        """运行随机测试"""
        if test_state["running"]:
            log_message("已有测试在运行中", "orange")
            return
        
        items = test_tree.get_children()
        if items:
            # 随机选择3个项目进行测试
            import random
            selected_items = random.sample(list(items), min(3, len(items)))
            
            def run_sequential():
                for item in selected_items:
                    if not test_state["running"]:
                        break
                    run_single_test_simulation(item)
                    time.sleep(1)  # 间隔1秒
            
            threading.Thread(target=run_sequential, daemon=True).start()
    
    # 控制按钮
    control_frame = ttk.Frame(right_frame)
    control_frame.pack(fill=tk.X)
    
    ttk.Button(control_frame, text="重置测试", 
              command=reset_all_tests, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="随机测试", 
              command=run_random_tests, width=12).pack(side=tk.LEFT, padx=5)
    
    # 说明文档
    instruction_frame = ttk.LabelFrame(main_frame, text="操作说明", padding="10")
    instruction_frame.pack(fill=tk.X)
    
    instruction_text = """📋 操作说明:

1. 双击测试: 双击任意测试项目开始单项测试
2. 右键菜单: 右键点击测试项目，选择"运行测试"
3. 查看详情: 右键选择"查看详情"查看测试信息
4. 重置测试: 点击"重置测试"清除所有测试结果
5. 随机测试: 点击"随机测试"随机选择项目进行测试

✅ 验证要点:
• 双击是否能正常启动单项测试
• 右键菜单是否正常显示和工作
• 测试状态是否正确更新（测试中→通过/失败）
• 测试数据是否正确显示"""
    
    ttk.Label(instruction_frame, text=instruction_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 9)).pack(fill=tk.X)
    
    # 初始化
    init_test_projects()
    log_message("单项测试功能验证已准备就绪")
    log_message("双击测试项目或右键选择'运行测试'开始单项测试")
    
    print("单项测试功能验证界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("单项测试功能验证")
    print("=" * 50)
    
    print("🔧 验证内容:")
    print("  1. 双击测试项目是否能运行单项测试")
    print("  2. 右键菜单是否正常显示")
    print("  3. 单项测试结果是否正确更新")
    print("  4. 测试状态变化是否正常")
    
    # 创建测试界面
    test_single_test_function()
    
    print("\n测试完成")
