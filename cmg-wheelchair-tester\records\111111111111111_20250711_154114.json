{"sn": "111111111111111", "timestamp": "2025-07-11T15:41:14.451019", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-11T15:39:59.836925"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-11T15:40:00.292362"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-11T15:40:00.908051"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-11T15:40:04.831032"}, "4g_test": {"test_name": "4G模组测试", "status": "通过", "message": "CCID: 89860114851012535241", "timestamp": "2025-07-11T15:40:15.420024"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-11T15:40:20.136773"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-11T15:40:37.243993"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-11T15:40:40.435317"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-11T15:40:42.020032"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-11T15:40:42.553605"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-11T15:40:46.049868"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-11T15:40:50.129209"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-11T15:40:53.502675"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "无设备数据", "timestamp": "2025-07-11T15:41:07.767579"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "WiFi连接正常", "timestamp": "2025-07-11T15:41:08.437616"}}}