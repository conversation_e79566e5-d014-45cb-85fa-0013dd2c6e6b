[2025-07-18 11:54:28] === 开始新的测试 ===
[2025-07-18 11:54:28] 开始测试
[2025-07-18 11:54:28] 工序: 整机半成品功能测试
[2025-07-18 11:54:28] 序列号: 2222222222222222
[2025-07-18 11:54:28] 开始运行 整机半成品功能测试 工序测试，共 22 个项目
[2025-07-18 11:54:29] 
开始执行: 设备连接状态检测
[2025-07-18 11:54:29] 设备连接测试通过，检测到 1 个设备
[2025-07-18 11:54:29] 设备: ?	device
[2025-07-18 11:54:29] 
开始执行: 3568版本测试
[2025-07-18 11:54:29] 执行3568版本测试...
[2025-07-18 11:54:29] 读取ROM版本号...
[2025-07-18 11:54:29] 执行命令: adb shell uname -a
[2025-07-18 11:54:29] uname命令执行成功
[2025-07-18 11:54:29] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 11:54:29] ✅ 3568版本测试成功
[2025-07-18 11:54:30] ROM版本号: Jul 9 12:01:12
[2025-07-18 11:54:30] ✅ 确认为RK3568平台
[2025-07-18 11:54:30] 
开始执行: USB关键器件检测
[2025-07-18 11:54:30] 执行USB设备检测...
[2025-07-18 11:54:30] 执行命令: adb shell lsusb
[2025-07-18 11:54:30] 设备返回数据:
[2025-07-18 11:54:30] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 11:54:30] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 11:54:30] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 11:54:30] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 11:54:30] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 11:54:30] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 11:54:30] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 11:54:30] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 11:54:30] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 11:54:30] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 11:54:30] 总共解析到 9 个设备
[2025-07-18 11:54:30] ✅ 所有预期的设备ID都已找到
[2025-07-18 11:54:30] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 11:54:30] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 11:54:30] ✅ USB设备检测通过
[2025-07-18 11:54:31] 
开始执行: CAN0测试
[2025-07-18 11:54:31] 执行CAN0测试流程...
[2025-07-18 11:54:31] 执行命令: adb shell ip link set can0 down
[2025-07-18 11:54:31] CAN0已关闭
[2025-07-18 11:54:31] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 11:54:32] CAN0已启动
[2025-07-18 11:54:32] CAN监听线程已启动...
[2025-07-18 11:54:33] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 11:54:33] CAN测试数据已发送，等待监听返回...
[2025-07-18 11:54:33] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 11:54:35] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 11:54:35] 
开始执行: GPS测试
[2025-07-18 11:54:35] 执行GPS测试...
[2025-07-18 11:54:35] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 11:54:45] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 11:54:46] 
开始执行: 4G模组测试
[2025-07-18 11:54:46] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 11:54:46] 监听线程已启动，等待串口数据...
[2025-07-18 11:54:48] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 11:54:48] AT+CCID指令已发送，等待串口返回...
[2025-07-18 11:54:48] 串口输出: AT+CCID
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 13
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: AT+C
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:48] 串口输出: 
[2025-07-18 11:54:49] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: AT+C
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: AT+C
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: AT+C
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:54:49] 串口输出: +CME ERROR: 58
[2025-07-18 11:54:49] 串口输出: 
[2025-07-18 11:55:00] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-18 11:55:01] 
开始执行: 按键测试
[2025-07-18 11:55:01] 开始按键测试...
[2025-07-18 11:55:01] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 11:55:05] 原始事件: Event: time 1752810905.915970, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-18 11:55:05] 解析结果: key_code=662, value=1
[2025-07-18 11:55:05] ✓ 检测到档位减(662)按下
[2025-07-18 11:55:05] 原始事件: Event: time 1752810906.142754, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-18 11:55:05] 解析结果: key_code=662, value=0
[2025-07-18 11:55:05] ✓ 档位减(662)测试通过
[2025-07-18 11:55:06] 原始事件: Event: time 1752810906.816008, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-18 11:55:06] 解析结果: key_code=658, value=1
[2025-07-18 11:55:06] ✓ 检测到喇叭键(658)按下
[2025-07-18 11:55:06] 原始事件: Event: time 1752810906.992652, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-18 11:55:06] 解析结果: key_code=658, value=0
[2025-07-18 11:55:06] ✓ 喇叭键(658)测试通过
[2025-07-18 11:55:06] 按键测试失败 - 只检测到2个按键
[2025-07-18 11:55:10] 
开始执行: 按键灯测试
[2025-07-18 11:55:10] 开始LED背光灯测试...
[2025-07-18 11:55:10] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 11:55:10] LED控制命令执行失败: error: no devices/emulators found

[2025-07-18 11:55:11] 
开始执行: 手电筒测试
[2025-07-18 11:55:11] 开始手电筒LED测试...
[2025-07-18 11:55:11] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 11:55:11] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-18 11:55:12] 
开始执行: 摇杆使能测试
[2025-07-18 11:55:12] 执行摇杆测试...
[2025-07-18 11:55:12] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 11:55:12] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:55:13] 
开始执行: 前摄像头测试
[2025-07-18 11:55:13] 开始执行前摄像头测试...
[2025-07-18 11:55:13] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 11:55:13] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:55:14] 
开始执行: 光感测试
[2025-07-18 11:55:14] 执行光感测试...
[2025-07-18 11:55:14] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 11:55:14] 光感测试失败 - 未检测到数值变化
[2025-07-18 11:55:15] 
开始执行: 回充摄像头测试
[2025-07-18 11:55:15] 开始执行回充摄像头测试...
[2025-07-18 11:55:15] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 11:55:15] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:55:16] 
开始执行: 喇叭测试
[2025-07-18 11:55:16] 执行喇叭测试...
[2025-07-18 11:55:16] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 11:55:16] 命令执行失败: error: no devices/emulators found

[2025-07-18 11:55:16] 
开始执行: 蓝牙测试
[2025-07-18 11:55:16] 执行蓝牙测试...
[2025-07-18 11:55:16] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 11:55:16] 执行命令: adb shell bluetoothctl show
[2025-07-18 11:55:16] ❌ 命令执行失败: error: no devices/emulators found

[2025-07-18 11:55:17] 
开始执行: 4G网络测试
[2025-07-18 11:55:17] 执行4G网络测试...
[2025-07-18 11:55:17] 第一步：关闭WiFi网络...
[2025-07-18 11:55:17] 关闭WiFi失败: error: no devices/emulators found

[2025-07-18 11:55:17] 第三步：重新打开WiFi网络...
[2025-07-18 11:55:17] 重新打开WiFi失败: error: no devices/emulators found

[2025-07-18 11:55:20] 
开始执行: WiFi测试
[2025-07-18 11:55:20] 执行WiFi测试...
[2025-07-18 11:55:20] 第一步：关闭4G网络...
[2025-07-18 11:55:20] 关闭4G网络失败: error: no devices/emulators found

[2025-07-18 11:55:20] 第三步：重新打开4G网络...
[2025-07-18 11:55:20] 重新打开4G网络失败: error: no devices/emulators found

[2025-07-18 11:55:20] 重新打开4G网络时出错: cannot access local variable 'time' where it is not associated with a value
[2025-07-18 11:55:20] 
开始执行: Accel
[2025-07-18 11:55:20] 执行Accel传感器FPS测试...
[2025-07-18 11:55:20] 读取Accel传感器FPS数据...
[2025-07-18 11:55:20] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 11:55:20] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 11:55:21] 
开始执行: Gyro
[2025-07-18 11:55:21] 执行Gyro传感器FPS测试...
[2025-07-18 11:55:21] 读取Gyro传感器FPS数据...
[2025-07-18 11:55:21] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 11:55:21] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 11:55:22] 
开始执行: Laser
[2025-07-18 11:55:22] 执行Laser传感器FPS测试...
[2025-07-18 11:55:22] 读取Laser传感器FPS数据...
[2025-07-18 11:55:22] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 11:55:22] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 11:55:22] 
开始执行: Laser2
[2025-07-18 11:55:22] 执行Laser2传感器FPS测试...
[2025-07-18 11:55:22] 读取Laser2传感器FPS数据...
[2025-07-18 11:55:22] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 11:55:22] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 11:55:23] 
开始执行: Odom
[2025-07-18 11:55:23] 执行Odom传感器FPS测试...
[2025-07-18 11:55:23] 读取Odom传感器FPS数据...
[2025-07-18 11:55:23] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 11:55:23] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 11:55:23] 
测试完成 - 通过率: 4/22
[2025-07-18 11:55:23] ❌ 存在测试失败项！
[2025-07-18 11:55:23] 测试记录已保存: records/2222222222222222_20250718_115523.json
[2025-07-18 11:55:23] 测试日志已保存: records/2222222222222222_20250718_115523.log

