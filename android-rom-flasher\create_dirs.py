import os

# 基础目录
base_dir = "roms"

# 版本和区域
versions = ["工厂版本", "出货版本"]
regions = ["国内版本", "海外版本"]

# 产品型号映射表
model_map = {
    "K03": "招财豹",
    "K08": "招财豹商超版",
    "K13": "豹小秘Plus",
    "K20": "招财豹Pro",
    "K21": "电动门",
    "K22": "Carry",
    "K23": "Carry",
    "K24": "Slim",
    "K26": "豹小递Slim",
    "MC1": "豹小秘MINI",
    "M03": "豹小秘2.0"
}

def create_directory_structure():
    """创建ROM目录结构"""
    # 创建基础目录
    os.makedirs(base_dir, exist_ok=True)
    print(f"创建基础目录: {base_dir}")
    
    # 创建版本目录
    for version in versions:
        version_path = os.path.join(base_dir, version)
        os.makedirs(version_path, exist_ok=True)
        print(f"创建版本目录: {version_path}")
        
        # 在出货版本下创建区域目录
        if version == "出货版本":
            for region in regions:
                region_path = os.path.join(version_path, region)
                os.makedirs(region_path, exist_ok=True)
                print(f"创建区域目录: {region_path}")
                
                # 为每个区域创建产品型号目录
                for model_code, model_name in model_map.items():
                    model_path = os.path.join(region_path, f"{model_name}({model_code})")
                    os.makedirs(model_path, exist_ok=True)
                    print(f"创建产品目录: {model_path}")
        else:
            # 在工厂版本下直接创建产品型号目录
            for model_code, model_name in model_map.items():
                model_path = os.path.join(version_path, f"{model_name}({model_code})")
                os.makedirs(model_path, exist_ok=True)
                print(f"创建产品目录: {model_path}")

if __name__ == "__main__":
    print("开始创建ROM目录结构...")
    create_directory_structure()
    print("\n目录结构创建完成！")
    print("\n目录结构说明：")
    print("1. 工厂版本：存放各型号的工厂版本ROM")
    print("2. 出货版本：")
    print("   - 国内版本：存放各型号的国内出货版本ROM")
    print("   - 海外版本：存放各型号的海外出货版本ROM")
    print("\n每个产品型号目录格式为：产品名称(型号代码)")
    print("例如：招财豹(K03)、豹小秘Plus(K13)等") 