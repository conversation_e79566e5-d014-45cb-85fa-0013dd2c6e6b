{"sn": "2222", "timestamp": "2025-07-05T21:14:54.960114", "results": {"usb_test": {"test_name": "USB关键器件测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "can_test": {"test_name": "CAN0测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "gps_test": {"test_name": "GPS测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "4g_test": {"test_name": "4G模组测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "key_test": {"test_name": "按键测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "led_test": {"test_name": "按键灯测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "torch_test": {"test_name": "手电筒测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-05T21:14:52.676043"}, "light_sensor_test": {"test_name": "光感测试", "status": "失败", "message": "光感异常", "timestamp": "2025-07-05T21:14:53.935841"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "speaker_test": {"test_name": "喇叭测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}, "wifi_test": {"test_name": "WiFi测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:14:52.155119"}}}