[2025-07-11 14:42:01] 开始测试 - SN: 000000
[2025-07-11 14:42:02] 
开始执行: 设备连接状态检测
[2025-07-11 14:42:02] 设备连接测试通过，检测到 1 个设备
[2025-07-11 14:42:02] 设备: ?	device
[2025-07-11 14:42:02] 
开始执行: USB关键器件检测
[2025-07-11 14:42:02] 执行USB设备检测...
[2025-07-11 14:42:02] 执行命令: adb shell lsusb
[2025-07-11 14:42:02] 设备返回数据:
[2025-07-11 14:42:02] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 002: ID 2c7c:0901

[2025-07-11 14:42:02] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-11 14:42:02] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-11 14:42:02] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-11 14:42:02] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-11 14:42:02] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-11 14:42:02] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-11 14:42:02] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-11 14:42:02] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-11 14:42:02] 解析到设备: Bus 003 Device 002 ID 2c7c:0901
[2025-07-11 14:42:02] 总共解析到 9 个设备
[2025-07-11 14:42:02] ✅ 所有预期的设备ID都已找到
[2025-07-11 14:42:02] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 14:42:02] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 14:42:02] ✅ USB设备检测通过
[2025-07-11 14:42:02] 
开始执行: CAN0测试
[2025-07-11 14:42:02] 执行CAN0测试流程...
[2025-07-11 14:42:02] 执行命令: adb shell ip link set can0 down
[2025-07-11 14:42:02] CAN0已关闭
[2025-07-11 14:42:02] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-11 14:42:02] CAN0已启动
[2025-07-11 14:42:02] CAN监听线程已启动...
[2025-07-11 14:42:03] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-11 14:42:03] CAN测试数据已发送，等待监听返回...
[2025-07-11 14:42:03] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-11 14:42:05] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-11 14:42:06] 
开始执行: GPS测试
[2025-07-11 14:42:06] 执行GPS测试...
[2025-07-11 14:42:06] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-11 14:42:16] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-11 14:42:16] 
开始执行: 4G模组测试
[2025-07-11 14:42:16] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-11 14:42:16] 监听线程已启动，等待串口数据...
[2025-07-11 14:42:18] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-11 14:42:18] AT+CCID指令已发送，等待串口返回...
[2025-07-11 14:42:18] 串口输出: AT+CCID
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: +CME ERROR: 13
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: AT+C
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: AT+C
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: AT+C
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: AT+C
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: AT+C
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:18] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:18] 串口输出: 
[2025-07-11 14:42:19] 串口输出: AT+C
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: AT+C
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: AT+C
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: AT+C
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: AT+C
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: AT+C
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:19] 串口输出: +CME ERROR: 58
[2025-07-11 14:42:19] 串口输出: 
[2025-07-11 14:42:31] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-11 14:42:31] 
开始执行: 按键测试
[2025-07-11 14:42:31] 开始按键测试...
[2025-07-11 14:42:31] 执行命令: adb shell evtest /dev/input/event5
[2025-07-11 14:42:35] 原始事件: Event: time 1751299268.907500, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:42:35] 解析结果: key_code=662, value=1
[2025-07-11 14:42:35] ✓ 检测到档位减(662)按下
[2025-07-11 14:42:35] 原始事件: Event: time 1751299269.180823, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:42:35] 解析结果: key_code=662, value=0
[2025-07-11 14:42:35] ✓ 档位减(662)测试通过
[2025-07-11 14:42:44] 原始事件: Event: time 1751299277.507485, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:42:44] 解析结果: key_code=663, value=1
[2025-07-11 14:42:44] ✓ 检测到语音键(663)按下
[2025-07-11 14:42:44] 原始事件: Event: time 1751299277.760790, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:42:44] 解析结果: key_code=663, value=0
[2025-07-11 14:42:44] ✓ 语音键(663)测试通过
[2025-07-11 14:42:45] 原始事件: Event: time 1751299278.207490, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:42:45] 解析结果: key_code=663, value=1
[2025-07-11 14:42:45] ✓ 检测到语音键(663)按下
[2025-07-11 14:42:45] 原始事件: Event: time 1751299278.430846, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:42:45] 解析结果: key_code=663, value=0
[2025-07-11 14:42:45] ✓ 语音键(663)测试通过
[2025-07-11 14:42:46] 原始事件: Event: time 1751299279.330865, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 14:42:46] 解析结果: key_code=657, value=1
[2025-07-11 14:42:46] ✓ 检测到SOS键(657)按下
[2025-07-11 14:42:46] 原始事件: Event: time 1751299279.630826, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 14:42:46] 解析结果: key_code=657, value=0
[2025-07-11 14:42:46] ✓ SOS键(657)测试通过
[2025-07-11 14:42:49] 原始事件: Event: time 1751299282.757417, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:42:49] 解析结果: key_code=660, value=1
[2025-07-11 14:42:49] ✓ 检测到智驾键(660)按下
[2025-07-11 14:42:49] 原始事件: Event: time 1751299282.957527, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:42:49] 解析结果: key_code=660, value=0
[2025-07-11 14:42:49] ✓ 智驾键(660)测试通过
[2025-07-11 14:42:50] 原始事件: Event: time 1751299284.180840, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:42:50] 解析结果: key_code=656, value=1
[2025-07-11 14:42:50] ✓ 检测到锁定键(656)按下
[2025-07-11 14:42:51] 原始事件: Event: time 1751299284.407525, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:42:51] 解析结果: key_code=656, value=0
[2025-07-11 14:42:51] ✓ 锁定键(656)测试通过
[2025-07-11 14:42:52] 原始事件: Event: time 1751299286.080831, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:42:52] 解析结果: key_code=656, value=1
[2025-07-11 14:42:52] ✓ 检测到锁定键(656)按下
[2025-07-11 14:42:53] 原始事件: Event: time 1751299286.357604, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:42:53] 解析结果: key_code=656, value=0
[2025-07-11 14:42:53] ✓ 锁定键(656)测试通过
[2025-07-11 14:42:57] 原始事件: Event: time 1751299290.580823, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:42:57] 解析结果: key_code=663, value=1
[2025-07-11 14:42:57] ✓ 检测到语音键(663)按下
[2025-07-11 14:42:57] 原始事件: Event: time 1751299290.830746, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:42:57] 解析结果: key_code=663, value=0
[2025-07-11 14:42:57] ✓ 语音键(663)测试通过
[2025-07-11 14:42:59] 原始事件: Event: time 1751299292.657519, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:42:59] 解析结果: key_code=662, value=1
[2025-07-11 14:42:59] ✓ 检测到档位减(662)按下
[2025-07-11 14:42:59] 原始事件: Event: time 1751299293.007494, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:42:59] 解析结果: key_code=662, value=0
[2025-07-11 14:42:59] ✓ 档位减(662)测试通过
[2025-07-11 14:43:09] 原始事件: Event: time 1751299302.330810, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:43:09] 解析结果: key_code=662, value=1
[2025-07-11 14:43:09] ✓ 检测到档位减(662)按下
[2025-07-11 14:43:09] 原始事件: Event: time 1751299302.530820, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:43:09] 解析结果: key_code=662, value=0
[2025-07-11 14:43:09] ✓ 档位减(662)测试通过
[2025-07-11 14:43:12] 原始事件: Event: time 1751299305.630806, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:43:12] 解析结果: key_code=663, value=1
[2025-07-11 14:43:12] ✓ 检测到语音键(663)按下
[2025-07-11 14:43:12] 原始事件: Event: time 1751299305.780820, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:43:12] 解析结果: key_code=663, value=0
[2025-07-11 14:43:12] ✓ 语音键(663)测试通过
[2025-07-11 14:43:12] 原始事件: Event: time 1751299306.034152, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:43:12] 解析结果: key_code=663, value=1
[2025-07-11 14:43:12] ✓ 检测到语音键(663)按下
[2025-07-11 14:43:12] 原始事件: Event: time 1751299306.157652, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:43:12] 解析结果: key_code=663, value=0
[2025-07-11 14:43:12] ✓ 语音键(663)测试通过
[2025-07-11 14:43:13] 原始事件: Event: time 1751299306.430818, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:43:13] 解析结果: key_code=663, value=1
[2025-07-11 14:43:13] ✓ 检测到语音键(663)按下
[2025-07-11 14:43:13] 原始事件: Event: time 1751299306.580924, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:43:13] 解析结果: key_code=663, value=0
[2025-07-11 14:43:13] ✓ 语音键(663)测试通过
[2025-07-11 14:43:13] 原始事件: Event: time 1751299307.107612, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:43:13] 解析结果: key_code=660, value=1
[2025-07-11 14:43:13] ✓ 检测到智驾键(660)按下
[2025-07-11 14:43:14] 原始事件: Event: time 1751299307.280816, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:43:14] 解析结果: key_code=660, value=0
[2025-07-11 14:43:14] ✓ 智驾键(660)测试通过
[2025-07-11 14:43:14] 原始事件: Event: time 1751299307.510831, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:43:14] 解析结果: key_code=660, value=1
[2025-07-11 14:43:14] ✓ 检测到智驾键(660)按下
[2025-07-11 14:43:14] 原始事件: Event: time 1751299307.657572, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:43:14] 解析结果: key_code=660, value=0
[2025-07-11 14:43:14] ✓ 智驾键(660)测试通过
[2025-07-11 14:43:15] 原始事件: Event: time 1751299308.610794, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:43:15] 解析结果: key_code=656, value=1
[2025-07-11 14:43:15] ✓ 检测到锁定键(656)按下
[2025-07-11 14:43:15] 原始事件: Event: time 1751299308.830802, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:43:15] 解析结果: key_code=656, value=0
[2025-07-11 14:43:15] ✓ 锁定键(656)测试通过
[2025-07-11 14:43:16] 原始事件: Event: time 1751299309.957518, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 14:43:16] 解析结果: key_code=657, value=1
[2025-07-11 14:43:16] ✓ 检测到SOS键(657)按下
[2025-07-11 14:43:16] 原始事件: Event: time 1751299310.130812, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 14:43:16] 解析结果: key_code=657, value=0
[2025-07-11 14:43:16] ✓ SOS键(657)测试通过
[2025-07-11 14:43:34] 按键测试失败 - 只检测到5个按键
[2025-07-11 14:43:36] 
开始执行: 按键灯测试
[2025-07-11 14:43:36] 开始LED背光灯测试...
[2025-07-11 14:43:36] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-11 14:43:36] LED控制命令执行成功
[2025-07-11 14:43:56] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-11 14:43:57] LED灯已关闭
[2025-07-11 14:43:57] LED测试通过 - 背光灯正常点亮
[2025-07-11 14:43:57] 
开始执行: 手电筒测试
[2025-07-11 14:43:57] 开始手电筒LED测试...
[2025-07-11 14:43:57] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-11 14:43:57] 手电筒控制命令执行成功
[2025-07-11 14:43:58] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-11 14:43:58] 手电筒已关闭
[2025-07-11 14:43:58] 手电筒测试通过 - 手电筒正常点亮
[2025-07-11 14:43:59] 
开始执行: 摇杆使能测试
[2025-07-11 14:43:59] 执行摇杆测试...
[2025-07-11 14:43:59] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-11 14:43:59] 命令执行成功
[2025-07-11 14:43:59] 返回数据: 255
[2025-07-11 14:43:59] 摇杆测试通过，值: 255
[2025-07-11 14:43:59] 
开始执行: 前摄像头测试
[2025-07-11 14:43:59] 开始执行前摄像头测试...
[2025-07-11 14:43:59] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-11 14:43:59] 命令执行成功，返回: 无输出
[2025-07-11 14:43:59] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-11 14:44:00] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.323915616
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-11 14:44:00] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-11 14:44:00] 拉取命令执行成功
[2025-07-11 14:44:00] 返回数据: [ 38%] /data/camera/cam0_3840x2160.jpg
[ 77%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 14.2 MB/s (168873 bytes in 0.011s)
[2025-07-11 14:44:00] 图片已保存: cam0_3840x2160.jpg
[2025-07-11 14:44:02] 用户确认结果: 通过
[2025-07-11 14:44:03] 
开始执行: 光感测试
[2025-07-11 14:44:03] 执行光感测试...
[2025-07-11 14:44:03] 执行命令: adb shell evtest /dev/input/event1
[2025-07-11 14:44:11] 光感测试完成 - 检测到数值变化
[2025-07-11 14:44:11] 数值从 0 变化到 1
[2025-07-11 14:44:11] 
开始执行: 回充摄像头测试
[2025-07-11 14:44:11] 开始执行回充摄像头测试...
[2025-07-11 14:44:11] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-11 14:44:11] 命令执行成功，返回: 无输出
[2025-07-11 14:44:11] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-11 14:44:12] 命令执行成功，返回: 无输出
[2025-07-11 14:44:12] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-11 14:44:12] 拉取命令执行成功
[2025-07-11 14:44:12] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 9.9 MB/s (49296 bytes in 0.005s)
[2025-07-11 14:44:12] 图片已保存: output.jpg
[2025-07-11 14:44:14] 用户确认结果: 通过
[2025-07-11 14:44:14] 
开始执行: 喇叭测试
[2025-07-11 14:44:14] 执行喇叭测试...
[2025-07-11 14:44:14] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-11 14:44:28] 命令执行成功
[2025-07-11 14:44:29] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-11 14:44:29] 音频播放完成
[2025-07-11 14:44:29] 
开始执行: 蓝牙测试
[2025-07-11 14:44:29] 执行蓝牙测试...
[2025-07-11 14:44:29] 启动蓝牙服务...
[2025-07-11 14:44:29] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-11 14:44:29] 开启蓝牙...
[2025-07-11 14:44:29] 执行命令: adb shell bluetoothctl devices
[2025-07-11 14:44:29] 命令执行成功
[2025-07-11 14:44:29] 无输出数据
[2025-07-11 14:44:30] 
开始执行: WiFi测试
[2025-07-11 14:44:30] 执行WiFi测试...
[2025-07-11 14:44:30] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-11 14:44:30] 命令执行成功，无输出
[2025-07-11 14:44:30] 执行命令 2: adb shell rm -f /var/run/wpa_supplicant/wlan0
[2025-07-11 14:44:30] 命令执行成功，无输出
[2025-07-11 14:44:30] 执行命令 3: adb shell ip link set wlan0 down
[2025-07-11 14:44:30] 命令执行成功，无输出
[2025-07-11 14:44:30] 执行命令 4: adb shell wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && wpa_cli -i wlan0 add_network && wpa_cli -i wlan0 set_network 0 ssid '"Orion_SZ_5G"' && wpa_cli -i wlan0 set_network 0 psk '"Orion@2025"' && wpa_cli -i wlan0 enable_network 0 && udhcpc -i wlan0 && iw wlan0 link && ip addr show wlan0
[2025-07-11 14:44:33] 命令执行成功，返回数据:
[2025-07-11 14:44:33]   Successfully initialized wpa_supplicant
[2025-07-11 14:44:33]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-11 14:44:33]   1
[2025-07-11 14:44:33]   OK
[2025-07-11 14:44:34]   OK
[2025-07-11 14:44:34]   OK
[2025-07-11 14:44:34]   deleting routers
[2025-07-11 14:44:34]   adding dns ************
[2025-07-11 14:44:34]   adding dns ***********
[2025-07-11 14:44:34]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-11 14:44:34]   SSID: Orion_SZ_5G
[2025-07-11 14:44:34]   freq: 5300
[2025-07-11 14:44:34]   RX: 2664 bytes (6 packets)
[2025-07-11 14:44:34]   TX: 1928 bytes (11 packets)
[2025-07-11 14:44:34]   signal: -50 dBm
[2025-07-11 14:44:34]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-11 14:44:34]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-11 14:44:34]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-11 14:44:34]   link/ether 24:21:5e:c0:3a:e8 brd ff:ff:ff:ff:ff:ff
[2025-07-11 14:44:34]   inet 192.168.20.181/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-11 14:44:34]   valid_lft forever preferred_lft forever
[2025-07-11 14:44:34]   inet6 fe80::6a36:4efb:41fb:75a2/64 scope link
[2025-07-11 14:44:34]   valid_lft forever preferred_lft forever
[2025-07-11 14:44:34] 执行命令 5: adb shell ping -c 3 www.baidu.com
[2025-07-11 14:44:36] 命令执行成功，返回数据:
[2025-07-11 14:44:36]   PING www.a.shifen.com (157.148.69.151) 56(84) bytes of data.
[2025-07-11 14:44:36]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=1 ttl=54 time=8.76 ms
[2025-07-11 14:44:36]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=2 ttl=54 time=10.3 ms
[2025-07-11 14:44:36]   64 bytes from 157.148.69.151 (157.148.69.151): icmp_seq=3 ttl=54 time=10.7 ms
[2025-07-11 14:44:36]   --- www.a.shifen.com ping statistics ---
[2025-07-11 14:44:36]   3 packets transmitted, 3 received, 0% packet loss, time 2003ms
[2025-07-11 14:44:36]   rtt min/avg/max/mdev = 8.759/9.917/10.715/0.838 ms
[2025-07-11 14:44:36] ✅ ping测试成功，网络连通性正常
[2025-07-11 14:44:36] WiFi连接成功
[2025-07-11 14:44:36] 
测试完成 - 通过率: 11/15
[2025-07-11 14:44:36] ❌ 存在测试失败项！
[2025-07-11 14:44:36] 测试记录已保存: records/000000_20250711_144436.json
[2025-07-11 14:44:36] 测试日志已保存: records/000000_20250711_144436.log

