# Android ROM 刷机工具

这是一个用于Windows平台的Android设备刷机工具，可以自动检测设备连接状态，识别设备型号，并推送对应的ROM包。

## 功能特点

- 自动检测设备连接状态
- 显示设备序列号和型号信息
- 支持选择本地ROM文件
- 图形化操作界面
- 实时显示刷机进度

## 系统要求

- Windows 10 或更高版本
- Python 3.7 或更高版本
- ADB 工具（Android Debug Bridge）
- 已安装设备驱动程序

## 安装步骤

1. 安装 Python 3.7 或更高版本
2. 安装 ADB 工具并添加到系统环境变量
3. 安装项目依赖：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 确保设备已启用USB调试模式
2. 运行程序：
   ```bash
   python main.py
   ```
3. 连接Android设备到电脑
4. 选择ROM文件
5. 点击"开始刷机"按钮

## 注意事项

- 刷机前请备份重要数据
- 确保设备电量充足
- 刷机过程中请勿断开设备连接
- 建议使用原厂数据线进行连接 