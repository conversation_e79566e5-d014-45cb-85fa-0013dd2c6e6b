#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WiFi测试中的4G网络控制功能
验证：
1. WiFi测试前正确关闭4G网络（ifconfig usb0 down）
2. WiFi测试完成后正确恢复4G网络（ifconfig usb0 up）
3. 测试失败或异常时也能正确恢复4G网络
"""

import subprocess
import time
import json
import os

def check_4g_status():
    """检查4G网络状态"""
    try:
        result = subprocess.run(['adb', 'shell', 'ifconfig usb0'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            output = result.stdout.strip()
            if "UP" in output and "RUNNING" in output:
                return "UP"
            else:
                return "DOWN"
        else:
            return "ERROR"
    except Exception as e:
        return f"EXCEPTION: {str(e)}"

def test_4g_control_commands():
    """测试4G网络控制命令"""
    print("=== 测试4G网络控制命令 ===")
    
    # 检查初始状态
    print("1. 检查4G网络初始状态...")
    initial_status = check_4g_status()
    print(f"   初始状态: {initial_status}")
    
    # 测试关闭4G网络
    print("\n2. 测试关闭4G网络...")
    try:
        result = subprocess.run(['adb', 'shell', 'ifconfig usb0 down'], 
                              capture_output=True, text=True, timeout=10)
        print(f"   关闭命令返回码: {result.returncode}")
        if result.stderr:
            print(f"   错误输出: {result.stderr}")
        
        time.sleep(2)
        down_status = check_4g_status()
        print(f"   关闭后状态: {down_status}")
        
    except Exception as e:
        print(f"   关闭4G网络失败: {str(e)}")
    
    # 测试打开4G网络
    print("\n3. 测试打开4G网络...")
    try:
        result = subprocess.run(['adb', 'shell', 'ifconfig usb0 up'], 
                              capture_output=True, text=True, timeout=10)
        print(f"   打开命令返回码: {result.returncode}")
        if result.stderr:
            print(f"   错误输出: {result.stderr}")
        
        time.sleep(2)
        up_status = check_4g_status()
        print(f"   打开后状态: {up_status}")
        
    except Exception as e:
        print(f"   打开4G网络失败: {str(e)}")
    
    return initial_status, down_status, up_status

def simulate_wifi_test():
    """模拟WiFi测试流程"""
    print("\n=== 模拟WiFi测试流程 ===")
    
    print("模拟WiFi测试步骤:")
    print("1. 关闭4G网络 (ifconfig usb0 down)")
    print("2. 连接WiFi网络")
    print("3. 进行网络延时测试")
    print("4. 恢复4G网络 (ifconfig usb0 up)")
    
    # 步骤1：关闭4G网络
    print("\n执行步骤1：关闭4G网络...")
    status_before = check_4g_status()
    print(f"关闭前状态: {status_before}")
    
    try:
        result = subprocess.run(['adb', 'shell', 'ifconfig usb0 down'], 
                              capture_output=True, text=True, timeout=10)
        print(f"关闭命令执行结果: {result.returncode}")
        time.sleep(2)
        
        status_after_down = check_4g_status()
        print(f"关闭后状态: {status_after_down}")
        
    except Exception as e:
        print(f"关闭4G网络失败: {str(e)}")
        return False
    
    # 步骤2-3：模拟WiFi连接和测试（这里只是等待）
    print("\n执行步骤2-3：模拟WiFi连接和网络测试...")
    print("（实际测试中会执行WiFi连接和ping测试）")
    time.sleep(3)
    
    # 步骤4：恢复4G网络
    print("\n执行步骤4：恢复4G网络...")
    try:
        result = subprocess.run(['adb', 'shell', 'ifconfig usb0 up'], 
                              capture_output=True, text=True, timeout=10)
        print(f"恢复命令执行结果: {result.returncode}")
        time.sleep(2)
        
        status_after_up = check_4g_status()
        print(f"恢复后状态: {status_after_up}")
        
        return True
        
    except Exception as e:
        print(f"恢复4G网络失败: {str(e)}")
        return False

def test_wifi_test_integration():
    """测试WiFi测试的完整集成"""
    print("\n=== 测试WiFi测试集成 ===")
    
    # 检查配置文件
    if not os.path.exists('config.json'):
        print("❌ config.json 文件不存在")
        return False
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 查找WiFi测试项目
        wifi_test = None
        for project in config.get("test_projects", []):
            if project.get("id") == "wifi_test":
                wifi_test = project
                break
        
        if not wifi_test:
            print("❌ 未找到WiFi测试项目配置")
            return False
        
        print("✅ 找到WiFi测试项目配置:")
        print(f"   ID: {wifi_test['id']}")
        print(f"   名称: {wifi_test['name']}")
        print(f"   类型: {wifi_test['type']}")
        print(f"   描述: {wifi_test['description']}")
        
        # 检查WiFi设置
        wifi_settings = config.get("wifi_settings", {})
        print(f"\n✅ WiFi设置:")
        print(f"   SSID: {wifi_settings.get('ssid', '未配置')}")
        print(f"   密码: {'已配置' if wifi_settings.get('password') else '未配置'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 读取配置文件失败: {str(e)}")
        return False

def provide_test_guidance():
    """提供测试指导"""
    print("\n📋 WiFi测试4G网络控制功能说明:")
    
    print("\n🔧 修改内容:")
    print("1. WiFi测试前自动关闭4G网络:")
    print("   - 执行命令: adb shell ifconfig usb0 down")
    print("   - 确保WiFi测试时不受4G网络干扰")
    
    print("\n2. WiFi测试后自动恢复4G网络:")
    print("   - 执行命令: adb shell ifconfig usb0 up")
    print("   - 无论测试成功或失败都会恢复")
    
    print("\n3. 异常处理:")
    print("   - WiFi连接失败时恢复4G网络")
    print("   - 网络测试失败时恢复4G网络")
    print("   - 发生异常时恢复4G网络")
    
    print("\n📋 测试流程:")
    print("1. 关闭4G网络 (ifconfig usb0 down)")
    print("2. 停止wpa_supplicant进程")
    print("3. 清理socket文件")
    print("4. 关闭wlan0接口")
    print("5. 连接WiFi网络")
    print("6. 进行10秒ping测试")
    print("7. 计算平均延时")
    print("8. 恢复4G网络 (ifconfig usb0 up)")
    
    print("\n🔍 验证要点:")
    print("✅ 测试开始前4G网络状态为DOWN")
    print("✅ WiFi连接和测试正常进行")
    print("✅ 测试完成后4G网络状态恢复为UP")
    print("✅ 异常情况下4G网络也能正确恢复")

def check_adb_connection():
    """检查ADB连接状态"""
    print("=== 检查ADB连接状态 ===")
    
    try:
        result = subprocess.run(['adb', 'devices'], 
                              capture_output=True, text=True, timeout=5)
        
        if result.returncode == 0:
            output = result.stdout.strip()
            lines = output.split('\n')
            devices = [line for line in lines[1:] if line.strip() and 'device' in line]
            
            if devices:
                print(f"✅ ADB连接正常，检测到 {len(devices)} 个设备:")
                for device in devices:
                    print(f"   {device}")
                return True
            else:
                print("❌ 未检测到ADB设备")
                return False
        else:
            print(f"❌ ADB命令执行失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 检查ADB连接失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("WiFi测试4G网络控制功能验证")
    print("=" * 50)
    
    # 提供测试指导
    provide_test_guidance()
    
    # 检查ADB连接
    if not check_adb_connection():
        print("\n❌ ADB连接检查失败，请确保设备已连接")
        return
    
    # 测试4G控制命令
    initial, down, up = test_4g_control_commands()
    
    # 模拟WiFi测试流程
    wifi_test_success = simulate_wifi_test()
    
    # 测试配置集成
    config_success = test_wifi_test_integration()
    
    # 总结测试结果
    print("\n📊 测试结果总结:")
    print(f"ADB连接: ✅")
    print(f"4G控制命令: {'✅' if down == 'DOWN' and up == 'UP' else '❌'}")
    print(f"WiFi测试流程: {'✅' if wifi_test_success else '❌'}")
    print(f"配置文件集成: {'✅' if config_success else '❌'}")
    
    if down == 'DOWN' and up == 'UP' and wifi_test_success and config_success:
        print("\n🎉 所有测试通过！WiFi测试的4G网络控制功能正常工作")
    else:
        print("\n⚠️ 部分测试未通过，请检查相关配置和连接")
    
    print("\n📝 使用说明:")
    print("- 在实际WiFi测试中，程序会自动控制4G网络")
    print("- 测试前关闭4G，测试后恢复4G")
    print("- 无需手动干预，确保测试结果准确")

if __name__ == "__main__":
    main()
