#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WiFi测试中的4G网络控制功能
验证：
1. WiFi测试前关闭4G网络 (ifconfig usb0 down)
2. WiFi测试完成后打开4G网络 (ifconfig usb0 up)
3. 确保WiFi测试使用WiFi网络而不是4G网络
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import subprocess
import threading
import time
import random

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}, "test_projects": []}

def test_wifi_config():
    """测试WiFi配置更新"""
    print("=== 测试WiFi配置更新 ===")
    
    config = load_config()
    test_projects = config.get("test_projects", [])
    
    # 检查WiFi测试项目配置
    found_wifi_test = False
    for project in test_projects:
        if project.get("id") == "wifi_test":
            found_wifi_test = True
            print(f"✅ 找到WiFi测试项目:")
            print(f"  ID: {project['id']}")
            print(f"  名称: {project['name']}")
            print(f"  描述: {project['description']}")
            print(f"  类型: {project['type']}")
            print(f"  WiFi SSID: {project.get('wifi_ssid', 'N/A')}")
            print(f"  测试时长: {project.get('test_duration', 'N/A')}秒")
            
            # 检查描述是否包含4G网络控制信息
            if "关闭4G网络" in project['description']:
                print("✅ 配置描述已更新，包含4G网络控制信息")
            else:
                print("❌ 配置描述未更新")
            break
    
    if not found_wifi_test:
        print("❌ 未找到WiFi测试项目")
        return False
    
    return True

def simulate_wifi_test_with_4g_control():
    """模拟带4G网络控制的WiFi测试"""
    print("\n=== 模拟WiFi测试中的4G网络控制 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("WiFi测试4G网络控制验证")
    root.geometry("900x700")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 900) // 2
    y = (screen_height - 700) // 2
    root.geometry(f"900x700+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="WiFi测试4G网络控制功能验证", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 测试说明
    info_frame = ttk.LabelFrame(main_frame, text="WiFi测试流程（含4G网络控制）", padding="15")
    info_frame.pack(fill=tk.X, pady=(0, 20))
    
    info_text = """🔧 WiFi测试流程（修改后）:

1. 关闭4G网络:
   • 执行命令: adb shell ifconfig usb0 down
   • 确保网络流量走WiFi网络而不是4G

2. 连接WiFi网络:
   • SSID: Orion_SZ_5G
   • 密码: Orion@2025
   • 等待连接成功

3. WiFi网络ping测试:
   • 目标: ************** (默认)
   • 时长: 10秒
   • 提取延时数据

4. 计算延时统计:
   • 平均延时
   • 最小延时  
   • 最大延时
   • 网络质量评估

5. 恢复4G网络:
   • 执行命令: adb shell ifconfig usb0 up
   • 等待4G网络重新连接

✅ 网络隔离: 确保WiFi测试时不受4G网络干扰"""
    
    ttk.Label(info_frame, text=info_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 状态显示
    status_var = tk.StringVar(value="准备开始WiFi测试...")
    status_label = ttk.Label(main_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack(pady=(0, 15))
    
    # 网络状态显示
    network_frame = ttk.Frame(main_frame)
    network_frame.pack(fill=tk.X, pady=(0, 15))
    
    ttk.Label(network_frame, text="网络状态:", font=("Microsoft YaHei UI", 11, "bold")).pack(side=tk.LEFT)
    
    g4_status_var = tk.StringVar(value="4G: 开启")
    g4_status_label = ttk.Label(network_frame, textvariable=g4_status_var, 
                               font=("Microsoft YaHei UI", 10), foreground="green")
    g4_status_label.pack(side=tk.LEFT, padx=(10, 20))
    
    wifi_status_var = tk.StringVar(value="WiFi: 未连接")
    wifi_status_label = ttk.Label(network_frame, textvariable=wifi_status_var, 
                                 font=("Microsoft YaHei UI", 10), foreground="gray")
    wifi_status_label.pack(side=tk.LEFT)
    
    # 日志显示
    log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    log_text = tk.Text(log_frame, height=15, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message, color=None):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if color:
            start_pos = log_text.index(tk.END + "-1c")
            log_text.insert(tk.END, log_entry)
            end_pos = log_text.index(tk.END + "-1c")
            log_text.tag_add(color, start_pos, end_pos)
            log_text.tag_config(color, foreground=color)
        else:
            log_text.insert(tk.END, log_entry)
        
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 测试状态
    test_state = {"running": False}
    
    def run_wifi_test_simulation():
        """运行WiFi测试模拟（含4G网络控制）"""
        test_state["running"] = True
        status_var.set("正在执行WiFi测试...")
        
        def simulate_test():
            try:
                log_message("执行WiFi测试...", "blue")
                log_message("第一步：关闭4G网络...")
                
                # 模拟关闭4G网络
                log_message("执行命令: adb shell ifconfig usb0 down")
                time.sleep(1)
                g4_status_var.set("4G: 关闭")
                g4_status_label.config(foreground="red")
                log_message("4G网络已关闭，等待网络切换...")
                time.sleep(2)
                
                log_message("第二步：连接WiFi网络...")
                log_message("WiFi SSID: Orion_SZ_5G")
                log_message("正在连接WiFi...")
                time.sleep(2)
                
                wifi_status_var.set("WiFi: 已连接")
                wifi_status_label.config(foreground="green")
                log_message("WiFi连接成功")
                
                log_message("第三步：WiFi网络ping测试...")
                log_message("ping目标: **************")
                log_message("测试时长: 10秒")
                
                # 模拟ping测试
                log_message("执行命令: adb shell ping -c 10 **************")
                
                # 模拟ping输出
                ping_results = []
                for i in range(1, 11):
                    if not test_state["running"]:
                        break
                    
                    # 模拟WiFi延时（通常比4G更低，10-30ms）
                    delay = random.uniform(10.0, 30.0)
                    ping_results.append(delay)
                    
                    log_message(f"64 bytes from **************: icmp_seq={i} ttl=54 time={delay:.1f} ms")
                    time.sleep(0.8)
                
                if test_state["running"] and ping_results:
                    # 计算统计数据
                    avg_delay = sum(ping_results) / len(ping_results)
                    min_delay = min(ping_results)
                    max_delay = max(ping_results)
                    
                    # 判断网络质量
                    if avg_delay <= 50:
                        quality = "优秀"
                        quality_color = "green"
                    elif avg_delay <= 100:
                        quality = "良好"
                        quality_color = "blue"
                    elif avg_delay <= 200:
                        quality = "一般"
                        quality_color = "orange"
                    else:
                        quality = "较差"
                        quality_color = "red"
                    
                    log_message("✅ WiFi延时测试成功", "green")
                    log_message(f"发包数量: {len(ping_results)} 个")
                    log_message(f"平均延时: {avg_delay:.2f} ms")
                    log_message(f"最小延时: {min_delay:.2f} ms")
                    log_message(f"最大延时: {max_delay:.2f} ms")
                    log_message(f"网络质量: {quality}", quality_color)
                    
                    status_var.set(f"WiFi测试通过 - 平均延时: {avg_delay:.1f}ms ({quality})")
                
                log_message("第四步：重新打开4G网络...")
                log_message("执行命令: adb shell ifconfig usb0 up")
                time.sleep(1)
                g4_status_var.set("4G: 开启")
                g4_status_label.config(foreground="green")
                log_message("4G网络已重新打开")
                time.sleep(1)
                log_message("WiFi测试完成", "blue")
                
            except Exception as e:
                log_message(f"WiFi测试出错: {str(e)}", "red")
            finally:
                test_state["running"] = False
        
        threading.Thread(target=simulate_test, daemon=True).start()
    
    def run_real_wifi_test():
        """运行真实的WiFi测试"""
        if not messagebox.askyesno("确认", "这将执行真实的WiFi测试，会临时关闭4G网络。确定继续吗？"):
            return
        
        test_state["running"] = True
        status_var.set("正在执行真实WiFi测试...")
        
        def real_test():
            try:
                log_message("执行真实WiFi测试...", "blue")
                log_message("第一步：关闭4G网络...")
                
                # 真实关闭4G网络
                result = subprocess.run(
                    ["adb", "shell", "ifconfig", "usb0", "down"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if result.returncode == 0:
                    log_message("4G网络已关闭")
                    g4_status_var.set("4G: 关闭")
                    g4_status_label.config(foreground="red")
                else:
                    log_message(f"关闭4G网络失败: {result.stderr}", "red")
                    return
                
                time.sleep(2)
                log_message("第二步：连接WiFi网络...")
                
                # 这里可以添加真实的WiFi连接逻辑
                log_message("WiFi连接逻辑（需要根据实际情况实现）")
                time.sleep(3)
                
                log_message("第三步：WiFi网络ping测试...")
                
                # 真实ping测试
                result = subprocess.run(
                    ["adb", "shell", "ping", "-c", "10", "**************"],
                    capture_output=True,
                    text=True,
                    timeout=20
                )
                
                if result.returncode == 0:
                    ping_output = result.stdout
                    log_message("ping测试输出:")
                    for line in ping_output.split('\n'):
                        if line.strip():
                            log_message(f"  {line}")
                    
                    # 解析延时数据
                    import re
                    time_pattern = re.compile(r'time=(\d+\.?\d*)\s*ms')
                    delays = []
                    
                    for line in ping_output.split('\n'):
                        if "64 bytes from" in line and "time=" in line:
                            match = time_pattern.search(line)
                            if match:
                                delay = float(match.group(1))
                                delays.append(delay)
                    
                    if delays:
                        avg_delay = sum(delays) / len(delays)
                        min_delay = min(delays)
                        max_delay = max(delays)
                        
                        # 判断网络质量
                        if avg_delay <= 50:
                            quality = "优秀"
                        elif avg_delay <= 100:
                            quality = "良好"
                        elif avg_delay <= 200:
                            quality = "一般"
                        else:
                            quality = "较差"
                        
                        log_message("✅ WiFi延时测试成功", "green")
                        log_message(f"发包数量: {len(delays)} 个")
                        log_message(f"平均延时: {avg_delay:.2f} ms")
                        log_message(f"最小延时: {min_delay:.2f} ms")
                        log_message(f"最大延时: {max_delay:.2f} ms")
                        log_message(f"网络质量: {quality}")
                        
                        status_var.set(f"WiFi测试通过 - 平均延时: {avg_delay:.1f}ms ({quality})")
                    else:
                        log_message("未能解析到延时数据", "red")
                else:
                    log_message(f"ping测试失败: {result.stderr}", "red")
                
            except subprocess.TimeoutExpired:
                log_message("WiFi测试超时", "red")
            except Exception as e:
                log_message(f"WiFi测试出错: {str(e)}", "red")
            finally:
                # 恢复4G网络
                try:
                    log_message("第四步：重新打开4G网络...")
                    result = subprocess.run(
                        ["adb", "shell", "ifconfig", "usb0", "up"],
                        capture_output=True,
                        text=True,
                        timeout=10
                    )
                    
                    if result.returncode == 0:
                        log_message("4G网络已重新打开")
                        g4_status_var.set("4G: 开启")
                        g4_status_label.config(foreground="green")
                    else:
                        log_message(f"重新打开4G网络失败: {result.stderr}", "red")
                    
                    time.sleep(2)
                    
                except Exception as e:
                    log_message(f"重新打开4G网络时出错: {str(e)}", "red")
                
                test_state["running"] = False
        
        threading.Thread(target=real_test, daemon=True).start()
    
    def stop_test():
        """停止测试"""
        test_state["running"] = False
        log_message("测试已停止")
        status_var.set("测试已停止")
    
    def clear_log():
        """清空日志"""
        log_text.delete(1.0, tk.END)
        status_var.set("准备开始WiFi测试...")
        g4_status_var.set("4G: 开启")
        g4_status_label.config(foreground="green")
        wifi_status_var.set("WiFi: 未连接")
        wifi_status_label.config(foreground="gray")
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X)
    
    ttk.Button(button_frame, text="模拟WiFi测试", 
              command=run_wifi_test_simulation, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="真实WiFi测试", 
              command=run_real_wifi_test, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="停止测试", 
              command=stop_test, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="清空日志", 
              command=clear_log, width=12).pack(side=tk.RIGHT, padx=5)
    
    # 初始化日志
    log_message("WiFi测试4G网络控制功能验证已准备就绪")
    log_message("点击'模拟WiFi测试'查看测试流程")
    log_message("点击'真实WiFi测试'执行实际测试（需要ADB连接）")
    
    print("WiFi测试4G网络控制验证界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("WiFi测试4G网络控制功能验证")
    print("=" * 50)
    
    print("🔧 验证内容:")
    print("  1. WiFi测试前关闭4G网络 (ifconfig usb0 down)")
    print("  2. WiFi测试完成后打开4G网络 (ifconfig usb0 up)")
    print("  3. 确保WiFi测试使用WiFi网络而不是4G网络")
    print("  4. 网络切换的完整流程验证")
    
    # 测试配置
    if test_wifi_config():
        print("\n✅ WiFi配置验证通过")
    else:
        print("\n❌ WiFi配置验证失败")
    
    # 创建模拟测试界面
    simulate_wifi_test_with_4g_control()
    
    print("\n测试完成")
