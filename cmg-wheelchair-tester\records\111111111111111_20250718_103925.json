{"sn": "111111111111111", "timestamp": "2025-07-18T10:39:25.176292", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-18T10:37:26.842502"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-18T10:37:27.558459"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-18T10:37:28.411997"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-18T10:37:28.989167"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-18T10:37:32.653388"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-18T10:37:43.327784"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-18T10:37:58.143446"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-18T10:38:29.289882"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-18T10:38:34.102142"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-18T10:38:35.459236"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T10:38:36.011358"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-18T10:38:40.832794"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T10:38:45.420625"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-18T10:38:53.501402"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 24:21:5E:C0:2A:83", "timestamp": "2025-07-18T10:39:08.000390"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "平均延时: 11.08ms (优秀)", "timestamp": "2025-07-18T10:39:08.519384"}}}