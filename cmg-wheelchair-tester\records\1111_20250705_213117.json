{"sn": "1111", "timestamp": "2025-07-05T21:31:17.952799", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "失败", "message": "未连接设备", "timestamp": "2025-07-05T21:31:15.904081"}, "usb_test": {"test_name": "USB关键器件检测", "status": "失败", "message": "未知的测试类型", "timestamp": "2025-07-05T21:31:16.924354"}, "can_test": {"test_name": "CAN0测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "gps_test": {"test_name": "GPS测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "4g_test": {"test_name": "4G模组测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "key_test": {"test_name": "按键测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "led_test": {"test_name": "按键灯测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "torch_test": {"test_name": "手电筒测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "light_sensor_test": {"test_name": "光感测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "speaker_test": {"test_name": "喇叭测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}, "wifi_test": {"test_name": "WiFi测试", "status": "未测试", "message": "", "timestamp": "2025-07-05T21:31:15.813418"}}}