[2025-07-17 10:46:53] 开始测试 - SN: 2222222222222222
[2025-07-17 10:46:53] 
开始执行: 设备连接状态检测
[2025-07-17 10:46:53] 设备连接测试通过，检测到 1 个设备
[2025-07-17 10:46:53] 设备: ?	device
[2025-07-17 10:46:53] 
开始执行: USB关键器件检测
[2025-07-17 10:46:53] 执行USB设备检测...
[2025-07-17 10:46:53] 执行命令: adb shell lsusb
[2025-07-17 10:46:53] 设备返回数据:
[2025-07-17 10:46:53] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-17 10:46:53] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-17 10:46:53] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-17 10:46:53] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-17 10:46:53] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-17 10:46:53] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-17 10:46:53] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-17 10:46:53] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-17 10:46:53] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-17 10:46:53] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-17 10:46:53] 总共解析到 9 个设备
[2025-07-17 10:46:53] ✅ 所有预期的设备ID都已找到
[2025-07-17 10:46:53] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-17 10:46:53] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-17 10:46:53] ✅ USB设备检测通过
[2025-07-17 10:46:54] 
开始执行: CAN0测试
[2025-07-17 10:46:54] 执行CAN0测试流程...
[2025-07-17 10:46:54] 执行命令: adb shell ip link set can0 down
[2025-07-17 10:46:54] CAN0已关闭
[2025-07-17 10:46:54] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-17 10:46:54] CAN0已启动
[2025-07-17 10:46:54] CAN监听线程已启动...
[2025-07-17 10:46:55] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-17 10:46:55] CAN测试数据已发送，等待监听返回...
[2025-07-17 10:46:55] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-17 10:46:57] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-17 10:46:57] 
开始执行: GPS测试
[2025-07-17 10:46:58] 执行GPS测试...
[2025-07-17 10:46:58] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-17 10:47:08] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-17 10:47:08] 
开始执行: 4G模组测试
[2025-07-17 10:47:08] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-17 10:47:08] 监听线程已启动，等待串口数据...
[2025-07-17 10:47:10] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-17 10:47:10] AT+CCID指令已发送，等待串口返回...
[2025-07-17 10:47:10] 串口输出: AT+CCID
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:10] 串口输出: +CME ERROR: 13
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:10] 串口输出: AT+C
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:10] 串口输出: +CME ERROR: 58
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:10] 串口输出: AT+C
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:10] 串口输出: +CME ERROR: 58
[2025-07-17 10:47:10] 串口输出: 
[2025-07-17 10:47:22] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-17 10:47:23] 
开始执行: 按键测试
[2025-07-17 10:47:23] 开始按键测试...
[2025-07-17 10:47:23] 执行命令: adb shell evtest /dev/input/event5
[2025-07-17 10:47:25] 原始事件: Event: time 1751299435.326525, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-17 10:47:25] 解析结果: key_code=662, value=1
[2025-07-17 10:47:25] ✓ 检测到档位减(662)按下
[2025-07-17 10:47:25] 原始事件: Event: time 1751299435.502922, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-17 10:47:25] 解析结果: key_code=662, value=0
[2025-07-17 10:47:25] ✓ 档位减(662)测试通过
[2025-07-17 10:47:25] 原始事件: Event: time 1751299436.199602, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-17 10:47:25] 解析结果: key_code=661, value=1
[2025-07-17 10:47:25] ✓ 检测到静音键(661)按下
[2025-07-17 10:47:26] 原始事件: Event: time 1751299436.372935, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-17 10:47:26] 解析结果: key_code=661, value=0
[2025-07-17 10:47:26] ✓ 静音键(661)测试通过
[2025-07-17 10:47:26] 原始事件: Event: time 1751299436.849591, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-17 10:47:26] 解析结果: key_code=662, value=1
[2025-07-17 10:47:26] ✓ 检测到档位减(662)按下
[2025-07-17 10:47:26] 原始事件: Event: time 1751299436.949586, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-17 10:47:26] 解析结果: key_code=662, value=0
[2025-07-17 10:47:26] ✓ 档位减(662)测试通过
[2025-07-17 10:47:27] 原始事件: Event: time 1751299437.326241, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-17 10:47:27] 解析结果: key_code=659, value=1
[2025-07-17 10:47:27] ✓ 检测到档位加(659)按下
[2025-07-17 10:47:27] 原始事件: Event: time 1751299437.449561, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-17 10:47:27] 解析结果: key_code=659, value=0
[2025-07-17 10:47:27] ✓ 档位加(659)测试通过
[2025-07-17 10:47:27] 原始事件: Event: time 1751299437.749666, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-17 10:47:27] 解析结果: key_code=660, value=1
[2025-07-17 10:47:27] ✓ 检测到智驾键(660)按下
[2025-07-17 10:47:27] 原始事件: Event: time 1751299437.899624, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-17 10:47:27] 解析结果: key_code=660, value=0
[2025-07-17 10:47:27] ✓ 智驾键(660)测试通过
[2025-07-17 10:47:29] 原始事件: Event: time 1751299440.049629, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-17 10:47:29] 解析结果: key_code=657, value=1
[2025-07-17 10:47:29] ✓ 检测到SOS键(657)按下
[2025-07-17 10:47:29] 原始事件: Event: time 1751299440.222893, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-17 10:47:29] 解析结果: key_code=657, value=0
[2025-07-17 10:47:29] ✓ SOS键(657)测试通过
[2025-07-17 10:47:30] 原始事件: Event: time 1751299440.672964, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-17 10:47:30] 解析结果: key_code=656, value=1
[2025-07-17 10:47:30] ✓ 检测到锁定键(656)按下
[2025-07-17 10:47:30] 原始事件: Event: time 1751299440.849573, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-17 10:47:30] 解析结果: key_code=656, value=0
[2025-07-17 10:47:30] ✓ 锁定键(656)测试通过
[2025-07-17 10:47:30] 原始事件: Event: time 1751299441.272936, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-17 10:47:30] 解析结果: key_code=657, value=1
[2025-07-17 10:47:30] ✓ 检测到SOS键(657)按下
[2025-07-17 10:47:31] 原始事件: Event: time 1751299441.372985, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-17 10:47:31] 解析结果: key_code=657, value=0
[2025-07-17 10:47:31] ✓ SOS键(657)测试通过
[2025-07-17 10:47:33] 原始事件: Event: time 1751299443.502911, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-17 10:47:33] 解析结果: key_code=658, value=1
[2025-07-17 10:47:33] ✓ 检测到喇叭键(658)按下
[2025-07-17 10:47:33] 原始事件: Event: time 1751299443.700153, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-17 10:47:33] 解析结果: key_code=658, value=0
[2025-07-17 10:47:33] ✓ 喇叭键(658)测试通过
[2025-07-17 10:47:37] 原始事件: Event: time 1751299447.473082, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-17 10:47:37] 解析结果: key_code=663, value=1
[2025-07-17 10:47:37] ✓ 检测到语音键(663)按下
[2025-07-17 10:47:37] 原始事件: Event: time 1751299447.649555, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-17 10:47:37] 解析结果: key_code=663, value=0
[2025-07-17 10:47:37] ✓ 语音键(663)测试通过
[2025-07-17 10:47:37] 按键测试完成 - 检测到8个按键
[2025-07-17 10:47:40] 
开始执行: 按键灯测试
[2025-07-17 10:47:40] 开始LED背光灯测试...
[2025-07-17 10:47:40] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-17 10:47:40] LED控制命令执行成功
[2025-07-17 10:47:40] 🔧 显示确认对话框: LED测试确认
[2025-07-17 10:47:40] 🔧 对话框窗口已创建
[2025-07-17 10:47:40] 🔧 '是'按钮已创建
[2025-07-17 10:47:40] 🔧 '否'按钮已创建
[2025-07-17 10:47:40] 🔧 对话框显示完成，等待用户响应...
[2025-07-17 10:47:44] 👤 用户选择: 是 (测试通过)
[2025-07-17 10:47:44] 🔧 对话框关闭，用户响应: yes
[2025-07-17 10:47:44] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-17 10:47:44] LED灯已关闭
[2025-07-17 10:47:44] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-17 10:47:44] 
开始执行: 手电筒测试
[2025-07-17 10:47:44] 开始手电筒LED测试...
[2025-07-17 10:47:44] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-17 10:47:44] 手电筒控制命令执行成功
[2025-07-17 10:47:44] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-17 10:47:44] 🔧 对话框窗口已创建
[2025-07-17 10:47:44] 🔧 '是'按钮已创建
[2025-07-17 10:47:45] 🔧 '否'按钮已创建
[2025-07-17 10:47:45] 🔧 对话框显示完成，等待用户响应...
[2025-07-17 10:47:46] 👤 用户选择: 是 (测试通过)
[2025-07-17 10:47:46] 🔧 对话框关闭，用户响应: yes
[2025-07-17 10:47:46] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-17 10:47:46] 手电筒已关闭
[2025-07-17 10:47:46] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-17 10:47:46] 
开始执行: 摇杆使能测试
[2025-07-17 10:47:46] 执行摇杆测试...
[2025-07-17 10:47:46] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-17 10:47:46] 命令执行成功
[2025-07-17 10:47:46] 返回数据: 255
[2025-07-17 10:47:46] 摇杆测试通过，值: 255
[2025-07-17 10:47:47] 
开始执行: 前摄像头测试
[2025-07-17 10:47:47] 开始执行前摄像头测试...
[2025-07-17 10:47:47] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-17 10:47:47] 命令执行成功，返回: 无输出
[2025-07-17 10:47:47] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-17 10:47:48] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.326167199
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-17 10:47:48] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-17 10:47:48] 拉取命令执行成功
[2025-07-17 10:47:48] 返回数据: [ 34%] /data/camera/cam0_3840x2160.jpg
[ 69%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 16.0 MB/s (189549 bytes in 0.011s)
[2025-07-17 10:47:48] 图片已保存: cam0_3840x2160.jpg
[2025-07-17 10:47:50] 用户确认结果: 通过
[2025-07-17 10:47:50] 
开始执行: 光感测试
[2025-07-17 10:47:50] 执行光感测试...
[2025-07-17 10:47:51] 执行命令: adb shell evtest /dev/input/event1
[2025-07-17 10:47:54] 光感测试完成 - 检测到数值变化
[2025-07-17 10:47:54] 数值从 0 变化到 2
[2025-07-17 10:47:54] 
开始执行: 回充摄像头测试
[2025-07-17 10:47:54] 开始执行回充摄像头测试...
[2025-07-17 10:47:54] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-17 10:47:54] 命令执行成功，返回: 无输出
[2025-07-17 10:47:54] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-17 10:47:55] 命令执行成功，返回: 无输出
[2025-07-17 10:47:55] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-17 10:47:55] 拉取命令执行成功
[2025-07-17 10:47:55] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 3.5 MB/s (7440 bytes in 0.002s)
[2025-07-17 10:47:55] 图片已保存: output.jpg
[2025-07-17 10:48:12] 用户确认结果: 通过
[2025-07-17 10:48:12] 
开始执行: 喇叭测试
[2025-07-17 10:48:12] 执行喇叭测试...
[2025-07-17 10:48:12] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-17 10:48:26] 命令执行成功
[2025-07-17 10:48:26] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-17 10:48:26] 音频播放完成
[2025-07-17 10:48:26] 
开始执行: 蓝牙测试
[2025-07-17 10:48:26] 执行蓝牙测试...
[2025-07-17 10:48:26] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-17 10:48:26] 执行命令: adb shell bluetoothctl show
[2025-07-17 10:48:26] 命令执行成功
[2025-07-17 10:48:26] 返回数据:
[2025-07-17 10:48:26]   Controller 40:55:48:07:E4:2D (public)
[2025-07-17 10:48:26]   Name: CMG-1
[2025-07-17 10:48:26]   Alias: CMG-1
[2025-07-17 10:48:26]   Class: 0x006c0000 (7077888)
[2025-07-17 10:48:26]   Powered: yes
[2025-07-17 10:48:26]   PowerState: on
[2025-07-17 10:48:26]   Discoverable: no
[2025-07-17 10:48:26]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-17 10:48:26]   Pairable: yes
[2025-07-17 10:48:26]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-17 10:48:26]   Modalias: usb:v1D6Bp0246d0544
[2025-07-17 10:48:26]   Discovering: no
[2025-07-17 10:48:26]   Roles: central
[2025-07-17 10:48:26]   Roles: peripheral
[2025-07-17 10:48:26]   Advertising Features:
[2025-07-17 10:48:26]   ActiveInstances: 0x00 (0)
[2025-07-17 10:48:26]   SupportedInstances: 0x10 (16)
[2025-07-17 10:48:26]   SupportedIncludes: tx-power
[2025-07-17 10:48:26]   SupportedIncludes: appearance
[2025-07-17 10:48:26]   SupportedIncludes: local-name
[2025-07-17 10:48:26]   SupportedSecondaryChannels: 1M
[2025-07-17 10:48:26]   SupportedSecondaryChannels: 2M
[2025-07-17 10:48:26]   SupportedSecondaryChannels: Coded
[2025-07-17 10:48:26]   SupportedCapabilities Key: MaxAdvLen
[2025-07-17 10:48:26]   SupportedCapabilities Value: 0x1f (31)
[2025-07-17 10:48:26]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-17 10:48:26]   SupportedCapabilities Value: 0x1f (31)
[2025-07-17 10:48:26]   SupportedFeatures: CanSetTxPower
[2025-07-17 10:48:26]   SupportedFeatures: HardwareOffload
[2025-07-17 10:48:26]   Advertisement Monitor Features:
[2025-07-17 10:48:26]   SupportedMonitorTypes: or_patterns
[2025-07-17 10:48:26] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-17 10:48:26] 蓝牙控制器MAC地址: 40:55:48:07:E4:2D
[2025-07-17 10:48:27] 
开始执行: WiFi测试
[2025-07-17 10:48:27] 执行WiFi测试...
[2025-07-17 10:48:27] 开始网络发包延时测试...
[2025-07-17 10:48:27] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-17 10:48:27] 正在进行10秒钟的网络延时测试...
[2025-07-17 10:48:27] ❌ ping命令执行失败: ping: www.baidu.com: Temporary failure in name resolution

[2025-07-17 10:48:27] 
测试完成 - 通过率: 12/15
[2025-07-17 10:48:27] ❌ 存在测试失败项！
[2025-07-17 10:48:27] 测试记录已保存: records/2222222222222222_20250717_104827.json
[2025-07-17 10:48:27] 测试日志已保存: records/2222222222222222_20250717_104827.log

