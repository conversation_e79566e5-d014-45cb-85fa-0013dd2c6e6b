# WiFi测试4G网络控制功能实现总结

## 需求说明

用户要求在WiFi测试前关闭4G网络，测试完成后重新打开4G网络：
- **关闭4G网络**: `adb shell ifconfig usb0 down`
- **打开4G网络**: `adb shell ifconfig usb0 up`

## 实现方案

### 1. WiFi测试流程修改

#### **修改前的流程**
```
WiFi测试开始 → 连接WiFi → 网络延时测试 → 测试结束
```

#### **修改后的流程**
```
WiFi测试开始 → 关闭4G网络 → 连接WiFi → 网络延时测试 → 恢复4G网络 → 测试结束
```

### 2. 具体代码修改

#### **步骤0：关闭4G网络**
```python
# 步骤0：关闭4G网络（usb0接口）
self.log_message("第一步：关闭4G网络...")
self.log_message("关闭usb0接口（4G网络）...")
cmd_4g_down = "ifconfig usb0 down"
self.log_message(f"执行命令: adb shell {cmd_4g_down}")

result_4g_down = subprocess.run(['adb', 'shell', cmd_4g_down], 
                              capture_output=True, text=True, timeout=10)

if result_4g_down.returncode == 0:
    self.log_message("✅ 4G网络已关闭")
else:
    self.log_message(f"⚠️ 关闭4G网络时出现警告: {result_4g_down.stderr}")
    self.log_message("继续执行WiFi测试...")

# 等待网络状态稳定
import time
time.sleep(2)
```

#### **测试完成后恢复4G网络**
```python
def _restore_4g_network(self):
    """恢复4G网络连接"""
    try:
        self.log_message("第三步：恢复4G网络...")
        self.log_message("重新打开usb0接口（4G网络）...")
        cmd_4g_up = "ifconfig usb0 up"
        self.log_message(f"执行命令: adb shell {cmd_4g_up}")
        
        result_4g_up = subprocess.run(['adb', 'shell', cmd_4g_up], 
                                    capture_output=True, text=True, timeout=10)
        
        if result_4g_up.returncode == 0:
            self.log_message("✅ 4G网络已恢复")
        else:
            self.log_message(f"⚠️ 恢复4G网络时出现警告: {result_4g_up.stderr}")
        
        # 等待网络状态稳定
        import time
        time.sleep(2)
        
    except Exception as e:
        self.log_message(f"恢复4G网络时出错: {str(e)}")
```

### 3. 异常处理机制

#### **所有退出点都恢复4G网络**

1. **WiFi连接失败**
```python
if wifi_result.returncode != 0:
    self.log_message(f"❌ WiFi连接失败: {wifi_result.stderr}")
    self.test_results[test_project["id"]].message = "WiFi连接失败"
    # WiFi连接失败也要恢复4G网络
    self._restore_4g_network()
    return False
```

2. **网络测试成功**
```python
self.test_results[test_project["id"]].message = f"平均延时: {avg_delay:.2f}ms ({quality})"

# WiFi测试成功，重新打开4G网络
self._restore_4g_network()
return True
```

3. **网络测试失败**
```python
else:
    self.log_message("❌ 未检测到有效的延时数据")
    self.test_results[test_project["id"]].message = "未检测到延时数据"
    # 测试失败也要恢复4G网络
    self._restore_4g_network()
    return False
```

4. **异常情况**
```python
except Exception as e:
    self.log_message(f"WiFi测试出错: {str(e)}")
    self.test_results[test_project["id"]].message = f"错误: {str(e)}"
    # 异常情况也要恢复4G网络
    self._restore_4g_network()
    return False
```

## 完整的WiFi测试流程

### 1. 测试步骤详解

```
第一步：关闭4G网络
├── 执行命令: adb shell ifconfig usb0 down
├── 检查执行结果
├── 等待网络状态稳定（2秒）
└── 记录日志

第二步：连接WiFi网络
├── 停止现有wpa_supplicant进程
├── 清理socket文件
├── 关闭wlan0接口
├── 连接WiFi网络（使用配置的SSID和密码）
├── 获取IP地址
└── 验证连接状态

第三步：网络延时测试
├── 执行ping命令（10次）
├── 解析延时数据
├── 计算平均延时
├── 评估网络质量
└── 记录测试结果

第四步：恢复4G网络
├── 执行命令: adb shell ifconfig usb0 up
├── 检查执行结果
├── 等待网络状态稳定（2秒）
└── 记录日志
```

### 2. 日志输出示例

```
[14:30:15] 执行WiFi测试...
[14:30:15] 第一步：关闭4G网络...
[14:30:15] 关闭usb0接口（4G网络）...
[14:30:15] 执行命令: adb shell ifconfig usb0 down
[14:30:16] ✅ 4G网络已关闭
[14:30:18] 第二步：连接WiFi网络...
[14:30:18] 停止现有的wpa_supplicant进程...
[14:30:18] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[14:30:19] 清理wpa_supplicant socket文件...
[14:30:19] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[14:30:20] 关闭wlan0接口...
[14:30:20] 执行命令: adb shell ip link set wlan0 down
[14:30:21] 连接WiFi网络...
[14:30:21] 执行WiFi连接命令...
[14:30:21] SSID: Orion_SZ_5G
[14:30:35] WiFi连接命令执行完成，返回数据:
[14:30:35]   Successfully initialized wpa_supplicant
[14:30:35]   Selected interface 'wlan0'
[14:30:35]   0
[14:30:35]   OK
[14:30:35]   OK
[14:30:35]   OK
[14:30:35]   udhcpc: started, v1.35.0
[14:30:35]   udhcpc: broadcasting discover
[14:30:35]   udhcpc: broadcasting discover
[14:30:35]   udhcpc: no lease, failing
[14:30:35]   Connected to aa:bb:cc:dd:ee:ff (on wlan0)
[14:30:35]   SSID: Orion_SZ_5G
[14:30:35]   freq: 5745
[14:30:35]   RX: 156 CCMP
[14:30:35]   TX: 156 CCMP
[14:30:35]   signal: -45 dBm
[14:30:35]   rx bitrate: 866.7 MBit/s VHT-MCS 9 80MHz short GI VHT-NSS 2
[14:30:35]   tx bitrate: 866.7 MBit/s VHT-MCS 9 80MHz short GI VHT-NSS 2
[14:30:35]   bss flags:	short-slot-time
[14:30:35]   dtim period:	1
[14:30:35]   beacon int:	100
[14:30:35]   3: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[14:30:35]       link/ether 12:34:56:78:9a:bc brd ff:ff:ff:ff:ff:ff
[14:30:35]       inet 192.168.1.100/24 brd 192.168.1.255 scope global wlan0
[14:30:35]          valid_lft forever preferred_lft forever
[14:30:35] ✅ WiFi连接成功
[14:30:38] 第二步：开始网络发包延时测试...
[14:30:38] 执行命令: adb shell ping -c 10 www.baidu.com
[14:30:38] 正在进行10秒钟的网络延时测试...
[14:30:48] ping命令执行成功
[14:30:48] 返回数据:
[14:30:48]   PING www.a.shifen.com (**************): 56 data bytes
[14:30:48]   64 bytes from **************: seq=0 ttl=54 time=12.5 ms
[14:30:48]   64 bytes from **************: seq=1 ttl=54 time=11.8 ms
[14:30:48]   64 bytes from **************: seq=2 ttl=54 time=13.2 ms
[14:30:48]   64 bytes from **************: seq=3 ttl=54 time=10.9 ms
[14:30:48]   64 bytes from **************: seq=4 ttl=54 time=12.1 ms
[14:30:48]   64 bytes from **************: seq=5 ttl=54 time=11.5 ms
[14:30:48]   64 bytes from **************: seq=6 ttl=54 time=13.8 ms
[14:30:48]   64 bytes from **************: seq=7 ttl=54 time=12.3 ms
[14:30:48]   64 bytes from **************: seq=8 ttl=54 time=11.7 ms
[14:30:48]   64 bytes from **************: seq=9 ttl=54 time=12.6 ms
[14:30:48]   
[14:30:48]   --- www.a.shifen.com ping statistics ---
[14:30:48]   10 packets transmitted, 10 received, 0% packet loss
[14:30:48]   round-trip min/avg/max = 10.9/12.2/13.8 ms
[14:30:48] 检测到延时: 12.5 ms
[14:30:48] 检测到延时: 11.8 ms
[14:30:48] 检测到延时: 13.2 ms
[14:30:48] 检测到延时: 10.9 ms
[14:30:48] 检测到延时: 12.1 ms
[14:30:48] 检测到延时: 11.5 ms
[14:30:48] 检测到延时: 13.8 ms
[14:30:48] 检测到延时: 12.3 ms
[14:30:48] 检测到延时: 11.7 ms
[14:30:48] 检测到延时: 12.6 ms
[14:30:48] ✅ WiFi延时测试成功
[14:30:48] 发包数量: 10 个
[14:30:48] 平均延时: 12.24 ms
[14:30:48] 最小延时: 10.90 ms
[14:30:48] 最大延时: 13.80 ms
[14:30:48] 第三步：恢复4G网络...
[14:30:48] 重新打开usb0接口（4G网络）...
[14:30:48] 执行命令: adb shell ifconfig usb0 up
[14:30:49] ✅ 4G网络已恢复
```

## 关键特性

### 1. 自动化控制
- ✅ **无需手动干预**：程序自动控制4G网络开关
- ✅ **时序控制**：确保网络状态切换的时序正确
- ✅ **状态等待**：每次网络状态改变后等待2秒稳定

### 2. 完善的异常处理
- ✅ **WiFi连接失败**：自动恢复4G网络
- ✅ **网络测试失败**：自动恢复4G网络
- ✅ **程序异常**：自动恢复4G网络
- ✅ **命令执行失败**：记录警告但继续执行

### 3. 详细的日志记录
- ✅ **命令执行日志**：记录每个命令的执行过程
- ✅ **状态变化日志**：记录网络状态的变化
- ✅ **错误处理日志**：记录异常和错误信息
- ✅ **测试结果日志**：记录详细的测试数据

### 4. 网络质量评估
- ✅ **延时统计**：平均、最小、最大延时
- ✅ **质量评级**：优秀(≤50ms)、良好(≤100ms)、一般(≤200ms)、较差(>200ms)
- ✅ **丢包检测**：检测网络连接的稳定性

## 使用说明

### 1. 配置要求
- 确保设备通过ADB连接
- 确保设备有usb0接口（4G网络）
- 确保设备有wlan0接口（WiFi网络）
- 在config.json中配置WiFi信息

### 2. 测试执行
- 选择包含WiFi测试的工序
- 程序会自动执行完整的测试流程
- 无需手动控制4G网络开关

### 3. 结果判断
- **测试通过**：WiFi连接成功且网络延时正常
- **测试失败**：WiFi连接失败或网络延时异常
- **4G网络**：无论测试结果如何都会自动恢复

## 总结

通过这次修改，WiFi测试现在具备了完整的4G网络控制功能：

✅ **测试前自动关闭4G网络**，避免网络干扰  
✅ **测试后自动恢复4G网络**，确保设备正常使用  
✅ **完善的异常处理机制**，确保4G网络始终能恢复  
✅ **详细的日志记录**，便于问题诊断和结果分析  
✅ **自动化执行**，无需手动干预  

这样的实现确保了WiFi测试的准确性和可靠性，同时保证了设备网络功能的完整性。
