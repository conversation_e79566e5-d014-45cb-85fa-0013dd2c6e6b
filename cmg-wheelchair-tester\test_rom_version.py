#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试3568版本测试功能
"""

import subprocess
import re
import time

def test_adb_connection():
    """测试ADB连接"""
    print("=== 测试ADB连接 ===")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        print(f"ADB devices返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("ADB连接正常")
            devices = result.stdout.strip().split('\n')[1:]  # 跳过标题行
            connected_devices = [line for line in devices if 'device' in line]
            
            if connected_devices:
                print(f"已连接设备: {len(connected_devices)}个")
                for device in connected_devices:
                    print(f"  {device}")
                return True
            else:
                print("❌ 没有连接的设备")
                return False
        else:
            print(f"❌ ADB连接失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ ADB连接测试出错: {str(e)}")
        return False

def test_rom_version_parsing():
    """测试ROM版本解析功能"""
    print("\n=== 测试ROM版本解析功能 ===")
    
    # 测试数据
    test_outputs = [
        "Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux",
        "Linux rk3568-buildroot 5.10.198 #1 SMP Tue Dec 25 08:30:45 CST 2024 aarch64 GNU/Linux",
        "Linux buildroot 4.19.232 #1 SMP Mon Jan 1 00:00:00 CST 2024 aarch64 GNU/Linux",
        "Linux rk3568 5.10.198-ab13 #1 SMP Fri Nov 15 14:22:33 CST 2024 aarch64 GNU/Linux"
    ]
    
    # 日期时间匹配模式
    date_pattern = re.compile(r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})')
    
    for i, output in enumerate(test_outputs, 1):
        print(f"\n测试样本 {i}:")
        print(f"输入: {output}")
        
        # 解析版本信息
        match = date_pattern.search(output)
        
        if match:
            version_info = match.group(1)
            print(f"✅ 解析成功: {version_info}")
            
            # 检查平台信息
            if "rk3568" in output.lower():
                platform_info = "RK3568"
                print(f"✅ 平台识别: {platform_info}")
            else:
                platform_info = "未知平台"
                print(f"⚠️ 平台识别: {platform_info}")
            
            result = f"{platform_info} - {version_info}"
            print(f"📋 最终结果: {result}")
        else:
            print("❌ 解析失败: 未找到有效的日期时间格式")

def test_real_uname_command():
    """测试真实的uname命令"""
    print("\n=== 测试真实uname命令 ===")
    
    try:
        # 执行uname -a命令
        command = "uname -a"
        print(f"执行命令: adb shell {command}")
        
        result = subprocess.run(['adb', 'shell', command], 
                              capture_output=True, text=True, timeout=10)
        
        print(f"返回码: {result.returncode}")
        
        if result.returncode == 0:
            output = result.stdout.strip()
            print("✅ uname命令执行成功")
            print(f"返回数据: {output}")
            
            # 解析版本信息
            date_pattern = re.compile(r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})')
            match = date_pattern.search(output)
            
            if match:
                version_info = match.group(1)
                print(f"✅ 版本信息解析成功: {version_info}")
                
                # 检查平台信息
                if "rk3568" in output.lower():
                    platform_info = "RK3568"
                    print(f"✅ 确认为RK3568平台")
                else:
                    platform_info = "未知平台"
                    print(f"⚠️ 未检测到RK3568标识")
                
                final_result = f"{platform_info} - {version_info}"
                print(f"📋 测试结果: {final_result}")
                return True, final_result
            else:
                print("❌ 无法解析版本信息")
                print("未找到有效的日期时间格式")
                return False, "版本信息解析失败"
        else:
            print(f"❌ uname命令执行失败: {result.stderr}")
            return False, "命令执行失败"
            
    except Exception as e:
        print(f"❌ uname命令测试出错: {str(e)}")
        return False, f"错误: {str(e)}"

def test_complete_rom_version_flow():
    """测试完整的ROM版本测试流程"""
    print("\n=== 测试完整的ROM版本测试流程 ===")
    
    print("步骤1: 测试ROM版本解析算法...")
    test_rom_version_parsing()
    
    print("\n步骤2: 测试真实设备...")
    success, result = test_real_uname_command()
    
    if success:
        print(f"\n🎉 3568版本测试成功！")
        print(f"ROM版本信息: {result}")
        return True
    else:
        print(f"\n❌ 3568版本测试失败: {result}")
        return False

def test_regex_patterns():
    """测试不同的正则表达式模式"""
    print("\n=== 测试正则表达式模式 ===")
    
    test_strings = [
        "Wed Jul 9 12:01:12 CST 2025",
        "Tue Dec 25 08:30:45 CST 2024", 
        "Mon Jan 1 00:00:00 CST 2024",
        "Fri Nov 15 14:22:33 CST 2024",
        "Jul 9 12:01:12",
        "Dec 25 08:30:45",
        "Jan 1 00:00:00"
    ]
    
    patterns = [
        (r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})', "基本模式"),
        (r'(\w{3}\s+\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})', "包含星期"),
        (r'(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+\w{3}', "包含时区")
    ]
    
    for pattern_str, pattern_name in patterns:
        print(f"\n测试模式: {pattern_name}")
        print(f"正则表达式: {pattern_str}")
        pattern = re.compile(pattern_str)
        
        for test_str in test_strings:
            match = pattern.search(test_str)
            if match:
                print(f"  ✅ '{test_str}' -> '{match.group(1)}'")
            else:
                print(f"  ❌ '{test_str}' -> 无匹配")

if __name__ == "__main__":
    print("3568版本测试功能验证")
    print("=" * 50)
    
    # 测试ADB连接
    if not test_adb_connection():
        print("\n❌ ADB连接失败，无法进行ROM版本测试")
        exit(1)
    
    # 测试正则表达式模式
    test_regex_patterns()
    
    # 测试完整的ROM版本测试流程
    success = test_complete_rom_version_flow()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 3568版本测试功能验证成功！")
    else:
        print("❌ 3568版本测试功能验证失败")
    
    print("\n测试完成")
