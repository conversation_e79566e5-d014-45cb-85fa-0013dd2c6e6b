[2025-07-18 10:58:28] === 开始新的测试 ===
[2025-07-18 10:58:28] 开始测试
[2025-07-18 10:58:28] 工序: 右手臂功能测试
[2025-07-18 10:58:28] 序列号: 111111111111111
[2025-07-18 10:58:28] 开始运行 右手臂功能测试 工序测试，共 16 个项目
[2025-07-18 10:58:28] 
开始执行: 设备连接状态检测
[2025-07-18 10:58:28] 设备连接测试通过，检测到 1 个设备
[2025-07-18 10:58:28] 设备: ?	device
[2025-07-18 10:58:29] 
开始执行: 3568版本测试
[2025-07-18 10:58:29] 执行3568版本测试...
[2025-07-18 10:58:29] 读取ROM版本号...
[2025-07-18 10:58:29] 执行命令: adb shell uname -a
[2025-07-18 10:58:29] uname命令执行成功
[2025-07-18 10:58:29] 返回数据: Linux rk3568-buildroot 5.10.198-ab13 #1 SMP Wed Jul 9 12:01:12 CST 2025 aarch64 GNU/Linux
[2025-07-18 10:58:29] ✅ 3568版本测试成功
[2025-07-18 10:58:29] ROM版本号: Jul 9 12:01:12
[2025-07-18 10:58:29] ✅ 确认为RK3568平台
[2025-07-18 10:58:29] 
开始执行: USB关键器件检测
[2025-07-18 10:58:29] 执行USB设备检测...
[2025-07-18 10:58:29] 执行命令: adb shell lsusb
[2025-07-18 10:58:29] 设备返回数据:
[2025-07-18 10:58:29] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-18 10:58:29] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-18 10:58:29] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-18 10:58:29] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-18 10:58:29] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-18 10:58:29] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-18 10:58:29] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-18 10:58:29] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-18 10:58:29] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-18 10:58:29] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-18 10:58:29] 总共解析到 9 个设备
[2025-07-18 10:58:29] ✅ 所有预期的设备ID都已找到
[2025-07-18 10:58:29] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 10:58:29] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-18 10:58:29] ✅ USB设备检测通过
[2025-07-18 10:58:30] 
开始执行: CAN0测试
[2025-07-18 10:58:30] 执行CAN0测试流程...
[2025-07-18 10:58:30] 执行命令: adb shell ip link set can0 down
[2025-07-18 10:58:30] CAN0已关闭
[2025-07-18 10:58:30] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-18 10:58:30] CAN0已启动
[2025-07-18 10:58:30] CAN监听线程已启动...
[2025-07-18 10:58:31] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-18 10:58:31] CAN测试数据已发送，等待监听返回...
[2025-07-18 10:58:31] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-18 10:58:33] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-18 10:58:34] 
开始执行: GPS测试
[2025-07-18 10:58:34] 执行GPS测试...
[2025-07-18 10:58:34] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 10:58:44] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-18 10:58:44] 
开始执行: 4G模组测试
[2025-07-18 10:58:44] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 10:58:44] 监听线程已启动，等待串口数据...
[2025-07-18 10:58:46] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 10:58:46] AT+CCID指令已发送，等待串口返回...
[2025-07-18 10:58:46] 串口输出: AT+CCID
[2025-07-18 10:58:46] 串口输出: 
[2025-07-18 10:58:46] 串口输出: 
[2025-07-18 10:58:46] 串口输出: +CCID: 89860114851012535241
[2025-07-18 10:58:48] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-18 10:58:49] 
开始执行: 按键测试
[2025-07-18 10:58:49] 开始按键测试...
[2025-07-18 10:58:49] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 10:58:53] 原始事件: Event: time 1752807533.995749, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-18 10:58:53] 解析结果: key_code=659, value=1
[2025-07-18 10:58:53] ✓ 检测到档位加(659)按下
[2025-07-18 10:58:54] 原始事件: Event: time 1752807534.419128, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-18 10:58:54] 解析结果: key_code=659, value=0
[2025-07-18 10:58:54] ✓ 档位加(659)测试通过
[2025-07-18 10:58:55] 原始事件: Event: time 1752807535.922328, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-18 10:58:55] 解析结果: key_code=660, value=1
[2025-07-18 10:58:55] ✓ 检测到智驾键(660)按下
[2025-07-18 10:58:55] 原始事件: Event: time 1752807536.195662, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-18 10:58:55] 解析结果: key_code=660, value=0
[2025-07-18 10:58:55] ✓ 智驾键(660)测试通过
[2025-07-18 10:58:56] 原始事件: Event: time 1752807537.372403, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-18 10:58:56] 解析结果: key_code=656, value=1
[2025-07-18 10:58:56] ✓ 检测到锁定键(656)按下
[2025-07-18 10:58:57] 原始事件: Event: time 1752807537.749065, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-18 10:58:57] 解析结果: key_code=656, value=0
[2025-07-18 10:58:57] ✓ 锁定键(656)测试通过
[2025-07-18 10:58:58] 原始事件: Event: time 1752807538.745723, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-18 10:58:58] 解析结果: key_code=661, value=1
[2025-07-18 10:58:58] ✓ 检测到静音键(661)按下
[2025-07-18 10:58:58] 原始事件: Event: time 1752807539.022537, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-18 10:58:58] 解析结果: key_code=661, value=0
[2025-07-18 10:58:58] ✓ 静音键(661)测试通过
[2025-07-18 10:58:59] 原始事件: Event: time 1752807539.795718, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-18 10:58:59] 解析结果: key_code=663, value=1
[2025-07-18 10:58:59] ✓ 检测到语音键(663)按下
[2025-07-18 10:58:59] 原始事件: Event: time 1752807540.022407, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-18 10:58:59] 解析结果: key_code=663, value=0
[2025-07-18 10:58:59] ✓ 语音键(663)测试通过
[2025-07-18 10:59:00] 原始事件: Event: time 1752807540.672434, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-18 10:59:00] 解析结果: key_code=657, value=1
[2025-07-18 10:59:00] ✓ 检测到SOS键(657)按下
[2025-07-18 10:59:00] 原始事件: Event: time 1752807540.975834, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-18 10:59:00] 解析结果: key_code=657, value=0
[2025-07-18 10:59:00] ✓ SOS键(657)测试通过
[2025-07-18 10:59:02] 原始事件: Event: time 1752807542.525723, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-18 10:59:02] 解析结果: key_code=662, value=1
[2025-07-18 10:59:02] ✓ 检测到档位减(662)按下
[2025-07-18 10:59:02] 原始事件: Event: time 1752807542.745778, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-18 10:59:02] 解析结果: key_code=662, value=0
[2025-07-18 10:59:02] ✓ 档位减(662)测试通过
[2025-07-18 10:59:02] 原始事件: Event: time 1752807543.372324, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-18 10:59:02] 解析结果: key_code=658, value=1
[2025-07-18 10:59:02] ✓ 检测到喇叭键(658)按下
[2025-07-18 10:59:03] 原始事件: Event: time 1752807543.695774, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-18 10:59:03] 解析结果: key_code=658, value=0
[2025-07-18 10:59:03] ✓ 喇叭键(658)测试通过
[2025-07-18 10:59:03] 按键测试完成 - 检测到8个按键
[2025-07-18 10:59:06] 
开始执行: 按键灯测试
[2025-07-18 10:59:06] 开始LED背光灯测试...
[2025-07-18 10:59:06] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 10:59:06] LED控制命令执行成功
[2025-07-18 10:59:06] 🔧 显示确认对话框: LED测试确认
[2025-07-18 10:59:06] 🔧 对话框窗口已创建
[2025-07-18 10:59:06] 🔧 '是'按钮已创建
[2025-07-18 10:59:06] 🔧 '否'按钮已创建
[2025-07-18 10:59:06] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 10:59:07] 👤 用户选择: 是 (测试通过)
[2025-07-18 10:59:07] 🔧 对话框关闭，用户响应: yes
[2025-07-18 10:59:07] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-18 10:59:07] LED灯已关闭
[2025-07-18 10:59:07] ✅ LED测试通过 - 用户确认背光灯正常
[2025-07-18 10:59:08] 
开始执行: 手电筒测试
[2025-07-18 10:59:08] 开始手电筒LED测试...
[2025-07-18 10:59:08] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 10:59:08] 手电筒控制命令执行成功
[2025-07-18 10:59:08] 🔧 显示确认对话框: 手电筒测试确认
[2025-07-18 10:59:08] 🔧 对话框窗口已创建
[2025-07-18 10:59:08] 🔧 '是'按钮已创建
[2025-07-18 10:59:08] 🔧 '否'按钮已创建
[2025-07-18 10:59:08] 🔧 对话框显示完成，等待用户响应...
[2025-07-18 10:59:09] 👤 用户选择: 是 (测试通过)
[2025-07-18 10:59:09] 🔧 对话框关闭，用户响应: yes
[2025-07-18 10:59:09] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-18 10:59:09] 手电筒已关闭
[2025-07-18 10:59:09] ✅ 手电筒测试通过 - 用户确认手电筒正常
[2025-07-18 10:59:09] 
开始执行: 摇杆使能测试
[2025-07-18 10:59:09] 执行摇杆测试...
[2025-07-18 10:59:09] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 10:59:09] 命令执行成功
[2025-07-18 10:59:09] 返回数据: 255
[2025-07-18 10:59:09] 摇杆测试通过，值: 255
[2025-07-18 10:59:10] 
开始执行: 前摄像头测试
[2025-07-18 10:59:10] 开始执行前摄像头测试...
[2025-07-18 10:59:10] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 10:59:10] 命令执行成功，返回: 无输出
[2025-07-18 10:59:10] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-18 10:59:11] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.325131574
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-18 10:59:11] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-18 10:59:11] 拉取命令执行成功
[2025-07-18 10:59:11] 返回数据: [ 39%] /data/camera/cam0_3840x2160.jpg
[ 78%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 24.1 MB/s (167613 bytes in 0.007s)
[2025-07-18 10:59:11] 图片已保存: cam0_3840x2160.jpg
[2025-07-18 10:59:12] 用户确认结果: 通过
[2025-07-18 10:59:13] 
开始执行: 光感测试
[2025-07-18 10:59:13] 执行光感测试...
[2025-07-18 10:59:13] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 10:59:26] 光感测试完成 - 检测到数值变化
[2025-07-18 10:59:26] 数值从 3 变化到 4
[2025-07-18 10:59:26] 
开始执行: 回充摄像头测试
[2025-07-18 10:59:26] 开始执行回充摄像头测试...
[2025-07-18 10:59:26] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 10:59:26] 命令执行成功，返回: 无输出
[2025-07-18 10:59:26] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-18 10:59:27] 命令执行成功，返回: 无输出
[2025-07-18 10:59:27] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-18 10:59:27] 拉取命令执行成功
[2025-07-18 10:59:27] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 19.6 MB/s (41528 bytes in 0.002s)
[2025-07-18 10:59:27] 图片已保存: output.jpg
[2025-07-18 10:59:29] 用户确认结果: 通过
[2025-07-18 10:59:29] 
开始执行: 喇叭测试
[2025-07-18 10:59:29] 执行喇叭测试...
[2025-07-18 10:59:29] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 10:59:44] 命令执行成功
[2025-07-18 10:59:44] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-18 10:59:44] 音频播放完成
[2025-07-18 10:59:44] 
开始执行: 蓝牙测试
[2025-07-18 10:59:44] 执行蓝牙测试...
[2025-07-18 10:59:44] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 10:59:44] 执行命令: adb shell bluetoothctl show
[2025-07-18 10:59:44] 命令执行成功
[2025-07-18 10:59:44] 返回数据:
[2025-07-18 10:59:44]   Controller 40:55:48:A4:B8:E8 (public)
[2025-07-18 10:59:44]   Name: CMG-1
[2025-07-18 10:59:44]   Alias: CMG-1
[2025-07-18 10:59:44]   Class: 0x006c0000 (7077888)
[2025-07-18 10:59:44]   Powered: yes
[2025-07-18 10:59:44]   PowerState: on
[2025-07-18 10:59:44]   Discoverable: no
[2025-07-18 10:59:44]   DiscoverableTimeout: 0x00000000 (0)
[2025-07-18 10:59:44]   Pairable: yes
[2025-07-18 10:59:44]   UUID: A/V Remote Control        (0000110e-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: PnP Information           (00001200-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Handsfree Audio Gateway   (0000111f-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Audio Sink                (0000110b-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Headset                   (00001108-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: A/V Remote Control Target (0000110c-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Generic Access Profile    (00001800-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Audio Source              (0000110a-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Generic Attribute Profile (00001801-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Volume Control            (00001844-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Device Information        (0000180a-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   UUID: Volume Offset Control     (00001845-0000-1000-8000-00805f9b34fb)
[2025-07-18 10:59:44]   Modalias: usb:v1D6Bp0246d0544
[2025-07-18 10:59:44]   Discovering: no
[2025-07-18 10:59:44]   Roles: central
[2025-07-18 10:59:44]   Roles: peripheral
[2025-07-18 10:59:44]   Advertising Features:
[2025-07-18 10:59:44]   ActiveInstances: 0x00 (0)
[2025-07-18 10:59:44]   SupportedInstances: 0x10 (16)
[2025-07-18 10:59:44]   SupportedIncludes: tx-power
[2025-07-18 10:59:44]   SupportedIncludes: appearance
[2025-07-18 10:59:44]   SupportedIncludes: local-name
[2025-07-18 10:59:44]   SupportedSecondaryChannels: 1M
[2025-07-18 10:59:44]   SupportedSecondaryChannels: 2M
[2025-07-18 10:59:44]   SupportedSecondaryChannels: Coded
[2025-07-18 10:59:44]   SupportedCapabilities Key: MaxAdvLen
[2025-07-18 10:59:44]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 10:59:44]   SupportedCapabilities Key: MaxScnRspLen
[2025-07-18 10:59:44]   SupportedCapabilities Value: 0x1f (31)
[2025-07-18 10:59:44]   SupportedFeatures: CanSetTxPower
[2025-07-18 10:59:44]   SupportedFeatures: HardwareOffload
[2025-07-18 10:59:44]   Advertisement Monitor Features:
[2025-07-18 10:59:44]   SupportedMonitorTypes: or_patterns
[2025-07-18 10:59:44] ✅ 蓝牙测试成功，检测到蓝牙控制器
[2025-07-18 10:59:44] 蓝牙控制器MAC地址: 40:55:48:A4:B8:E8
[2025-07-18 10:59:44] 
开始执行: WiFi测试
[2025-07-18 10:59:44] 执行WiFi测试...
[2025-07-18 10:59:44] 第一步：连接WiFi网络...
[2025-07-18 10:59:44] 停止现有的wpa_supplicant进程...
[2025-07-18 10:59:44] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 10:59:44] 清理wpa_supplicant socket文件...
[2025-07-18 10:59:44] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 10:59:44] 关闭wlan0接口...
[2025-07-18 10:59:44] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 10:59:45] 连接WiFi网络...
[2025-07-18 10:59:45] 执行WiFi连接命令...
[2025-07-18 10:59:45] SSID: Orion_SZ_5G
[2025-07-18 10:59:48] WiFi连接命令执行完成，返回数据:
[2025-07-18 10:59:48]   Successfully initialized wpa_supplicant
[2025-07-18 10:59:48]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-18 10:59:48]   1
[2025-07-18 10:59:48]   OK
[2025-07-18 10:59:48]   OK
[2025-07-18 10:59:48]   OK
[2025-07-18 10:59:48]   deleting routers
[2025-07-18 10:59:48]   adding dns ************
[2025-07-18 10:59:48]   adding dns ***********
[2025-07-18 10:59:48]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-18 10:59:48]   SSID: Orion_SZ_5G
[2025-07-18 10:59:48]   freq: 5300
[2025-07-18 10:59:48]   RX: 2664 bytes (6 packets)
[2025-07-18 10:59:48]   TX: 1928 bytes (11 packets)
[2025-07-18 10:59:48]   signal: -56 dBm
[2025-07-18 10:59:48]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-18 10:59:48]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-18 10:59:48]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-18 10:59:48]   link/ether 40:55:48:a5:07:44 brd ff:ff:ff:ff:ff:ff
[2025-07-18 10:59:48]   inet 192.168.20.168/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-18 10:59:48]   valid_lft forever preferred_lft forever
[2025-07-18 10:59:48]   inet6 fe80::d008:200:a146:7156/64 scope link
[2025-07-18 10:59:48]   valid_lft forever preferred_lft forever
[2025-07-18 10:59:48] ✅ WiFi连接成功
[2025-07-18 10:59:48] 等待网络稳定...
[2025-07-18 10:59:51] 第二步：开始网络发包延时测试...
[2025-07-18 10:59:51] 执行命令: adb shell ping -c 10 www.baidu.com
[2025-07-18 10:59:51] 正在进行10秒钟的网络延时测试...
[2025-07-18 11:00:00] ping命令执行成功
[2025-07-18 11:00:00] 返回数据:
[2025-07-18 11:00:00]   PING www.baidu.com(2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207)) 56 data bytes
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=1 ttl=52 time=63.1 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=2 ttl=52 time=64.2 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=3 ttl=52 time=36.5 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=4 ttl=52 time=42.5 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=5 ttl=52 time=39.5 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=6 ttl=52 time=56.8 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=7 ttl=52 time=54.4 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=8 ttl=52 time=43.6 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=9 ttl=52 time=58.5 ms
[2025-07-18 11:00:00]   64 bytes from 2408:8756:c52:15df:0:ff:b073:d207 (2408:8756:c52:15df:0:ff:b073:d207): icmp_seq=10 ttl=52 time=40.5 ms
[2025-07-18 11:00:00]   --- www.baidu.com ping statistics ---
[2025-07-18 11:00:00]   10 packets transmitted, 10 received, 0% packet loss, time 9009ms
[2025-07-18 11:00:00]   rtt min/avg/max/mdev = 36.501/49.953/64.151/9.943 ms
[2025-07-18 11:00:00] 检测到延时: 63.1 ms
[2025-07-18 11:00:00] 检测到延时: 64.2 ms
[2025-07-18 11:00:00] 检测到延时: 36.5 ms
[2025-07-18 11:00:00] 检测到延时: 42.5 ms
[2025-07-18 11:00:00] 检测到延时: 39.5 ms
[2025-07-18 11:00:00] 检测到延时: 56.8 ms
[2025-07-18 11:00:00] 检测到延时: 54.4 ms
[2025-07-18 11:00:00] 检测到延时: 43.6 ms
[2025-07-18 11:00:00] 检测到延时: 58.5 ms
[2025-07-18 11:00:00] 检测到延时: 40.5 ms
[2025-07-18 11:00:00] ✅ WiFi延时测试成功
[2025-07-18 11:00:00] 发包数量: 10 个
[2025-07-18 11:00:00] 平均延时: 49.96 ms
[2025-07-18 11:00:00] 最小延时: 36.50 ms
[2025-07-18 11:00:00] 最大延时: 64.20 ms
[2025-07-18 11:00:00] 
测试完成 - 通过率: 15/16
[2025-07-18 11:00:00] ❌ 存在测试失败项！
[2025-07-18 11:00:00] 测试记录已保存: records/111111111111111_20250718_110000.json
[2025-07-18 11:00:00] 测试日志已保存: records/111111111111111_20250718_110000.log

