import sys
import time
import paramiko
import re
import json
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QPushButton, QTextEdit, QLineEdit, 
                            QLabel, QMessageBox, QGroupBox, QGridLayout, QTableWidget, QTableWidgetItem)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QColor

class SSHThread(QThread):
    connected = pyqtSignal(bool, str)
    
    def __init__(self):
        super().__init__()
        self.ssh_client = None
        
    def run(self):
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                hostname='************',
                port=22,
                username='root',
                password='slamware123'
            )
            # 停止电梯控制器服务
            stdin, stdout, stderr = self.ssh_client.exec_command('sv stop /service/elevator-controller')
            time.sleep(2)  # 等待服务停止
            self.connected.emit(True, "连接成功并已停止电梯控制器服务")
        except Exception as e:
            self.connected.emit(False, f"连接失败: {str(e)}")

class ElevatorControllerTester(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ssh_client = None
        self.ssh_thread = None
        self.key_fields = [
            "Firmware Version",
            "Hardware version",
            "Serial Number",
            "car_acc_d",
            "car_acc_b",
            "car_acc_dis",
            "USB设备数量"
        ]
        self.config_file = "config.json"
        self.standard_values = self.load_config()
        self.initUI()
        self.autoConnect()
        
    def load_config(self):
        """从配置文件加载标准值"""
        default_values = {
            "Firmware Version": "1.0.0",
            "Hardware version": "1.0.0",
            "Serial Number": "123456",
            "car_acc_d": "0.0",
            "car_acc_b": "0.0",
            "car_acc_dis": "0.0",
            "USB设备数量": "2"  # 默认USB设备数量
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
            
        return default_values
        
    def save_config(self):
        """保存标准值到配置文件"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.standard_values, f, indent=4)
        except Exception as e:
            print(f"保存配置文件失败: {str(e)}")
        
    def initUI(self):
        self.setWindowTitle('梯控测试上位机')
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建主窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态显示区域
        status_layout = QHBoxLayout()
        self.status_label = QLabel('状态: 未连接')
        self.status_label.setStyleSheet("background-color: red; color: white; padding: 5px; font-size: 34px;")
        self.status_label.setAlignment(Qt.AlignLeft)
        status_layout.addWidget(self.status_label)
        layout.addLayout(status_layout)
        
        # 测试结果显示区域
        result_group = QGroupBox("")
        result_layout = QVBoxLayout()
        
        # 添加表格用于显示设备信息
        self.info_table = QTableWidget()
        self.info_table.setColumnCount(4)
        self.info_table.setHorizontalHeaderLabels(["测试项目", "标准值", "测试值", "测试结果"])
        self.info_table.horizontalHeader().setStretchLastSection(True)
        
        # 设置列宽
        self.info_table.setColumnWidth(0, 400)  # 测试项目列宽
        self.info_table.setColumnWidth(1, 300)  # 标准值列宽
        self.info_table.setColumnWidth(2, 300)  # 测试值列宽
        self.info_table.setColumnWidth(3, 200)  # 测试结果列宽
        
        # 设置表头样式
        header = self.info_table.horizontalHeader()
        header.setStyleSheet("QHeaderView::section { background-color: #f0f0f0; padding: 5px; font-size: 34px; }")
        
        # 初始化表格行
        self.info_table.setRowCount(len(self.key_fields))
        for i, field in enumerate(self.key_fields):
            # 测试项目
            item = QTableWidgetItem(field)
            item.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            self.info_table.setItem(i, 0, item)
            
            # 标准值
            item = QTableWidgetItem(self.standard_values.get(field, ""))
            item.setTextAlignment(Qt.AlignCenter)
            self.info_table.setItem(i, 1, item)
            
            # 测试值
            item = QTableWidgetItem("")
            item.setTextAlignment(Qt.AlignCenter)
            self.info_table.setItem(i, 2, item)
            
            # 测试结果
            result_item = QTableWidgetItem("")
            result_item.setTextAlignment(Qt.AlignCenter)
            result_item.setBackground(QColor(255, 255, 255))
            self.info_table.setItem(i, 3, result_item)
            
        # 设置表格样式
        self.info_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #d0d0d0;
                background-color: white;
                font-size: 34px;
            }
            QTableWidget::item {
                padding: 10px;
            }
        """)
        
        # 设置表格不可编辑
        self.info_table.setEditTriggers(QTableWidget.NoEditTriggers)
        
        result_layout.addWidget(self.info_table)
        
        # 添加文本区域用于显示其他测试结果
        self.result_area = QTextEdit()
        self.result_area.setReadOnly(True)
        self.result_area.setStyleSheet("font-size: 34px;")
        result_layout.addWidget(self.result_area)
        
        result_group.setLayout(result_layout)
        layout.addWidget(result_group)
        
        # 序列号输入和开始测试按钮区域
        test_layout = QHBoxLayout()
        
        # 序列号输入
        serial_label = QLabel('序列号:')
        serial_label.setStyleSheet("font-size: 34px;")
        self.serial_input = QLineEdit()
        self.serial_input.setStyleSheet("font-size: 34px;")
        self.serial_input.setPlaceholderText("请输入序列号")
        test_layout.addWidget(serial_label)
        test_layout.addWidget(self.serial_input)
        
        # 开始测试按钮
        self.start_test_button = QPushButton('开始测试')
        self.start_test_button.setStyleSheet("font-size: 34px;")
        self.start_test_button.clicked.connect(self.start_all_tests)
        self.start_test_button.setEnabled(False)
        test_layout.addWidget(self.start_test_button)
        
        layout.addLayout(test_layout)
        
        # 命令输入区域
        command_layout = QHBoxLayout()
        self.command_input = QLineEdit()
        self.command_input.setPlaceholderText("输入自定义命令")
        self.command_input.setStyleSheet("font-size: 34px;")
        self.send_button = QPushButton('发送')
        self.send_button.setStyleSheet("font-size: 34px;")
        self.send_button.clicked.connect(self.send_command)
        self.send_button.setEnabled(False)
        
        command_layout.addWidget(self.command_input)
        command_layout.addWidget(self.send_button)
        
        layout.addLayout(command_layout)
        
    def start_all_tests(self):
        """执行所有测试"""
        if not self.serial_input.text():
            QMessageBox.warning(self, '警告', '请输入序列号')
            return
            
        try:
            # 停止电梯控制器服务
            self.ssh_client.exec_command('sv stop /service/elevator-controller')
            time.sleep(2)  # 等待服务停止
            self.result_area.append("已停止电梯控制器服务")
            
            # 执行测试
            self.run_test("设备信息测试")
            self.run_test("电梯状态测试")
            self.run_test("USB设备测试")
            
            # 保存测试结果
            self.save_test_results()
        except Exception as e:
            self.result_area.append(f'执行测试时出错: {str(e)}')
            
    def autoConnect(self):
        self.status_label.setText("状态: 正在连接...")
        self.ssh_thread = SSHThread()
        self.ssh_thread.connected.connect(self.handle_connection)
        self.ssh_thread.start()
        
    def handle_connection(self, success, message):
        if success:
            self.ssh_client = self.ssh_thread.ssh_client
            self.status_label.setText("状态: 已连接")
            self.status_label.setStyleSheet("background-color: green; color: white; padding: 5px; font-size: 34px;")
            self.send_button.setEnabled(True)
            self.start_test_button.setEnabled(True)
        else:
            self.status_label.setText("状态: 未连接")
            self.status_label.setStyleSheet("background-color: red; color: white; padding: 5px; font-size: 34px;")
            QMessageBox.critical(self, '连接错误', message)
            
    def parse_elevator_status(self, output):
        # 在结果区域显示原始输出
        self.result_area.append("=== 原始输出 ===")
        self.result_area.append(output)
        self.result_area.append("=== 解析结果 ===")
        
        # 解析每个关键字段
        for i, field in enumerate(self.key_fields):
            # 使用正则表达式匹配字段和值
            if field in ["car_acc_d", "car_acc_b", "car_acc_dis"]:
                pattern = rf"{field}:\s*([\d.-]+)"
            else:
                pattern = rf"{field}:\s*(.*?)(?=\n|$)"
                
            match = re.search(pattern, output)
            
            if match:
                value = match.group(1).strip()
                standard_value = self.standard_values.get(field, "")
                
                # 根据字段类型判断测试结果
                if field == "Serial Number":
                    # Serial Number只需要是33位字符串
                    result = "PASS" if len(value) == 33 else "FAIL"
                elif field in ["car_acc_d", "car_acc_b", "car_acc_dis"]:
                    # 加速度值只需要是数字
                    result = "PASS" if re.match(r'^-?\d+(\.\d+)?$', value) else "FAIL"
                else:
                    # 其他字段需要与标准值完全匹配
                    result = "PASS" if standard_value and value == standard_value else "FAIL"
                
                # 更新测试值
                item = QTableWidgetItem(value)
                item.setTextAlignment(Qt.AlignCenter)
                self.info_table.setItem(i, 2, item)
                
                # 更新测试结果
                result_item = QTableWidgetItem(result)
                result_item.setTextAlignment(Qt.AlignCenter)
                if result == "PASS":
                    result_item.setBackground(QColor(0, 255, 0))  # 绿色
                else:
                    result_item.setBackground(QColor(255, 0, 0))  # 红色
                self.info_table.setItem(i, 3, result_item)
                
                # 更新标准值
                self.standard_values[field] = value
                self.save_config()
            else:
                # 如果没有找到匹配，更新为未找到
                item = QTableWidgetItem("未找到")
                item.setTextAlignment(Qt.AlignCenter)
                self.info_table.setItem(i, 2, item)
                
                result_item = QTableWidgetItem("FAIL")
                result_item.setTextAlignment(Qt.AlignCenter)
                result_item.setBackground(QColor(255, 0, 0))
                self.info_table.setItem(i, 3, result_item)
            
    def run_test(self, test_name):
        self.result_area.append(f"\n=== 开始 {test_name} ===\n")
        
        if test_name == "设备信息测试":
            try:
                command = 'slamware_elevator_console -c slamware-core getData'
                self.result_area.append(f"发送指令: {command}")
                
                stdin, stdout, stderr = self.ssh_client.exec_command(command)
                output = stdout.read().decode('utf-8')
                error = stderr.read().decode('utf-8')
                
                if error:
                    self.result_area.append(f'错误:\n{error}')
                    return
                    
                # 解析输出并显示在表格中
                self.parse_elevator_status(output)
                self.result_area.append("设备信息测试完成")
                
            except Exception as e:
                self.result_area.append(f'执行测试时出错: {str(e)}')
                
        elif test_name == "电梯状态测试":
            try:
                command = 'slamware_elevator_console -c slamware-core getData'
                self.result_area.append(f"发送指令: {command}")
                
                stdin, stdout, stderr = self.ssh_client.exec_command(command)
                output = stdout.read().decode('utf-8')
                error = stderr.read().decode('utf-8')
                
                if error:
                    self.result_area.append(f'错误:\n{error}')
                    return
                    
                # 解析输出并显示在表格中
                self.parse_elevator_status(output)
                self.result_area.append("电梯状态测试完成")
                
            except Exception as e:
                self.result_area.append(f'执行测试时出错: {str(e)}')
                
        elif test_name == "USB设备测试":
            try:
                command = 'lsusb'
                self.result_area.append(f"发送指令: {command}")
                
                stdin, stdout, stderr = self.ssh_client.exec_command(command)
                output = stdout.read().decode('utf-8')
                error = stderr.read().decode('utf-8')
                
                if error:
                    self.result_area.append(f'错误:\n{error}')
                    return
                    
                # 显示原始输出
                self.result_area.append("=== 原始输出 ===")
                self.result_area.append(output)
                self.result_area.append("=== 解析结果 ===")
                
                # 计算USB设备数量（统计Bus的数量）
                usb_count = len([line for line in output.split('\n') if line.strip().startswith('Bus ')])
                
                # 更新表格中的USB设备数量
                for i, field in enumerate(self.key_fields):
                    if field == "USB设备数量":
                        # 更新测试值
                        item = QTableWidgetItem(str(usb_count))
                        item.setTextAlignment(Qt.AlignCenter)
                        self.info_table.setItem(i, 2, item)
                        
                        # 更新测试结果
                        standard_value = self.standard_values.get(field, "7")  # 默认值改为7
                        result = "PASS" if str(usb_count) == standard_value else "FAIL"
                        result_item = QTableWidgetItem(result)
                        result_item.setTextAlignment(Qt.AlignCenter)
                        if result == "PASS":
                            result_item.setBackground(QColor(0, 255, 0))  # 绿色
                        else:
                            result_item.setBackground(QColor(255, 0, 0))  # 红色
                        self.info_table.setItem(i, 3, result_item)
                        
                        # 更新标准值
                        self.standard_values[field] = str(usb_count)
                        self.save_config()
                        break
                
                self.result_area.append(f"USB设备数量: {usb_count}")
                self.result_area.append("USB设备测试完成")
                
            except Exception as e:
                self.result_area.append(f'执行测试时出错: {str(e)}')
                
        elif test_name == "电梯到达信号测试":
            self.result_area.append("请告诉我具体的测试命令和预期结果，我将为您实现该测试项")
        elif test_name == "电梯门状态测试":
            self.result_area.append("请告诉我具体的测试命令和预期结果，我将为您实现该测试项")
        # ... 其他测试项目
            
    def send_command(self):
        if not self.ssh_client:
            return
            
        command = self.command_input.text()
        if not command:
            return
            
        try:
            self.result_area.append(f"\n=== 发送自定义命令 ===\n")
            self.result_area.append(f"命令: {command}")
            
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if output:
                self.result_area.append(f'输出:\n{output}')
            if error:
                self.result_area.append(f'错误:\n{error}')
                
        except Exception as e:
            self.result_area.append(f'执行命令时出错: {str(e)}')
            
    def closeEvent(self, event):
        if self.ssh_client:
            try:
                # 退出前重启服务
                self.ssh_client.exec_command('sv start /service/elevator-controller')
                time.sleep(1)
                self.ssh_client.close()
            except:
                pass
        event.accept()

    def save_test_results(self):
        """保存测试结果到文件"""
        try:
            serial_number = self.serial_input.text()
            filename = f"test_results_{serial_number}.txt"
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"序列号: {serial_number}\n")
                f.write("测试时间: " + time.strftime("%Y-%m-%d %H:%M:%S") + "\n\n")
                
                # 写入表格内容
                f.write("测试项目\t标准值\t测试值\t测试结果\n")
                for row in range(self.info_table.rowCount()):
                    items = []
                    for col in range(self.info_table.columnCount()):
                        item = self.info_table.item(row, col)
                        items.append(item.text() if item else "")
                    f.write("\t".join(items) + "\n")
                
                # 写入原始输出
                f.write("\n原始输出:\n")
                f.write(self.result_area.toPlainText())
                
            self.result_area.append(f"\n测试结果已保存到文件: {filename}")
        except Exception as e:
            self.result_area.append(f'保存测试结果时出错: {str(e)}')

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = ElevatorControllerTester()
    window.show()
    sys.exit(app.exec_()) 