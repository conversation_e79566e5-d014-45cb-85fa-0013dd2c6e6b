#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的UI界面
验证工序选择和序列号输入对话框的新设计
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}}

def test_process_selection_ui():
    """测试工序选择UI"""
    print("=== 测试工序选择UI ===")
    
    config = load_config()
    work_processes = config.get("work_processes", {})
    
    if not work_processes:
        print("❌ 没有找到工序配置")
        return
    
    # 创建工序选择对话框
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    dialog = tk.Toplevel(root)
    dialog.title("选择测试工序")
    dialog.geometry("400x200")
    dialog.transient(root)
    dialog.grab_set()
    dialog.resizable(False, False)
    
    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
    y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
    dialog.geometry(f"+{x}+{y}")
    
    result = {"selected": False, "process": None}
    
    main_frame = ttk.Frame(dialog, padding="30")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="请选择测试工序", 
                           font=("Microsoft YaHei UI", 14, "bold"))
    title_label.pack(pady=(0, 25))
    
    # 工序选择区域
    select_frame = ttk.Frame(main_frame)
    select_frame.pack(fill=tk.X, pady=(0, 30))
    select_frame.columnconfigure(1, weight=1)
    
    # 工序标签
    ttk.Label(select_frame, text="测试工序:", 
             font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)
    
    # 工序下拉列表
    process_var = tk.StringVar()
    process_names = list(work_processes.keys())
    process_combo = ttk.Combobox(
        select_frame,
        textvariable=process_var,
        values=process_names,
        state="readonly",
        font=("Microsoft YaHei UI", 11),
        width=25
    )
    process_combo.grid(row=0, column=1, sticky=(tk.W, tk.E))
    
    # 默认选择第一个工序
    if process_names:
        process_combo.current(0)
    
    # 按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X)
    
    def confirm_selection():
        """确认选择"""
        selected_process = process_var.get()
        if selected_process:
            result["selected"] = True
            result["process"] = selected_process
            dialog.destroy()
        else:
            messagebox.showwarning("警告", "请选择一个工序")
    
    def cancel_selection():
        """取消选择"""
        result["selected"] = False
        dialog.destroy()
    
    # 按钮布局：退出程序在左边，确认选择在右边
    ttk.Button(button_frame, text="退出程序", 
              command=cancel_selection).pack(side=tk.LEFT)
    ttk.Button(button_frame, text="确认选择", 
              command=confirm_selection, 
              style="Accent.TButton").pack(side=tk.RIGHT)
    
    # 绑定回车键确认
    dialog.bind('<Return>', lambda e: confirm_selection())
    
    # 设置焦点到下拉框
    process_combo.focus()
    
    print("工序选择对话框已创建，请测试...")
    
    # 等待对话框关闭
    dialog.wait_window()
    
    if result["selected"]:
        print(f"✅ 用户选择了工序: {result['process']}")
        return result["process"]
    else:
        print("❌ 用户取消了选择")
        return None

def test_serial_input_ui(selected_process=None):
    """测试序列号输入UI"""
    print("\n=== 测试序列号输入UI ===")
    
    if not selected_process:
        selected_process = "测试工序"
    
    # 创建序列号输入对话框
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    dialog = tk.Toplevel(root)
    dialog.title("输入序列号")
    dialog.geometry("450x220")
    dialog.transient(root)
    dialog.grab_set()
    dialog.resizable(False, False)
    
    # 居中显示
    dialog.update_idletasks()
    x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
    y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
    dialog.geometry(f"+{x}+{y}")
    
    result = {"confirmed": False, "serial_number": ""}
    
    main_frame = ttk.Frame(dialog, padding="30")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="输入序列号", 
                           font=("Microsoft YaHei UI", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 当前工序信息
    process_info = f"当前工序: {selected_process}"
    info_label = ttk.Label(main_frame, text=process_info, 
                          font=("Microsoft YaHei UI", 10), 
                          foreground="blue")
    info_label.pack(pady=(0, 20))
    
    # 序列号输入区域
    input_frame = ttk.Frame(main_frame)
    input_frame.pack(fill=tk.X, pady=(0, 25))
    input_frame.columnconfigure(1, weight=1)
    
    # 输入标签
    ttk.Label(input_frame, text="设备序列号:", 
             font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)
    
    # 序列号输入框
    serial_var = tk.StringVar()
    serial_entry = ttk.Entry(input_frame, textvariable=serial_var, 
                           font=("Microsoft YaHei UI", 11), width=20)
    serial_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
    serial_entry.focus()
    
    # 按钮区域
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X)
    
    def confirm_serial():
        """确认序列号"""
        serial_number = serial_var.get().strip()
        if serial_number:
            result["confirmed"] = True
            result["serial_number"] = serial_number
            dialog.destroy()
        else:
            messagebox.showwarning("警告", "请输入序列号")
            serial_entry.focus()
    
    def cancel_serial():
        """取消输入"""
        result["confirmed"] = False
        dialog.destroy()
    
    # 绑定回车键
    serial_entry.bind("<Return>", lambda e: confirm_serial())
    
    # 按钮布局：退出程序在左边，开始测试在右边
    ttk.Button(button_frame, text="退出程序", 
              command=cancel_serial).pack(side=tk.LEFT)
    ttk.Button(button_frame, text="开始测试", 
              command=confirm_serial, 
              style="Accent.TButton").pack(side=tk.RIGHT)
    
    print("序列号输入对话框已创建，请测试...")
    
    # 等待对话框关闭
    dialog.wait_window()
    
    if result["confirmed"]:
        print(f"✅ 用户输入了序列号: {result['serial_number']}")
        return result["serial_number"]
    else:
        print("❌ 用户取消了输入")
        return None

def create_ui_comparison_demo():
    """创建UI对比演示"""
    print("\n=== 创建UI对比演示 ===")
    
    root = tk.Tk()
    root.title("UI优化对比演示")
    root.geometry("800x600")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="UI优化对比演示", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 优化内容说明
    optimization_frame = ttk.LabelFrame(main_frame, text="UI优化内容", padding="15")
    optimization_frame.pack(fill=tk.X, pady=(0, 20))
    
    optimization_text = """🎨 工序选择对话框优化:

优化前:
  • 使用列表框显示工序
  • 显示详细描述信息
  • 界面复杂，尺寸较大 (500x400)
  • 按钮位置：确认选择在右，退出程序在左

优化后:
  ✅ 改为下拉列表样式，更简洁
  ✅ 移除描述信息，界面清爽
  ✅ 优化尺寸 (400x200)，更紧凑
  ✅ 按钮位置互换：退出程序在左，确认选择在右
  ✅ 支持回车键快速确认
  ✅ 禁止窗口缩放，保持布局稳定

🎨 序列号输入对话框优化:

优化前:
  • 垂直布局，占用空间较大
  • 按钮位置不一致

优化后:
  ✅ 水平布局，标签和输入框对齐
  ✅ 统一按钮位置和样式
  ✅ 优化尺寸和间距
  ✅ 保持与工序选择对话框一致的风格"""
    
    ttk.Label(optimization_frame, text=optimization_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 测试按钮区域
    test_frame = ttk.LabelFrame(main_frame, text="测试新UI", padding="15")
    test_frame.pack(fill=tk.X, pady=(0, 20))
    
    def test_process_dialog():
        """测试工序选择对话框"""
        selected = test_process_selection_ui()
        if selected:
            messagebox.showinfo("测试结果", f"选择的工序: {selected}")
    
    def test_serial_dialog():
        """测试序列号输入对话框"""
        serial = test_serial_input_ui("整机半成品功能测试")
        if serial:
            messagebox.showinfo("测试结果", f"输入的序列号: {serial}")
    
    def test_complete_flow():
        """测试完整流程"""
        # 先选择工序
        selected_process = test_process_selection_ui()
        if selected_process:
            # 再输入序列号
            serial_number = test_serial_input_ui(selected_process)
            if serial_number:
                messagebox.showinfo("测试完成", 
                                  f"工序: {selected_process}\n序列号: {serial_number}\n\n准备开始测试！")
    
    button_frame = ttk.Frame(test_frame)
    button_frame.pack(fill=tk.X)
    
    ttk.Button(button_frame, text="测试工序选择", command=test_process_dialog).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试序列号输入", command=test_serial_dialog).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="测试完整流程", command=test_complete_flow).pack(side=tk.LEFT, padx=5)
    
    # 设计特点说明
    features_frame = ttk.LabelFrame(main_frame, text="设计特点", padding="15")
    features_frame.pack(fill=tk.BOTH, expand=True)
    
    features_text = """✨ 新UI设计特点:

🎯 简洁性:
  • 移除不必要的描述信息
  • 使用下拉列表替代列表框
  • 减少界面元素，突出核心功能

📐 一致性:
  • 统一的对话框尺寸和布局
  • 一致的按钮位置和样式
  • 统一的字体和间距

🚀 易用性:
  • 支持键盘快捷操作（回车确认）
  • 自动焦点设置
  • 清晰的视觉层次

🛡️ 稳定性:
  • 禁止窗口缩放
  • 模态对话框设计
  • 防止意外关闭"""
    
    ttk.Label(features_frame, text=features_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.BOTH, expand=True)
    
    print("UI对比演示界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("优化后的UI界面测试")
    print("=" * 50)
    
    print("🎨 UI优化要点:")
    print("  ✅ 工序选择改为下拉列表样式")
    print("  ✅ 移除描述信息，界面更简洁")
    print("  ✅ 按钮位置互换：退出程序在左，确认在右")
    print("  ✅ 优化布局，内容清晰可见")
    print("  ✅ 统一对话框风格和尺寸")
    
    # 创建UI对比演示
    create_ui_comparison_demo()
    
    print("\n测试完成")
