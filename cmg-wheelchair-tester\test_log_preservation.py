#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日志保留功能
验证：
1. 点击开始测试时才清除日志
2. 测试完成回到序列号输入弹窗时不清除日志
3. 可以查看上一次测试的pass/fail结果
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time
import random

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}}

def center_dialog_on_parent(parent, dialog):
    """将对话框居中显示在父窗口上"""
    try:
        parent.update_idletasks()
        dialog.update_idletasks()
        
        root_x = parent.winfo_x()
        root_y = parent.winfo_y()
        root_width = parent.winfo_width()
        root_height = parent.winfo_height()
        
        dialog_width = dialog.winfo_width()
        dialog_height = dialog.winfo_height()
        
        x = root_x + (root_width - dialog_width) // 2
        y = root_y + (root_height - dialog_height) // 2
        
        screen_width = dialog.winfo_screenwidth()
        screen_height = dialog.winfo_screenheight()
        
        x = max(0, min(x, screen_width - dialog_width))
        y = max(0, min(y, screen_height - dialog_height))
        
        dialog.geometry(f"+{x}+{y}")
    except Exception as e:
        print(f"居中对话框时出错: {e}")

def test_log_preservation():
    """测试日志保留功能"""
    print("=== 测试日志保留功能 ===")
    
    config = load_config()
    work_processes = config.get("work_processes", {})
    
    if not work_processes:
        print("❌ 没有找到工序配置")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title("日志保留功能测试")
    root.geometry("1000x800")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 1000) // 2
    y = (screen_height - 800) // 2
    root.geometry(f"1000x800+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="日志保留功能测试", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 修改说明
    change_frame = ttk.LabelFrame(main_frame, text="日志清除逻辑修改", padding="15")
    change_frame.pack(fill=tk.X, pady=(0, 20))
    
    change_text = """🔧 日志清除时机修改:

修改前:
  • 点击开始测试时清除日志
  • 测试完成回到序列号输入时也清除日志
  • 无法查看上一次测试的详细结果

修改后:
  ✅ 只有点击"开始测试"时才清除日志
  ✅ 测试完成回到序列号输入弹窗时不清除日志
  ✅ 可以查看上一次测试的pass/fail结果
  ✅ 便于对比和分析测试结果

🎯 使用场景:
  • 测试完成后可以查看详细的pass/fail结果
  • 在输入下一个序列号前可以确认上次测试状态
  • 便于问题排查和结果对比"""
    
    ttk.Label(change_frame, text=change_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 模拟测试区域
    test_frame = ttk.LabelFrame(main_frame, text="模拟测试日志保留", padding="15")
    test_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 状态显示
    status_var = tk.StringVar(value="准备开始测试...")
    status_label = ttk.Label(test_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack(pady=(0, 10))
    
    # 测试计数和统计
    stats_frame = ttk.Frame(test_frame)
    stats_frame.pack(fill=tk.X, pady=(0, 15))
    
    test_count_var = tk.StringVar(value="已完成测试: 0 个设备")
    count_label = ttk.Label(stats_frame, textvariable=test_count_var, 
                           font=("Microsoft YaHei UI", 11), 
                           foreground="green")
    count_label.pack(side=tk.LEFT)
    
    last_result_var = tk.StringVar(value="")
    result_label = ttk.Label(stats_frame, textvariable=last_result_var, 
                            font=("Microsoft YaHei UI", 11, "bold"))
    result_label.pack(side=tk.RIGHT)
    
    # 日志显示
    log_frame = ttk.Frame(test_frame)
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
    
    log_text = tk.Text(log_frame, height=18, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message, color=None):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # 插入日志
        if color:
            # 如果指定了颜色，使用标签
            start_pos = log_text.index(tk.END + "-1c")
            log_text.insert(tk.END, log_entry)
            end_pos = log_text.index(tk.END + "-1c")
            log_text.tag_add(color, start_pos, end_pos)
            log_text.tag_config(color, foreground=color)
        else:
            log_text.insert(tk.END, log_entry)
        
        log_text.see(tk.END)
        root.update_idletasks()
    
    def clear_log_silent():
        """静默清空日志（模拟开始测试时的清空）"""
        log_text.delete(1.0, tk.END)
        log_message("=== 开始新的测试 ===", "blue")
    
    # 测试状态
    test_state = {
        "selected_process": "整机半成品功能测试",
        "test_count": 0,
        "running": False,
        "last_results": []
    }
    
    def show_serial_input_dialog(title="输入序列号", prompt="请输入设备序列号", preserve_log=False):
        """显示序列号输入对话框"""
        dialog = tk.Toplevel(root)
        dialog.title(title)
        dialog.geometry("500x320")
        dialog.transient(root)
        dialog.grab_set()
        dialog.resizable(False, False)
        
        # 强制更新窗口尺寸信息
        root.update()
        dialog.update()
        
        # 等待窗口完全创建后居中
        root.after(10, lambda: center_dialog_on_parent(root, dialog))
        
        # 禁止关闭窗口
        dialog.protocol("WM_DELETE_WINDOW", lambda: None)
        
        result = {"confirmed": False, "serial_number": ""}
        
        main_frame = ttk.Frame(dialog, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_label = ttk.Label(main_frame, text=title, 
                               font=("Microsoft YaHei UI", 14, "bold"))
        title_label.pack(pady=(0, 15))
        
        # 当前工序信息
        process_info = f"当前工序: {test_state['selected_process']}"
        info_label = ttk.Label(main_frame, text=process_info, 
                              font=("Microsoft YaHei UI", 10), 
                              foreground="blue")
        info_label.pack(pady=(0, 15))
        
        # 如果保留日志，显示上次测试结果
        if preserve_log and test_state["last_results"]:
            result_frame = ttk.LabelFrame(main_frame, text="上次测试结果", padding="10")
            result_frame.pack(fill=tk.X, pady=(0, 15))
            
            last_result = test_state["last_results"][-1]
            result_text = f"设备: {last_result['serial']}\n"
            result_text += f"通过: {last_result['passed']} 项  失败: {last_result['failed']} 项\n"
            result_text += f"通过率: {last_result['pass_rate']:.1f}%"
            
            color = "green" if last_result['failed'] == 0 else "red"
            ttk.Label(result_frame, text=result_text, 
                     font=("Microsoft YaHei UI", 10), 
                     foreground=color).pack()
        
        # 序列号输入区域
        input_frame = ttk.Frame(main_frame)
        input_frame.pack(fill=tk.X, pady=(0, 20))
        input_frame.columnconfigure(1, weight=1)
        
        # 输入标签
        ttk.Label(input_frame, text="设备序列号:", 
                 font=("Microsoft YaHei UI", 11)).grid(row=0, column=0, padx=(0, 15), sticky=tk.W)
        
        # 序列号输入框
        serial_var = tk.StringVar()
        # 自动生成序列号
        auto_serial = f"CMG202412{test_state['test_count']+1:04d}"
        serial_var.set(auto_serial)
        
        serial_entry = ttk.Entry(input_frame, textvariable=serial_var, 
                               font=("Microsoft YaHei UI", 11), width=20)
        serial_entry.grid(row=0, column=1, sticky=(tk.W, tk.E))
        serial_entry.focus()
        serial_entry.select_range(0, tk.END)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)
        
        def confirm_serial():
            """确认序列号并开始测试"""
            serial_number = serial_var.get().strip()
            if serial_number:
                result["confirmed"] = True
                result["serial_number"] = serial_number
                dialog.destroy()
                
                # 开始测试（这里会清空日志）
                start_test_simulation(serial_number, clear_log=True)
            else:
                messagebox.showwarning("警告", "请输入序列号")
                serial_entry.focus()
        
        def cancel_serial():
            """取消输入"""
            result["confirmed"] = False
            dialog.destroy()
            log_message("用户取消测试，演示结束")
            status_var.set("演示结束")
        
        # 绑定回车键
        serial_entry.bind("<Return>", lambda e: confirm_serial())
        
        # 按钮区域居中布局
        button_container = ttk.Frame(button_frame)
        button_container.pack(expand=True)
        
        # 按钮布局
        start_btn = ttk.Button(button_container, text="开始测试", 
                             command=confirm_serial, 
                             style="Accent.TButton",
                             width=15)
        start_btn.pack(side=tk.LEFT, padx=(0, 20))
        
        exit_btn = ttk.Button(button_container, text="退出程序", 
                            command=cancel_serial,
                            width=15)
        exit_btn.pack(side=tk.LEFT)
        
        # 等待对话框关闭
        dialog.wait_window()
        
        return result["confirmed"], result["serial_number"]
    
    def start_test_simulation(serial_number, clear_log=False):
        """开始测试模拟"""
        test_state["running"] = True
        test_state["test_count"] += 1
        
        status_var.set(f"正在测试设备: {serial_number}")
        test_count_var.set(f"已完成测试: {test_state['test_count']-1} 个设备，当前测试第 {test_state['test_count']} 个")
        
        # 只有在开始测试时才清空日志
        if clear_log:
            clear_log_silent()
        
        log_message(f"工序: {test_state['selected_process']}")
        log_message(f"序列号: {serial_number}")
        
        def simulate_test_progress():
            test_items = [
                "设备连接检测",
                "版本信息测试", 
                "USB接口测试",
                "CAN通信测试",
                "GPS定位测试",
                "4G通信测试",
                "按键功能测试",
                "LED指示灯测试"
            ]
            
            passed = 0
            failed = 0
            
            for i, item in enumerate(test_items, 1):
                if not test_state["running"]:
                    break
                time.sleep(0.4)
                
                # 随机生成测试结果（大部分通过）
                is_pass = random.random() > 0.15  # 85%通过率
                
                if is_pass:
                    log_message(f"[{i}/{len(test_items)}] {item} - 通过 ✅", "green")
                    passed += 1
                else:
                    log_message(f"[{i}/{len(test_items)}] {item} - 失败 ❌", "red")
                    failed += 1
                
                root.update_idletasks()
            
            if test_state["running"]:
                # 记录测试结果
                pass_rate = (passed / len(test_items)) * 100
                test_result = {
                    "serial": serial_number,
                    "passed": passed,
                    "failed": failed,
                    "pass_rate": pass_rate
                }
                test_state["last_results"].append(test_result)
                
                log_message(f"设备 {serial_number} 测试完成")
                log_message(f"通过: {passed} 项，失败: {failed} 项，通过率: {pass_rate:.1f}%")
                
                # 更新显示
                test_count_var.set(f"已完成测试: {test_state['test_count']} 个设备")
                if failed == 0:
                    last_result_var.set("上次结果: 全部通过 ✅")
                    result_label.config(foreground="green")
                else:
                    last_result_var.set(f"上次结果: {failed} 项失败 ❌")
                    result_label.config(foreground="red")
                
                # 模拟测试完成后的重启循环（不清空日志）
                root.after(1500, restart_test_cycle)
        
        threading.Thread(target=simulate_test_progress, daemon=True).start()
    
    def restart_test_cycle():
        """重新开始测试循环（不清空日志）"""
        log_message("测试完成，准备开始下一轮测试")
        status_var.set("等待输入下一个设备序列号...")
        
        test_state["running"] = False
        
        # 直接弹出序列号输入窗口（保留日志）
        confirmed, serial_number = show_serial_input_dialog("输入新序列号", "请输入下一个设备的序列号", preserve_log=True)
        
        if not confirmed:
            log_message("测试循环结束")
            status_var.set("测试循环已结束")
    
    def start_demo():
        """开始演示"""
        log_message("开始日志保留功能演示")
        log_message(f"当前工序: {test_state['selected_process']}")
        
        # 显示第一个序列号输入对话框
        confirmed, serial_number = show_serial_input_dialog("输入序列号", "请输入第一个设备的序列号")
        
        if not confirmed:
            log_message("演示取消")
            status_var.set("演示已取消")
    
    def stop_demo():
        """停止演示"""
        test_state["running"] = False
        log_message("演示已停止")
        status_var.set("演示已停止")
    
    def reset_demo():
        """重置演示"""
        test_state["running"] = False
        test_state["test_count"] = 0
        test_state["last_results"] = []
        log_text.delete(1.0, tk.END)
        status_var.set("准备开始测试...")
        test_count_var.set("已完成测试: 0 个设备")
        last_result_var.set("")
        log_message("演示已重置")
    
    def manual_clear_log():
        """手动清空日志（模拟点击开始测试）"""
        clear_log_silent()
        log_message("手动清空日志（模拟点击开始测试时的行为）")
    
    # 控制按钮
    button_frame = ttk.Frame(test_frame)
    button_frame.pack(fill=tk.X)
    
    ttk.Button(button_frame, text="开始演示", 
              command=start_demo, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="停止演示", 
              command=stop_demo, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="重置演示", 
              command=reset_demo, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="手动清空日志", 
              command=manual_clear_log, width=12).pack(side=tk.RIGHT, padx=5)
    
    # 对比说明
    compare_frame = ttk.LabelFrame(main_frame, text="日志清除时机对比", padding="10")
    compare_frame.pack(fill=tk.X)
    
    compare_text = """📊 日志清除时机对比:

修改前:
  ❌ 点击开始测试时清除日志
  ❌ 测试完成回到序列号输入时也清除日志
  ❌ 无法查看上一次测试的详细pass/fail结果
  ❌ 不便于问题排查和结果对比

修改后:
  ✅ 只有点击"开始测试"时才清除日志
  ✅ 测试完成回到序列号输入时保留日志
  ✅ 可以查看上一次测试的详细pass/fail结果
  ✅ 便于确认测试状态和问题排查

🎯 实际使用场景:
  • 测试完成后可以查看哪些项目通过/失败
  • 在输入下一个序列号前可以确认上次测试结果
  • 便于对比不同设备的测试结果
  • 有助于快速定位问题项目"""
    
    ttk.Label(compare_frame, text=compare_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 9)).pack(fill=tk.X)
    
    # 初始化日志
    log_message("日志保留功能测试已准备就绪")
    log_message("点击'开始演示'体验日志保留功能")
    log_message("注意观察：测试完成后回到序列号输入时，日志会保留")
    
    print("日志保留功能测试界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("日志保留功能测试")
    print("=" * 50)
    
    print("🔧 修改内容:")
    print("  1. 只有点击开始测试时才清除日志")
    print("  2. 测试完成回到序列号输入弹窗时不清除日志")
    print("  3. 可以查看上一次测试的pass/fail结果")
    print("  4. 便于问题排查和结果对比")
    
    # 创建测试界面
    test_log_preservation()
    
    print("\n测试完成")
