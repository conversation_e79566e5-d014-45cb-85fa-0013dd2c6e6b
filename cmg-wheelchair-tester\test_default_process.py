#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试默认工序选择修复
验证程序启动时是否选择第一个工序
"""

import json
import tkinter as tk
from tkinter import ttk

def test_default_process_logic():
    """测试默认工序选择逻辑"""
    print("=== 测试默认工序选择逻辑 ===\n")
    
    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return False
    
    work_processes = config.get("work_processes", {})
    
    print(f"📊 工序配置:")
    print(f"  • 工序数量: {len(work_processes)} 个")
    
    if work_processes:
        process_names = list(work_processes.keys())
        print(f"  • 工序列表:")
        for i, name in enumerate(process_names, 1):
            print(f"    {i}. {name}")
        
        # 模拟默认选择逻辑
        default_process = process_names[0]
        print(f"\n🎯 默认选择逻辑:")
        print(f"  • 第一个工序: {default_process}")
        print(f"  • 描述: {work_processes[default_process]['description']}")
        print(f"  • 包含测试项目: {len(work_processes[default_process]['test_ids'])} 个")
        
        return True
    else:
        print(f"  ❌ 没有找到工序配置")
        return False

def test_default_process_ui():
    """测试默认工序选择UI"""
    print("\n=== 测试默认工序选择UI ===")
    
    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"❌ 加载配置失败: {e}")
        return
    
    work_processes = config.get("work_processes", {})
    test_projects = config.get("test_projects", [])
    
    # 模拟主程序的默认选择逻辑
    if work_processes:
        selected_process = list(work_processes.keys())[0]
    else:
        selected_process = "默认工序"
    
    print(f"模拟主程序默认选择: {selected_process}")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("默认工序选择测试")
    root.geometry("800x600")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="默认工序选择测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 修复说明
    fix_frame = ttk.LabelFrame(main_frame, text="修复内容", padding="10")
    fix_frame.pack(fill=tk.X, pady=(0, 20))
    
    fix_text = """🔧 修复内容:

修复前:
  self.selected_process = "完整测试"  # 硬编码，但配置中没有此工序

修复后:
  if self.work_processes:
      self.selected_process = list(self.work_processes.keys())[0]  # 动态选择第一个工序
  else:
      self.selected_process = "默认工序"  # 如果没有工序配置，使用默认值

✅ 现在程序启动时会自动选择配置中的第一个工序"""
    
    ttk.Label(fix_frame, text=fix_text, justify=tk.LEFT, font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 工序选择演示
    demo_frame = ttk.LabelFrame(main_frame, text="工序选择演示", padding="10")
    demo_frame.pack(fill=tk.X, pady=(0, 20))
    demo_frame.columnconfigure(1, weight=1)
    
    # 工序选择标签
    ttk.Label(demo_frame, text="当前默认工序:").grid(row=0, column=0, padx=(0, 10), sticky=tk.W)
    
    # 工序选择下拉框
    process_var = tk.StringVar(value=selected_process)
    process_combo = ttk.Combobox(
        demo_frame, 
        textvariable=process_var,
        values=list(work_processes.keys()),
        state="readonly",
        width=25
    )
    process_combo.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
    
    # 工序描述标签
    process_desc_var = tk.StringVar()
    desc_label = ttk.Label(demo_frame, textvariable=process_desc_var, foreground="gray")
    desc_label.grid(row=0, column=2, sticky=tk.W)
    
    # 统计信息
    stats_var = tk.StringVar()
    stats_label = ttk.Label(demo_frame, textvariable=stats_var, font=("Arial", 10, "bold"))
    stats_label.grid(row=1, column=0, columnspan=3, pady=(10, 0), sticky=tk.W)
    
    def update_process_info():
        """更新工序信息"""
        process_name = process_var.get()
        if process_name in work_processes:
            desc = work_processes[process_name]["description"]
            process_desc_var.set(f"({desc})")
            
            test_ids = work_processes[process_name]["test_ids"]
            stats_var.set(f"包含 {len(test_ids)} 个测试项目")
        else:
            process_desc_var.set("")
            stats_var.set("")
    
    # 绑定事件
    process_combo.bind("<<ComboboxSelected>>", lambda e: update_process_info())
    
    # 初始化显示
    update_process_info()
    
    # 测试项目显示
    project_frame = ttk.LabelFrame(main_frame, text="当前工序的测试项目", padding="10")
    project_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 创建测试项目列表
    project_listbox = tk.Listbox(project_frame, height=10, font=("Microsoft YaHei UI", 10))
    project_scrollbar = ttk.Scrollbar(project_frame, orient=tk.VERTICAL, command=project_listbox.yview)
    project_listbox.configure(yscrollcommand=project_scrollbar.set)
    
    project_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    project_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def update_project_list():
        """更新测试项目列表"""
        project_listbox.delete(0, tk.END)
        
        process_name = process_var.get()
        if process_name in work_processes:
            test_ids = work_processes[process_name]["test_ids"]
            
            # 显示测试项目
            for i, test_id in enumerate(test_ids, 1):
                # 查找项目名称
                project_name = test_id  # 默认显示ID
                for project in test_projects:
                    if project["id"] == test_id:
                        project_name = project["name"]
                        break
                
                project_listbox.insert(tk.END, f"{i:2d}. {project_name} ({test_id})")
    
    # 绑定项目列表更新
    def on_process_change(event=None):
        update_process_info()
        update_project_list()
    
    process_combo.bind("<<ComboboxSelected>>", on_process_change)
    
    # 初始化项目列表
    update_project_list()
    
    # 验证结果
    result_frame = ttk.LabelFrame(main_frame, text="验证结果", padding="10")
    result_frame.pack(fill=tk.X, pady=(0, 10))
    
    if work_processes:
        first_process = list(work_processes.keys())[0]
        result_text = f"✅ 验证通过！\n\n"
        result_text += f"• 配置中有 {len(work_processes)} 个工序\n"
        result_text += f"• 默认选择第一个工序: {first_process}\n"
        result_text += f"• 工序描述: {work_processes[first_process]['description']}\n"
        result_text += f"• 包含测试项目: {len(work_processes[first_process]['test_ids'])} 个"
        
        result_color = "green"
    else:
        result_text = "❌ 验证失败！没有找到工序配置"
        result_color = "red"
    
    ttk.Label(result_frame, text=result_text, foreground=result_color, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    def test_all_processes():
        """测试所有工序"""
        print("\n=== 测试所有工序 ===")
        for process_name in work_processes.keys():
            process_var.set(process_name)
            on_process_change()
            print(f"切换到工序: {process_name}")
        
        # 回到第一个工序
        if work_processes:
            first_process = list(work_processes.keys())[0]
            process_var.set(first_process)
            on_process_change()
            print(f"回到默认工序: {first_process}")
    
    def show_config_info():
        """显示配置信息"""
        info_window = tk.Toplevel(root)
        info_window.title("配置信息")
        info_window.geometry("500x400")
        
        info_text = f"配置文件信息:\n\n"
        info_text += f"工序配置 (work_processes):\n"
        
        for i, (name, config) in enumerate(work_processes.items(), 1):
            info_text += f"\n{i}. {name}\n"
            info_text += f"   描述: {config['description']}\n"
            info_text += f"   测试项目: {len(config['test_ids'])} 个\n"
            info_text += f"   项目ID: {', '.join(config['test_ids'][:5])}"
            if len(config['test_ids']) > 5:
                info_text += f" ... (共{len(config['test_ids'])}个)"
            info_text += "\n"
        
        text_widget = tk.Text(info_window, wrap=tk.WORD, font=("Consolas", 10))
        scrollbar = ttk.Scrollbar(info_window, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=10, padx=(0, 10))
        
        text_widget.insert(tk.END, info_text)
        text_widget.config(state=tk.DISABLED)
    
    ttk.Button(button_frame, text="测试所有工序", command=test_all_processes).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="配置信息", command=show_config_info).pack(side=tk.LEFT, padx=5)
    
    print("默认工序选择测试UI已创建")
    root.mainloop()

def verify_fix():
    """验证修复效果"""
    print("\n=== 验证修复效果 ===")
    
    print("🔧 修复前的问题:")
    print("  • 默认选择 '完整测试' 工序")
    print("  • 但配置文件中没有 '完整测试' 工序")
    print("  • 导致程序启动时工序选择错误")
    
    print("\n✅ 修复后的改进:")
    print("  • 动态获取第一个工序作为默认选择")
    print("  • 如果没有工序配置，使用 '默认工序' 作为备选")
    print("  • 确保程序启动时工序选择正确")
    
    print("\n📋 修复代码:")
    print("  修复前:")
    print("    self.selected_process = '完整测试'")
    print("  修复后:")
    print("    if self.work_processes:")
    print("        self.selected_process = list(self.work_processes.keys())[0]")
    print("    else:")
    print("        self.selected_process = '默认工序'")
    
    print("\n🎯 预期效果:")
    print("  • 程序启动时自动选择第一个工序")
    print("  • 工序下拉框显示正确的默认值")
    print("  • 测试项目列表显示对应工序的项目")

if __name__ == "__main__":
    print("默认工序选择修复测试")
    print("=" * 50)
    
    # 测试默认选择逻辑
    if not test_default_process_logic():
        print("❌ 默认工序选择逻辑测试失败")
        exit(1)
    
    # 验证修复效果
    verify_fix()
    
    # 测试UI
    test_default_process_ui()
    
    print("\n测试完成")
