[2025-07-11 15:39:59] 开始测试 - SN: 111111111111111
[2025-07-11 15:40:00] 
开始执行: 设备连接状态检测
[2025-07-11 15:40:00] 设备连接测试通过，检测到 1 个设备
[2025-07-11 15:40:00] 设备: ?	device
[2025-07-11 15:40:00] 
开始执行: USB关键器件检测
[2025-07-11 15:40:00] 执行USB设备检测...
[2025-07-11 15:40:00] 执行命令: adb shell lsusb
[2025-07-11 15:40:00] 设备返回数据:
[2025-07-11 15:40:00] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-11 15:40:00] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-11 15:40:00] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-11 15:40:00] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-11 15:40:00] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-11 15:40:00] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-11 15:40:00] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-11 15:40:00] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-11 15:40:00] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-11 15:40:00] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-11 15:40:00] 总共解析到 9 个设备
[2025-07-11 15:40:00] ✅ 所有预期的设备ID都已找到
[2025-07-11 15:40:00] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 15:40:00] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 15:40:00] ✅ USB设备检测通过
[2025-07-11 15:40:01] 
开始执行: CAN0测试
[2025-07-11 15:40:01] 执行CAN0测试流程...
[2025-07-11 15:40:01] 执行命令: adb shell ip link set can0 down
[2025-07-11 15:40:01] CAN0已关闭
[2025-07-11 15:40:01] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-11 15:40:01] CAN0已启动
[2025-07-11 15:40:01] CAN监听线程已启动...
[2025-07-11 15:40:02] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-11 15:40:02] CAN测试数据已发送，等待监听返回...
[2025-07-11 15:40:02] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-11 15:40:04] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-11 15:40:05] 
开始执行: GPS测试
[2025-07-11 15:40:05] 执行GPS测试...
[2025-07-11 15:40:05] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-11 15:40:15] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-11 15:40:15] 
开始执行: 4G模组测试
[2025-07-11 15:40:15] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-11 15:40:15] 监听线程已启动，等待串口数据...
[2025-07-11 15:40:17] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-11 15:40:17] AT+CCID指令已发送，等待串口返回...
[2025-07-11 15:40:17] 串口输出: AT+CCID
[2025-07-11 15:40:17] 串口输出: 
[2025-07-11 15:40:17] 串口输出: 
[2025-07-11 15:40:17] 串口输出: +CCID: 89860114851012535241
[2025-07-11 15:40:19] ✅ 4G模组测试成功，CCID: 89860114851012535241
[2025-07-11 15:40:20] 
开始执行: 按键测试
[2025-07-11 15:40:20] 开始按键测试...
[2025-07-11 15:40:20] 执行命令: adb shell evtest /dev/input/event5
[2025-07-11 15:40:23] 原始事件: Event: time 1751328033.881212, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 15:40:23] 解析结果: key_code=662, value=1
[2025-07-11 15:40:23] ✓ 检测到档位减(662)按下
[2025-07-11 15:40:24] 原始事件: Event: time 1751328034.134592, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 15:40:24] 解析结果: key_code=662, value=0
[2025-07-11 15:40:24] ✓ 档位减(662)测试通过
[2025-07-11 15:40:25] 原始事件: Event: time 1752219626.434990, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-11 15:40:25] 解析结果: key_code=658, value=1
[2025-07-11 15:40:25] ✓ 检测到喇叭键(658)按下
[2025-07-11 15:40:25] 原始事件: Event: time 1752219626.734969, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-11 15:40:25] 解析结果: key_code=658, value=0
[2025-07-11 15:40:25] ✓ 喇叭键(658)测试通过
[2025-07-11 15:40:27] 原始事件: Event: time 1752219629.021612, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-11 15:40:27] 解析结果: key_code=661, value=1
[2025-07-11 15:40:27] ✓ 检测到静音键(661)按下
[2025-07-11 15:40:28] 原始事件: Event: time 1752219629.248312, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-11 15:40:28] 解析结果: key_code=661, value=0
[2025-07-11 15:40:28] ✓ 静音键(661)测试通过
[2025-07-11 15:40:28] 原始事件: Event: time 1752219629.998367, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 15:40:28] 解析结果: key_code=663, value=1
[2025-07-11 15:40:28] ✓ 检测到语音键(663)按下
[2025-07-11 15:40:29] 原始事件: Event: time 1752219630.198290, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 15:40:29] 解析结果: key_code=663, value=0
[2025-07-11 15:40:29] ✓ 语音键(663)测试通过
[2025-07-11 15:40:29] 原始事件: Event: time 1752219630.898266, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 15:40:29] 解析结果: key_code=657, value=1
[2025-07-11 15:40:29] ✓ 检测到SOS键(657)按下
[2025-07-11 15:40:30] 原始事件: Event: time 1752219631.121819, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 15:40:30] 解析结果: key_code=657, value=0
[2025-07-11 15:40:30] ✓ SOS键(657)测试通过
[2025-07-11 15:40:31] 原始事件: Event: time 1752219633.198410, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-11 15:40:32] 解析结果: key_code=659, value=1
[2025-07-11 15:40:32] ✓ 检测到档位加(659)按下
[2025-07-11 15:40:32] 原始事件: Event: time 1752219633.321620, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-11 15:40:32] 解析结果: key_code=659, value=0
[2025-07-11 15:40:32] ✓ 档位加(659)测试通过
[2025-07-11 15:40:33] 原始事件: Event: time 1752219634.248489, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 15:40:33] 解析结果: key_code=660, value=1
[2025-07-11 15:40:33] ✓ 检测到智驾键(660)按下
[2025-07-11 15:40:33] 原始事件: Event: time 1752219634.448299, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 15:40:33] 解析结果: key_code=660, value=0
[2025-07-11 15:40:33] ✓ 智驾键(660)测试通过
[2025-07-11 15:40:34] 原始事件: Event: time 1752219635.698289, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 15:40:34] 解析结果: key_code=656, value=1
[2025-07-11 15:40:34] ✓ 检测到锁定键(656)按下
[2025-07-11 15:40:34] 原始事件: Event: time 1752219635.948264, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 15:40:34] 解析结果: key_code=656, value=0
[2025-07-11 15:40:34] ✓ 锁定键(656)测试通过
[2025-07-11 15:40:34] 按键测试完成 - 检测到8个按键
[2025-07-11 15:40:37] 
开始执行: 按键灯测试
[2025-07-11 15:40:37] 开始LED背光灯测试...
[2025-07-11 15:40:37] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-11 15:40:37] LED控制命令执行成功
[2025-07-11 15:40:40] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-11 15:40:40] LED灯已关闭
[2025-07-11 15:40:40] LED测试通过 - 背光灯正常点亮
[2025-07-11 15:40:40] 
开始执行: 手电筒测试
[2025-07-11 15:40:40] 开始手电筒LED测试...
[2025-07-11 15:40:40] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-11 15:40:40] 手电筒控制命令执行成功
[2025-07-11 15:40:41] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-11 15:40:41] 手电筒已关闭
[2025-07-11 15:40:41] 手电筒测试通过 - 手电筒正常点亮
[2025-07-11 15:40:42] 
开始执行: 摇杆使能测试
[2025-07-11 15:40:42] 执行摇杆测试...
[2025-07-11 15:40:42] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-11 15:40:42] 命令执行成功
[2025-07-11 15:40:42] 返回数据: 255
[2025-07-11 15:40:42] 摇杆测试通过，值: 255
[2025-07-11 15:40:42] 
开始执行: 前摄像头测试
[2025-07-11 15:40:42] 开始执行前摄像头测试...
[2025-07-11 15:40:42] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-11 15:40:42] 命令执行成功，返回: 无输出
[2025-07-11 15:40:42] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-11 15:40:44] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.322352146
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-11 15:40:44] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-11 15:40:44] 拉取命令执行成功
[2025-07-11 15:40:44] 返回数据: [ 42%] /data/camera/cam0_3840x2160.jpg
[ 85%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 22.5 MB/s (153412 bytes in 0.007s)
[2025-07-11 15:40:44] 图片已保存: cam0_3840x2160.jpg
[2025-07-11 15:40:45] 用户确认结果: 通过
[2025-07-11 15:40:46] 
开始执行: 光感测试
[2025-07-11 15:40:46] 执行光感测试...
[2025-07-11 15:40:46] 执行命令: adb shell evtest /dev/input/event1
[2025-07-11 15:40:49] 光感测试完成 - 检测到数值变化
[2025-07-11 15:40:49] 数值从 4 变化到 1
[2025-07-11 15:40:50] 
开始执行: 回充摄像头测试
[2025-07-11 15:40:50] 开始执行回充摄像头测试...
[2025-07-11 15:40:50] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-11 15:40:50] 命令执行成功，返回: 无输出
[2025-07-11 15:40:50] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-11 15:40:51] 命令执行成功，返回: 无输出
[2025-07-11 15:40:51] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-11 15:40:51] 拉取命令执行成功
[2025-07-11 15:40:51] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 8.3 MB/s (45472 bytes in 0.005s)
[2025-07-11 15:40:51] 图片已保存: output.jpg
[2025-07-11 15:40:53] 用户确认结果: 通过
[2025-07-11 15:40:53] 
开始执行: 喇叭测试
[2025-07-11 15:40:53] 执行喇叭测试...
[2025-07-11 15:40:53] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-11 15:41:07] 命令执行成功
[2025-07-11 15:41:07] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-11 15:41:07] 音频播放完成
[2025-07-11 15:41:07] 
开始执行: 蓝牙测试
[2025-07-11 15:41:08] 执行蓝牙测试...
[2025-07-11 15:41:08] 启动蓝牙服务...
[2025-07-11 15:41:08] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-11 15:41:08] 开启蓝牙...
[2025-07-11 15:41:08] 执行命令: adb shell bluetoothctl devices
[2025-07-11 15:41:08] 命令执行成功
[2025-07-11 15:41:08] 无输出数据
[2025-07-11 15:41:08] 
开始执行: WiFi测试
[2025-07-11 15:41:08] 执行WiFi测试...
[2025-07-11 15:41:08] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-11 15:41:08] 命令执行成功，无输出
[2025-07-11 15:41:08] 执行命令 2: adb shell rm -f /var/run/wpa_supplicant/wlan0
[2025-07-11 15:41:08] 命令执行成功，无输出
[2025-07-11 15:41:08] 执行命令 3: adb shell ip link set wlan0 down
[2025-07-11 15:41:08] 命令执行成功，无输出
[2025-07-11 15:41:08] 执行命令 4: adb shell wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && wpa_cli -i wlan0 add_network && wpa_cli -i wlan0 set_network 0 ssid '"Orion_SZ_5G"' && wpa_cli -i wlan0 set_network 0 psk '"Orion@2025"' && wpa_cli -i wlan0 enable_network 0 && udhcpc -i wlan0 && iw wlan0 link && ip addr show wlan0
[2025-07-11 15:41:12] 命令执行成功，返回数据:
[2025-07-11 15:41:12]   Successfully initialized wpa_supplicant
[2025-07-11 15:41:12]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-11 15:41:12]   1
[2025-07-11 15:41:12]   OK
[2025-07-11 15:41:12]   OK
[2025-07-11 15:41:12]   OK
[2025-07-11 15:41:12]   deleting routers
[2025-07-11 15:41:12]   adding dns ************
[2025-07-11 15:41:12]   adding dns ***********
[2025-07-11 15:41:12]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-11 15:41:12]   SSID: Orion_SZ_5G
[2025-07-11 15:41:12]   freq: 5300
[2025-07-11 15:41:12]   RX: 2664 bytes (6 packets)
[2025-07-11 15:41:12]   TX: 1928 bytes (11 packets)
[2025-07-11 15:41:12]   signal: -54 dBm
[2025-07-11 15:41:12]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-11 15:41:12]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-11 15:41:12]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-11 15:41:12]   link/ether 24:21:5e:c0:3c:b4 brd ff:ff:ff:ff:ff:ff
[2025-07-11 15:41:12]   inet 192.168.20.178/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-11 15:41:12]   valid_lft forever preferred_lft forever
[2025-07-11 15:41:12]   inet6 fe80::4a5a:6bc9:b80c:e70/64 scope link
[2025-07-11 15:41:12]   valid_lft forever preferred_lft forever
[2025-07-11 15:41:12] 执行命令 5: adb shell ping -c 3 www.baidu.com
[2025-07-11 15:41:14] 命令执行成功，返回数据:
[2025-07-11 15:41:14]   PING www.a.shifen.com (157.148.69.186) 56(84) bytes of data.
[2025-07-11 15:41:14]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=1 ttl=54 time=9.23 ms
[2025-07-11 15:41:14]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=2 ttl=54 time=10.0 ms
[2025-07-11 15:41:14]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=3 ttl=54 time=12.2 ms
[2025-07-11 15:41:14]   --- www.a.shifen.com ping statistics ---
[2025-07-11 15:41:14]   3 packets transmitted, 3 received, 0% packet loss, time 2003ms
[2025-07-11 15:41:14]   rtt min/avg/max/mdev = 9.229/10.493/12.227/1.268 ms
[2025-07-11 15:41:14] ✅ ping测试成功，网络连通性正常
[2025-07-11 15:41:14] WiFi连接成功
[2025-07-11 15:41:14] 
测试完成 - 通过率: 13/15
[2025-07-11 15:41:14] ❌ 存在测试失败项！
[2025-07-11 15:41:14] 测试记录已保存: records/111111111111111_20250711_154114.json
[2025-07-11 15:41:14] 测试日志已保存: records/111111111111111_20250711_154114.log

