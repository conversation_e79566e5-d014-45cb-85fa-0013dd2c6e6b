[2025-07-17 20:04:48] 日志已清空
[2025-07-17 20:04:51] 开始测试 - SN: 11111111
[2025-07-17 20:04:51] 开始运行 右手臂功能测试 工序测试，共 16 个项目
[2025-07-17 20:04:51] 
开始执行: 设备连接状态检测
[2025-07-17 20:04:51] 设备连接测试失败：未检测到设备
[2025-07-17 20:04:51] 
开始执行: 3568版本测试
[2025-07-17 20:04:51] 执行3568版本测试...
[2025-07-17 20:04:51] 读取ROM版本号...
[2025-07-17 20:04:51] 执行命令: adb shell uname -a
[2025-07-17 20:04:51] ❌ uname命令执行失败: error: no devices/emulators found

[2025-07-17 20:04:51] 
开始执行: USB关键器件检测
[2025-07-17 20:04:51] 执行USB设备检测...
[2025-07-17 20:04:51] 执行命令: adb shell lsusb
[2025-07-17 20:04:51] 命令执行失败: error: no devices/emulators found

[2025-07-17 20:04:51] 
开始执行: CAN0测试
[2025-07-17 20:04:51] 执行CAN0测试流程...
[2025-07-17 20:04:51] 执行命令: adb shell ip link set can0 down
[2025-07-17 20:04:51] CAN0 down失败: error: no devices/emulators found

[2025-07-17 20:04:52] 
开始执行: GPS测试
[2025-07-17 20:04:52] 执行GPS测试...
[2025-07-17 20:04:52] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-17 20:04:52] 命令执行失败: error: no devices/emulators found

[2025-07-17 20:04:52] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-17 20:04:52] 命令执行失败: error: no devices/emulators found

[2025-07-17 20:04:52] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-17 20:04:52] 命令执行失败: error: no devices/emulators found

[2025-07-17 20:04:52] GPS信号检测失败
[2025-07-17 20:04:52] 
开始执行: 4G模组测试
[2025-07-17 20:04:52] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-17 20:04:52] 监听线程已启动，等待串口数据...
[2025-07-17 20:04:54] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-17 20:04:54] AT指令发送失败: error: no devices/emulators found

[2025-07-17 20:04:54] 
开始执行: 按键测试
[2025-07-17 20:04:54] 开始按键测试...
[2025-07-17 20:04:54] 执行命令: adb shell evtest /dev/input/event5
[2025-07-17 20:04:57] 按键测试失败 - 只检测到0个按键
[2025-07-17 20:04:59] 
开始执行: 按键灯测试
[2025-07-17 20:04:59] 开始LED背光灯测试...
[2025-07-17 20:04:59] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-17 20:04:59] LED控制命令执行失败: error: no devices/emulators found

[2025-07-17 20:04:59] 
开始执行: 手电筒测试
[2025-07-17 20:05:00] 开始手电筒LED测试...
[2025-07-17 20:05:00] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-17 20:05:00] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-17 20:05:00] 
开始执行: 摇杆使能测试
[2025-07-17 20:05:00] 执行摇杆测试...
[2025-07-17 20:05:00] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-17 20:05:00] 命令执行失败: error: no devices/emulators found

[2025-07-17 20:05:00] 
开始执行: 前摄像头测试
[2025-07-17 20:05:00] 开始执行前摄像头测试...
[2025-07-17 20:05:01] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-17 20:05:01] 命令执行失败: error: no devices/emulators found

[2025-07-17 20:05:01] 
开始执行: 光感测试
[2025-07-17 20:05:01] 执行光感测试...
[2025-07-17 20:05:01] 执行命令: adb shell evtest /dev/input/event1
[2025-07-17 20:05:01] 光感测试失败 - 未检测到数值变化
[2025-07-17 20:05:01] 
开始执行: 回充摄像头测试
[2025-07-17 20:05:01] 开始执行回充摄像头测试...
[2025-07-17 20:05:01] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-17 20:05:01] 命令执行失败: error: no devices/emulators found

[2025-07-17 20:05:02] 
开始执行: 喇叭测试
[2025-07-17 20:05:02] 执行喇叭测试...
[2025-07-17 20:05:02] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-17 20:05:02] 命令执行失败: error: no devices/emulators found

[2025-07-17 20:05:02] 
开始执行: 蓝牙测试
[2025-07-17 20:05:02] 执行蓝牙测试...
[2025-07-17 20:05:02] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-17 20:05:02] 执行命令: adb shell bluetoothctl show
[2025-07-17 20:05:02] ❌ 命令执行失败: error: no devices/emulators found

[2025-07-17 20:05:02] 
开始执行: WiFi测试
[2025-07-17 20:05:02] 执行WiFi测试...
[2025-07-17 20:05:02] 第一步：连接WiFi网络...
[2025-07-17 20:05:02] 停止现有的wpa_supplicant进程...
[2025-07-17 20:05:02] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-17 20:05:03] 清理wpa_supplicant socket文件...
[2025-07-17 20:05:03] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-17 20:05:03] 关闭wlan0接口...
[2025-07-17 20:05:03] 执行命令: adb shell ip link set wlan0 down
[2025-07-17 20:05:03] 连接WiFi网络...
[2025-07-17 20:05:03] 执行WiFi连接命令...
[2025-07-17 20:05:03] SSID: Orion_SZ_5G
[2025-07-17 20:05:03] ❌ WiFi连接失败: error: no devices/emulators found

[2025-07-17 20:05:03] 
测试完成 - 通过率: 0/16
[2025-07-17 20:05:03] ❌ 存在测试失败项！
[2025-07-17 20:05:03] 测试记录已保存: records/11111111_20250717_200503.json
[2025-07-17 20:05:03] 测试日志已保存: records/11111111_20250717_200503.log

