[2025-07-05 22:06:52] 开始测试 - SN: 1111
[2025-07-05 22:06:52] 
开始执行: 设备连接状态检测
[2025-07-05 22:06:52] 设备连接测试失败：未检测到设备
[2025-07-05 22:06:52] 
开始执行: USB关键器件检测
[2025-07-05 22:06:52] 未知的测试类型：device_match
[2025-07-05 22:06:52] 
开始执行: CAN0测试
[2025-07-05 22:06:52] 执行CAN0测试流程...
[2025-07-05 22:06:52] 执行命令: adb shell ip link set can0 down
[2025-07-05 22:06:52] CAN0 down失败: error: no devices/emulators found

[2025-07-05 22:06:53] 
开始执行: GPS测试
[2025-07-05 22:06:53] 执行GPS测试...
[2025-07-05 22:06:53] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-05 22:06:53] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:53] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-05 22:06:53] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:53] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-05 22:06:53] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:53] GPS信号检测失败
[2025-07-05 22:06:53] 
开始执行: 4G模组测试
[2025-07-05 22:06:53] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-05 22:06:53] 监听线程已启动，等待串口数据...
[2025-07-05 22:06:55] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-05 22:06:55] AT指令发送失败: error: no devices/emulators found

[2025-07-05 22:06:55] 
开始执行: 按键测试
[2025-07-05 22:06:55] 执行按键测试...
[2025-07-05 22:06:55] 请按手柄按键进行测试...
[2025-07-05 22:06:56] 
开始执行: 按键灯测试
[2025-07-05 22:06:56] 开始LED背光灯测试...
[2025-07-05 22:06:56] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-05 22:06:56] LED控制命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:56] 
开始执行: 手电筒测试
[2025-07-05 22:06:56] 开始手电筒LED测试...
[2025-07-05 22:06:56] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-05 22:06:56] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:56] 
开始执行: 摇杆使能测试
[2025-07-05 22:06:56] 执行摇杆测试...
[2025-07-05 22:06:56] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-05 22:06:56] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:57] 
开始执行: 前摄像头测试
[2025-07-05 22:06:57] 开始执行前摄像头测试...
[2025-07-05 22:06:57] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-05 22:06:57] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:57] 
开始执行: 光感测试
[2025-07-05 22:06:57] 执行光感测试...
[2025-07-05 22:06:57] 执行命令: adb shell evtest /dev/input/event1
[2025-07-05 22:06:57] 光感测试失败 - 未检测到数值变化
[2025-07-05 22:06:58] 
开始执行: 回充摄像头测试
[2025-07-05 22:06:58] 开始执行回充摄像头测试...
[2025-07-05 22:06:58] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-05 22:06:58] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:58] 
开始执行: 喇叭测试
[2025-07-05 22:06:58] 执行喇叭测试...
[2025-07-05 22:06:58] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-05 22:06:58] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:58] 
开始执行: 蓝牙测试
[2025-07-05 22:06:58] 执行蓝牙测试...
[2025-07-05 22:06:58] 启动蓝牙服务...
[2025-07-05 22:06:58] 启动蓝牙服务失败: error: no devices/emulators found

[2025-07-05 22:06:58] 开启蓝牙...
[2025-07-05 22:06:58] 开启蓝牙失败: error: no devices/emulators found

[2025-07-05 22:06:58] 执行命令: adb shell bluetoothctl devices
[2025-07-05 22:06:58] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:59] 
开始执行: WiFi测试
[2025-07-05 22:06:59] 执行WiFi测试...
[2025-07-05 22:06:59] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-05 22:06:59] 命令执行失败: error: no devices/emulators found

[2025-07-05 22:06:59] 
测试完成 - 通过率: 1/15
[2025-07-05 22:06:59] ❌ 存在测试失败项！
[2025-07-05 22:06:59] 测试记录已保存: records/1111_20250705_220659.json
[2025-07-05 22:06:59] 测试日志已保存: records/1111_20250705_220659.log

