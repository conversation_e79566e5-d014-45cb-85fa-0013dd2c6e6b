#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试确认对话框按钮显示
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_dialog_buttons():
    """测试对话框按钮显示"""
    print("=== 测试确认对话框按钮显示 ===")
    
    root = tk.Tk()
    root.title("对话框按钮测试")
    root.geometry("400x300")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="确认对话框按钮测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    result_var = tk.StringVar(value="点击按钮测试对话框...")
    result_label = ttk.Label(main_frame, textvariable=result_var, font=("Arial", 12))
    result_label.pack(pady=10)
    
    def show_timeout_confirmation(title, message):
        """显示带超时的确认对话框"""
        print(f"显示对话框: {title}")
        
        # 创建自定义对话框
        dialog = tk.Toplevel(root)
        dialog.title(title)
        dialog.geometry("500x300")  # 增加窗口大小
        dialog.resizable(False, False)
        dialog.transient(root)
        dialog.grab_set()
        
        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")
        
        result = {"answer": None, "timeout": False}
        
        # 主框架
        dialog_frame = ttk.Frame(dialog, padding="30")  # 增加内边距
        dialog_frame.pack(fill=tk.BOTH, expand=True)
        
        # 消息文本
        message_label = ttk.Label(
            dialog_frame, 
            text=message, 
            justify=tk.CENTER, 
            wraplength=400,
            font=("Microsoft YaHei UI", 11)  # 设置字体
        )
        message_label.pack(pady=(0, 25))
        
        # 倒计时显示
        countdown_var = tk.StringVar(value="剩余时间: 60秒")
        countdown_label = ttk.Label(
            dialog_frame, 
            textvariable=countdown_var, 
            foreground="red",
            font=("Microsoft YaHei UI", 12, "bold")  # 加粗字体
        )
        countdown_label.pack(pady=(0, 25))
        
        # 按钮框架
        button_frame = ttk.Frame(dialog_frame)
        button_frame.pack(pady=10)
        
        print("创建按钮...")
        
        def on_yes():
            result["answer"] = "yes"
            print("👤 用户选择: 是 (测试通过)")
            dialog.destroy()
        
        def on_no():
            result["answer"] = "no"
            print("👤 用户选择: 否 (测试失败)")
            dialog.destroy()
        
        def on_timeout():
            result["timeout"] = True
            result["answer"] = "timeout"  # 超时状态，区别于用户主动选择
            print("⏰ 确认对话框超时 - 1分钟内无响应，自动判定为失败")
            dialog.destroy()
        
        # 按钮 - 增加按钮大小和样式
        yes_button = ttk.Button(
            button_frame, 
            text="是 (通过)", 
            command=on_yes,
            width=15
        )
        yes_button.pack(side=tk.LEFT, padx=(0, 20))
        print("创建了'是'按钮")
        
        no_button = ttk.Button(
            button_frame, 
            text="否 (失败)", 
            command=on_no,
            width=15
        )
        no_button.pack(side=tk.LEFT)
        print("创建了'否'按钮")
        
        # 倒计时更新
        remaining_time = [10]  # 使用10秒进行快速测试
        
        def update_countdown():
            if remaining_time[0] > 0:
                countdown_var.set(f"剩余时间: {remaining_time[0]}秒")
                remaining_time[0] -= 1
                dialog.after(1000, update_countdown)
            else:
                on_timeout()
        
        # 开始倒计时
        update_countdown()
        
        # 绑定键盘事件
        dialog.bind("<Return>", lambda e: on_yes())
        dialog.bind("<Escape>", lambda e: on_no())
        dialog.bind("<y>", lambda e: on_yes())
        dialog.bind("<n>", lambda e: on_no())
        
        print("等待用户响应...")
        
        # 等待窗口关闭
        dialog.wait_window()
        
        return result["answer"], result["timeout"]
    
    def test_led_dialog():
        """测试LED确认对话框"""
        result_var.set("显示LED测试确认对话框...")
        root.update()
        
        confirm, is_timeout = show_timeout_confirmation(
            "LED测试确认",
            "请确认：\n1. 背光灯是否已点亮？\n2. 背光灯颜色是否为蓝色？\n\n注意：10秒内无响应将自动判定为失败"
        )
        
        if is_timeout:
            result_var.set("结果: 超时失败 ⏰")
        elif confirm == "yes":
            result_var.set("结果: 用户选择'是' ✅")
        elif confirm == "no":
            result_var.set("结果: 用户选择'否' ❌")
        else:
            result_var.set("结果: 未知响应 ❓")
    
    def test_flashlight_dialog():
        """测试手电筒确认对话框"""
        result_var.set("显示手电筒测试确认对话框...")
        root.update()
        
        confirm, is_timeout = show_timeout_confirmation(
            "手电筒测试确认",
            "请确认：\n1. 手电筒是否已点亮？\n2. 亮度是否正常？\n\n注意：10秒内无响应将自动判定为失败"
        )
        
        if is_timeout:
            result_var.set("结果: 超时失败 ⏰")
        elif confirm == "yes":
            result_var.set("结果: 用户选择'是' ✅")
        elif confirm == "no":
            result_var.set("结果: 用户选择'否' ❌")
        else:
            result_var.set("结果: 未知响应 ❓")
    
    def test_simple_dialog():
        """测试简单对话框"""
        result_var.set("显示简单测试对话框...")
        root.update()
        
        confirm, is_timeout = show_timeout_confirmation(
            "简单测试",
            "这是一个简单的测试对话框\n请选择'是'或'否'\n\n倒计时10秒"
        )
        
        if is_timeout:
            result_var.set("结果: 超时失败 ⏰")
        elif confirm == "yes":
            result_var.set("结果: 用户选择'是' ✅")
        elif confirm == "no":
            result_var.set("结果: 用户选择'否' ❌")
        else:
            result_var.set("结果: 未知响应 ❓")
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=20)
    
    led_button = ttk.Button(
        button_frame,
        text="测试LED对话框",
        command=test_led_dialog
    )
    led_button.pack(side=tk.LEFT, padx=(0, 10))
    
    flashlight_button = ttk.Button(
        button_frame,
        text="测试手电筒对话框",
        command=test_flashlight_dialog
    )
    flashlight_button.pack(side=tk.LEFT, padx=(0, 10))
    
    simple_button = ttk.Button(
        button_frame,
        text="测试简单对话框",
        command=test_simple_dialog
    )
    simple_button.pack(side=tk.LEFT)
    
    # 说明文本
    info_frame = ttk.LabelFrame(main_frame, text="测试说明", padding="10")
    info_frame.pack(fill=tk.X, pady=20)
    
    info_text = """测试功能：
• 验证确认对话框是否正确显示"是"和"否"按钮
• 测试倒计时功能（设置为10秒快速测试）
• 验证键盘快捷键：Enter确认，Escape取消，Y/N快速选择
• 测试超时自动关闭功能

如果看不到按钮，请检查：
1. 窗口大小是否足够
2. 按钮是否被其他元素遮挡
3. 字体和样式是否正确加载"""
    
    info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
    info_label.pack(fill=tk.X)
    
    print("对话框按钮测试窗口已创建")
    print("点击按钮测试不同的确认对话框")
    
    root.mainloop()

if __name__ == "__main__":
    print("确认对话框按钮显示测试")
    print("=" * 50)
    
    test_dialog_buttons()
