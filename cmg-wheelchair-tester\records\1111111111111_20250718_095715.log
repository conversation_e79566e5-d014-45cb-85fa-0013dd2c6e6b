[2025-07-18 09:57:00] 日志已清空
[2025-07-18 09:57:04] 开始测试 - SN: 1111111111111
[2025-07-18 09:57:04] 开始运行 整机半成品功能测试 工序测试，共 21 个项目
[2025-07-18 09:57:04] 
开始执行: 设备连接状态检测
[2025-07-18 09:57:05] 设备连接测试失败：未检测到设备
[2025-07-18 09:57:05] 
开始执行: 3568版本测试
[2025-07-18 09:57:05] 执行3568版本测试...
[2025-07-18 09:57:05] 读取ROM版本号...
[2025-07-18 09:57:05] 执行命令: adb shell uname -a
[2025-07-18 09:57:05] ❌ uname命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:05] 
开始执行: USB关键器件检测
[2025-07-18 09:57:05] 执行USB设备检测...
[2025-07-18 09:57:05] 执行命令: adb shell lsusb
[2025-07-18 09:57:05] 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:05] 
开始执行: CAN0测试
[2025-07-18 09:57:05] 执行CAN0测试流程...
[2025-07-18 09:57:05] 执行命令: adb shell ip link set can0 down
[2025-07-18 09:57:05] CAN0 down失败: error: no devices/emulators found

[2025-07-18 09:57:05] 
开始执行: GPS测试
[2025-07-18 09:57:05] 执行GPS测试...
[2025-07-18 09:57:05] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-18 09:57:05] 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:05] 执行命令 2: adb shell cat /dev/ttyUSB4 |grep GNGSV
[2025-07-18 09:57:05] 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:05] 执行命令 3: adb shell cat /dev/ttyUSB4 |grep GBGSV
[2025-07-18 09:57:05] 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:05] GPS信号检测失败
[2025-07-18 09:57:05] 
开始执行: 4G模组测试
[2025-07-18 09:57:05] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-18 09:57:05] 监听线程已启动，等待串口数据...
[2025-07-18 09:57:07] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-18 09:57:07] AT指令发送失败: error: no devices/emulators found

[2025-07-18 09:57:08] 
开始执行: 按键测试
[2025-07-18 09:57:08] 开始按键测试...
[2025-07-18 09:57:08] 执行命令: adb shell evtest /dev/input/event5
[2025-07-18 09:57:10] 按键测试失败 - 只检测到0个按键
[2025-07-18 09:57:12] 
开始执行: 按键灯测试
[2025-07-18 09:57:12] 开始LED背光灯测试...
[2025-07-18 09:57:12] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-18 09:57:12] LED控制命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:12] 
开始执行: 手电筒测试
[2025-07-18 09:57:12] 开始手电筒LED测试...
[2025-07-18 09:57:12] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-18 09:57:12] 手电筒控制命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:12] 
开始执行: 摇杆使能测试
[2025-07-18 09:57:12] 执行摇杆测试...
[2025-07-18 09:57:12] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-18 09:57:12] 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:13] 
开始执行: 前摄像头测试
[2025-07-18 09:57:13] 开始执行前摄像头测试...
[2025-07-18 09:57:13] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-18 09:57:13] 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:13] 
开始执行: 光感测试
[2025-07-18 09:57:13] 执行光感测试...
[2025-07-18 09:57:13] 执行命令: adb shell evtest /dev/input/event1
[2025-07-18 09:57:13] 光感测试失败 - 未检测到数值变化
[2025-07-18 09:57:13] 
开始执行: 回充摄像头测试
[2025-07-18 09:57:13] 开始执行回充摄像头测试...
[2025-07-18 09:57:14] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-18 09:57:14] 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:14] 
开始执行: 喇叭测试
[2025-07-18 09:57:14] 执行喇叭测试...
[2025-07-18 09:57:14] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-18 09:57:14] 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:14] 
开始执行: 蓝牙测试
[2025-07-18 09:57:14] 执行蓝牙测试...
[2025-07-18 09:57:14] 使用bluetoothctl show命令获取蓝牙控制器信息...
[2025-07-18 09:57:14] 执行命令: adb shell bluetoothctl show
[2025-07-18 09:57:14] ❌ 命令执行失败: error: no devices/emulators found

[2025-07-18 09:57:14] 
开始执行: WiFi测试
[2025-07-18 09:57:14] 执行WiFi测试...
[2025-07-18 09:57:14] 第一步：连接WiFi网络...
[2025-07-18 09:57:14] 停止现有的wpa_supplicant进程...
[2025-07-18 09:57:14] 执行命令: adb shell "killall wpa_supplicant 2>/dev/null"
[2025-07-18 09:57:14] 清理wpa_supplicant socket文件...
[2025-07-18 09:57:14] 执行命令: adb shell "rm -f /var/run/wpa_supplicant/wlan0"
[2025-07-18 09:57:14] 关闭wlan0接口...
[2025-07-18 09:57:14] 执行命令: adb shell ip link set wlan0 down
[2025-07-18 09:57:14] 连接WiFi网络...
[2025-07-18 09:57:14] 执行WiFi连接命令...
[2025-07-18 09:57:14] SSID: Orion_SZ_5G
[2025-07-18 09:57:14] ❌ WiFi连接失败: error: no devices/emulators found

[2025-07-18 09:57:14] 
开始执行: Accel
[2025-07-18 09:57:14] 执行Accel传感器FPS测试...
[2025-07-18 09:57:14] 读取Accel传感器FPS数据...
[2025-07-18 09:57:14] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 09:57:14] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 09:57:14] 
开始执行: Gyro
[2025-07-18 09:57:14] 执行Gyro传感器FPS测试...
[2025-07-18 09:57:14] 读取Gyro传感器FPS数据...
[2025-07-18 09:57:14] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 09:57:14] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 09:57:14] 
开始执行: Laser
[2025-07-18 09:57:14] 执行Laser传感器FPS测试...
[2025-07-18 09:57:14] 读取Laser传感器FPS数据...
[2025-07-18 09:57:14] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 09:57:14] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 09:57:14] 
开始执行: Laser2
[2025-07-18 09:57:15] 执行Laser2传感器FPS测试...
[2025-07-18 09:57:15] 读取Laser2传感器FPS数据...
[2025-07-18 09:57:15] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 09:57:15] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 09:57:15] 
开始执行: Odom
[2025-07-18 09:57:15] 执行Odom传感器FPS测试...
[2025-07-18 09:57:15] 读取Odom传感器FPS数据...
[2025-07-18 09:57:15] 执行命令: adb shell cat /sdcard/lmv/normal_logs/sensor/sensor_newest
[2025-07-18 09:57:15] ❌ 传感器日志读取失败: error: no devices/emulators found

[2025-07-18 09:57:15] 
测试完成 - 通过率: 0/21
[2025-07-18 09:57:15] ❌ 存在测试失败项！
[2025-07-18 09:57:15] 测试记录已保存: records/1111111111111_20250718_095715.json

