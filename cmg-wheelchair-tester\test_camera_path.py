#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试前摄像头图片路径问题
"""

import subprocess
import json
import os

def test_camera_paths():
    """测试摄像头图片路径"""
    print("=== 前摄像头图片路径测试 ===\n")
    
    # 加载配置文件
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"加载配置文件失败: {e}")
        return
    
    # 查找前摄像头测试配置
    front_camera = None
    for project in config["test_projects"]:
        if project["id"] == "front_camera_test":
            front_camera = project
            break
    
    if not front_camera:
        print("未找到前摄像头测试配置")
        return
    
    print(f"前摄像头配置:")
    print(f"  ID: {front_camera['id']}")
    print(f"  名称: {front_camera['name']}")
    print(f"  输出文件: {front_camera['output_file']}")
    print(f"  命令:")
    for i, cmd in enumerate(front_camera['commands']):
        print(f"    {i+1}. {cmd}")
    print()
    
    # 检查设备连接
    print("检查ADB连接...")
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            devices = result.stdout.strip().split('\n')[1:]
            connected_devices = [d for d in devices if d.strip() and 'device' in d]
            if connected_devices:
                print(f"✅ ADB连接正常，检测到 {len(connected_devices)} 个设备")
                for device in connected_devices:
                    print(f"  设备: {device}")
            else:
                print("❌ ADB连接正常，但未检测到设备")
                return
        else:
            print(f"❌ ADB连接失败: {result.stderr}")
            return
    except Exception as e:
        print(f"❌ ADB连接错误: {e}")
        return
    
    print()
    
    # 检查远程文件是否存在
    output_file = front_camera["output_file"]
    remote_path = f"/data/camera/{output_file}"
    
    print(f"检查远程文件是否存在: {remote_path}")
    try:
        result = subprocess.run(['adb', 'shell', f'ls -la {remote_path}'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ 文件存在:")
            print(result.stdout)
        else:
            print(f"❌ 文件不存在: {result.stderr}")
            
            # 检查目录是否存在
            print("\n检查目录是否存在: /data/camera/")
            result = subprocess.run(['adb', 'shell', 'ls -la /data/camera/'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✅ 目录存在:")
                print(result.stdout)
            else:
                print(f"❌ 目录不存在: {result.stderr}")
    except Exception as e:
        print(f"❌ 检查文件失败: {e}")
    
    print()
    
    # 尝试拉取文件
    print(f"尝试拉取文件: {remote_path}")
    try:
        result = subprocess.run(['adb', 'pull', remote_path, '.'], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            print(f"✅ 拉取成功")
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f"  本地文件大小: {file_size} 字节")
            else:
                print("❌ 本地文件不存在")
        else:
            print(f"❌ 拉取失败: {result.stderr}")
    except Exception as e:
        print(f"❌ 拉取文件失败: {e}")
    
    print()
    
    # 检查其他可能的路径
    print("检查其他可能的路径...")
    possible_paths = [
        f"/data/{output_file}",
        f"/sdcard/{output_file}",
        f"/storage/emulated/0/{output_file}"
    ]
    
    for path in possible_paths:
        try:
            result = subprocess.run(['adb', 'shell', f'ls -la {path}'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ 找到文件: {path}")
                print(result.stdout)
            else:
                print(f"❌ 文件不存在: {path}")
        except Exception as e:
            print(f"❌ 检查失败: {path} - {e}")

if __name__ == "__main__":
    test_camera_paths() 