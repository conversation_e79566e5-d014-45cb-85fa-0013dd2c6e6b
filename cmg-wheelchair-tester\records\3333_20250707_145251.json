{"sn": "3333", "timestamp": "2025-07-07T14:52:51.089129", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-07T14:51:32.378545"}, "usb_test": {"test_name": "USB关键器件检测", "status": "失败", "message": "未知的测试类型", "timestamp": "2025-07-07T14:51:38.287950"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-07T14:51:38.799790"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-07T14:51:42.788605"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-07T14:51:53.357166"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成", "timestamp": "2025-07-07T14:52:08.171994"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-07T14:52:08.612571"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-07T14:52:11.763383"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-07T14:52:14.060390"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-07T14:52:14.603116"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-07T14:52:18.753804"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-07T14:52:29.681905"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-07T14:52:30.035467"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "无设备数据", "timestamp": "2025-07-07T14:52:44.321788"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "WiFi连接正常", "timestamp": "2025-07-07T14:52:44.883138"}}}