{"sn": "0000", "timestamp": "2025-07-10T11:59:07.085290", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-10T11:57:46.126408"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-10T11:57:46.515258"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-10T11:57:47.247070"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-10T11:57:50.994510"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-10T11:58:01.442762"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-10T11:58:16.178475"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-10T11:58:26.008275"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-10T11:58:31.936654"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-10T11:58:34.331969"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-10T11:58:34.755458"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-10T11:58:38.093969"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-10T11:58:41.171606"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-10T11:58:46.450483"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "无设备数据", "timestamp": "2025-07-10T11:59:00.740299"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "WiFi连接正常", "timestamp": "2025-07-10T11:59:01.285423"}}}