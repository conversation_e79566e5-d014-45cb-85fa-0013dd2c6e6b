#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修改后的按键测试功能
"""

import subprocess
import threading
import time
import queue
import tkinter as tk
from tkinter import ttk

def simulate_key_test():
    """模拟按键测试流程"""
    print("=== 模拟按键测试流程 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("按键测试模拟")
    root.geometry("600x500")
    
    # 主框架
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(
        main_frame,
        text="按键测试模拟",
        font=("Arial", 16, "bold")
    )
    title_label.pack(pady=(0, 20))
    
    # 状态显示
    status_frame = ttk.LabelFrame(main_frame, text="测试状态", padding="10")
    status_frame.pack(fill=tk.X, pady=(0, 15))
    
    status_var = tk.StringVar(value="正在初始化...")
    status_label = ttk.Label(
        status_frame,
        textvariable=status_var,
        font=("Arial", 12)
    )
    status_label.pack(fill=tk.X, padx=5, pady=5)
    
    # 按键状态显示
    keys_frame = ttk.LabelFrame(main_frame, text="按键状态", padding="10")
    keys_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))
    
    # 创建按键状态显示
    expected_keys = [656, 657, 658, 659, 660, 661, 662, 663]
    key_vars = {}
    key_labels = {}
    
    # 创建网格布局显示按键状态
    for i, key_code in enumerate(expected_keys):
        row = i // 4
        col = i % 4
        
        key_frame = ttk.Frame(keys_frame)
        key_frame.grid(row=row, column=col, padx=10, pady=5, sticky="ew")
        
        key_vars[key_code] = tk.StringVar(value="未测试")
        key_label = ttk.Label(
            key_frame,
            text=f"按键{key_code}:",
            font=("Arial", 10)
        )
        key_label.pack(side=tk.LEFT)
        
        status_label = ttk.Label(
            key_frame,
            textvariable=key_vars[key_code],
            font=("Arial", 10, "bold"),
            foreground="gray"
        )
        status_label.pack(side=tk.LEFT, padx=(5, 0))
        key_labels[key_code] = status_label
    
    # 配置网格权重
    for i in range(4):
        keys_frame.columnconfigure(i, weight=1)
    
    # 日志显示
    log_frame = ttk.LabelFrame(main_frame, text="测试日志", padding="10")
    log_frame.pack(fill=tk.X, pady=(0, 15))
    
    log_text = tk.Text(log_frame, height=8, wrap=tk.WORD)
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_text.insert(tk.END, f"[{timestamp}] {message}\n")
        log_text.see(tk.END)
        root.update()
    
    # 开始测试按钮
    def start_test():
        """开始按键测试"""
        log_message("开始按键测试...")
        status_var.set("正在启动按键监听...")
        root.update()
        
        try:
            # 启动evtest进程
            command = "evtest /dev/input/event5"
            log_message(f"执行命令: adb shell {command}")
            
            process = subprocess.Popen(
                ['adb', 'shell', command],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待初始化
            time.sleep(2)
            status_var.set("gpio-keys设备已就绪，请开始按键测试...")
            log_message("gpio-keys设备监听已启动，开始检测按键事件...")
            
            # 监听按键事件
            detected_keys = set()
            key_press_states = {}
            start_time = time.time()
            timeout = 30  # 30秒超时
            
            # 创建输出队列
            output_queue = queue.Queue()
            
            def read_output():
                """读取进程输出"""
                try:
                    while True:
                        line = process.stdout.readline()
                        if not line:
                            break
                        output_queue.put(line.strip())
                except Exception as e:
                    output_queue.put(f"ERROR: {str(e)}")
            
            # 启动输出读取线程
            output_thread = threading.Thread(target=read_output, daemon=True)
            output_thread.start()
            
            while len(detected_keys) < 8 and (time.time() - start_time) < timeout:
                if process.poll() is not None:
                    log_message("evtest进程已结束")
                    break
                
                try:
                    # 从队列中获取输出行
                    try:
                        line = output_queue.get_nowait()
                    except queue.Empty:
                        time.sleep(0.1)
                        root.update()
                        continue
                    
                    if not line or line.startswith("ERROR:"):
                        if line.startswith("ERROR:"):
                            log_message(f"读取输出时出错: {line[6:]}")
                        continue
                    
                    # 记录所有输出用于调试
                    if "Event:" in line:
                        log_message(f"事件: {line}")
                    
                    # 解析按键事件
                    if "EV_KEY" in line and "KEY_" in line:
                        try:
                            key_code = None
                            value = None
                            
                            parts = line.split(", ")
                            for part in parts:
                                part = part.strip()
                                if part.startswith("code "):
                                    code_str = part.split("code ")[1].split(" ")[0]
                                    key_code = int(code_str)
                                elif part.startswith("value "):
                                    value = int(part.split("value ")[1])
                            
                            # 检查是否是我们关心的按键
                            if key_code in expected_keys and value is not None:
                                if value == 1:
                                    # 按键按下
                                    key_press_states[key_code] = True
                                    key_vars[key_code].set("按下")
                                    key_labels[key_code].configure(foreground="orange")
                                    log_message(f"检测到按键{key_code}按下")
                                elif value == 0 and key_press_states.get(key_code, False):
                                    # 按键松开
                                    detected_keys.add(key_code)
                                    key_vars[key_code].set("完成")
                                    key_labels[key_code].configure(foreground="green")
                                    log_message(f"按键{key_code}测试完成 (按下->松开)")
                                    
                                    # 更新状态
                                    status_var.set(f"已完成 {len(detected_keys)}/8 个按键测试")
                                    root.update()
                            
                        except (ValueError, IndexError) as e:
                            log_message(f"解析按键事件失败: {str(e)}, 原始行: {line}")
                            continue
                    
                    root.update()
                    
                except Exception as e:
                    log_message(f"处理按键事件时出错: {str(e)}")
                    break
            
            # 停止进程
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                process.kill()
            
            # 显示结果
            if len(detected_keys) >= 8:
                log_message(f"按键测试完成 - 成功检测到{len(detected_keys)}个按键")
                log_message(f"检测到的按键: {sorted(detected_keys)}")
                status_var.set("测试完成 - 成功")
            else:
                missing_keys = set(expected_keys) - detected_keys
                log_message(f"按键测试失败 - 只检测到{len(detected_keys)}个按键")
                log_message(f"未检测到的按键: {sorted(missing_keys)}")
                status_var.set(f"测试失败 - 只检测到{len(detected_keys)}/8个按键")
            
        except Exception as e:
            log_message(f"按键测试出错: {str(e)}")
            status_var.set("测试出错")
    
    # 开始测试按钮
    start_button = ttk.Button(
        main_frame,
        text="开始按键测试",
        command=lambda: threading.Thread(target=start_test, daemon=True).start()
    )
    start_button.pack(pady=10)
    
    log_message("按键测试模拟器已就绪")
    log_message("点击'开始按键测试'按钮开始测试")
    
    root.mainloop()

if __name__ == "__main__":
    print("按键测试功能验证")
    print("=" * 50)
    
    # 检查ADB连接
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=5)
        if result.returncode != 0:
            print("ADB连接失败，无法进行测试")
            exit(1)
        
        devices = result.stdout.strip().split('\n')[1:]
        if not any('device' in line for line in devices):
            print("没有连接的设备，无法进行测试")
            exit(1)
        
        print("ADB连接正常，设备已连接")
        
    except Exception as e:
        print(f"检查ADB连接失败: {e}")
        exit(1)
    
    # 启动模拟测试
    simulate_key_test()
