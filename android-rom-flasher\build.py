import PyInstaller.__main__
import os
import shutil

def build():
    # 清理之前的构建文件
    if os.path.exists("build"):
        shutil.rmtree("build")
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    
    # 配置PyInstaller
    PyInstaller.__main__.run([
        'main.py',
        '--name=OrionStar_Rom_Flasher',
        '--onefile',
        '--windowed',
        '--add-data=roms;roms',
    ])
    
    # 创建release目录
    if not os.path.exists("release"):
        os.makedirs("release")
    
    # 复制可执行文件到release目录
    shutil.copy2("dist/OrionStar_Rom_Flasher.exe", "release/")
    
    # 创建README文件
    with open("release/README.txt", "w", encoding="utf-8") as f:
        f.write("""OrionStar ROM刷机工具使用说明

1. 运行程序
   - 双击运行 OrionStar_Rom_Flasher.exe

2. 放置ROM文件
   - 在程序同目录下创建roms文件夹
   - 在roms文件夹中创建以下子文件夹：
     * factory - 存放工厂版本ROM
     * domestic - 存放国内出货版本ROM
     * oversea - 存放海外出货版本ROM
   - 将对应的ROM文件放入相应文件夹

3. 使用说明
   - 连接设备后，程序会自动检测设备信息
   - 选择要刷入的ROM版本
   - 点击"开始推送"按钮开始刷机
   - 刷机过程中请勿断开设备连接
   - 刷机完成后会显示成功提示

4. 注意事项
   - 请确保设备已进入fastboot模式
   - 国内机器不能刷入海外版本ROM
   - 刷机前请备份重要数据
   - 刷机过程中请勿关闭程序或断开设备连接
""")

if __name__ == "__main__":
    build() 