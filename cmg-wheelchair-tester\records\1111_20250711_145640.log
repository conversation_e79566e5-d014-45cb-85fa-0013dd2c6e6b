[2025-07-11 14:55:18] 开始测试 - SN: 1111
[2025-07-11 14:55:19] 
开始执行: 设备连接状态检测
[2025-07-11 14:55:19] 设备连接测试通过，检测到 1 个设备
[2025-07-11 14:55:19] 设备: ?	device
[2025-07-11 14:55:19] 
开始执行: USB关键器件检测
[2025-07-11 14:55:19] 执行USB设备检测...
[2025-07-11 14:55:19] 执行命令: adb shell lsusb
[2025-07-11 14:55:19] 设备返回数据:
[2025-07-11 14:55:19] Bus 005 Device 001: ID 1d6b:0001
Bus 003 Device 001: ID 1d6b:0002
Bus 001 Device 001: ID 1d6b:0002
Bus 006 Device 001: ID 1d6b:0001
Bus 001 Device 002: ID 0c45:1915
Bus 004 Device 001: ID 1d6b:0002
Bus 004 Device 002: ID 1a86:55ec
Bus 002 Device 001: ID 1d6b:0003
Bus 003 Device 003: ID 2c7c:0901

[2025-07-11 14:55:19] 解析到设备: Bus 005 Device 001 ID 1d6b:0001
[2025-07-11 14:55:19] 解析到设备: Bus 003 Device 001 ID 1d6b:0002
[2025-07-11 14:55:19] 解析到设备: Bus 001 Device 001 ID 1d6b:0002
[2025-07-11 14:55:19] 解析到设备: Bus 006 Device 001 ID 1d6b:0001
[2025-07-11 14:55:19] 解析到设备: Bus 001 Device 002 ID 0c45:1915
[2025-07-11 14:55:19] 解析到设备: Bus 004 Device 001 ID 1d6b:0002
[2025-07-11 14:55:19] 解析到设备: Bus 004 Device 002 ID 1a86:55ec
[2025-07-11 14:55:19] 解析到设备: Bus 002 Device 001 ID 1d6b:0003
[2025-07-11 14:55:19] 解析到设备: Bus 003 Device 003 ID 2c7c:0901
[2025-07-11 14:55:19] 总共解析到 9 个设备
[2025-07-11 14:55:19] ✅ 所有预期的设备ID都已找到
[2025-07-11 14:55:19] 检测到的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 14:55:19] 预期的设备: ['1d6b:0001', '1d6b:0002', '1d6b:0002', '1d6b:0001', '0c45:1915', '1d6b:0002', '1a86:55ec', '1d6b:0003', '2c7c:0901']
[2025-07-11 14:55:19] ✅ USB设备检测通过
[2025-07-11 14:55:20] 
开始执行: CAN0测试
[2025-07-11 14:55:20] 执行CAN0测试流程...
[2025-07-11 14:55:20] 执行命令: adb shell ip link set can0 down
[2025-07-11 14:55:20] CAN0已关闭
[2025-07-11 14:55:20] 执行命令: adb shell ip link set can0 up type can bitrate 500000 loopback on
[2025-07-11 14:55:20] CAN0已启动
[2025-07-11 14:55:20] CAN监听线程已启动...
[2025-07-11 14:55:21] 执行命令: adb shell cansend can0 1234ABCD#aa.00.cc.aa.55.66.aa.ff
[2025-07-11 14:55:21] CAN测试数据已发送，等待监听返回...
[2025-07-11 14:55:21] CAN监听输出: can0  1234ABCD   [8]  AA 00 CC AA 55 66 AA FF
[2025-07-11 14:55:23] ✅ CAN测试成功，接收到数据: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF
[2025-07-11 14:55:23] 
开始执行: GPS测试
[2025-07-11 14:55:23] 执行GPS测试...
[2025-07-11 14:55:23] 执行命令 1: adb shell cat /dev/ttyUSB4 |grep GPGSV
[2025-07-11 14:55:33] GPS测试出错: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds
[2025-07-11 14:55:34] 
开始执行: 4G模组测试
[2025-07-11 14:55:34] 执行4G模组测试...（自动化监听+指令发送）
[2025-07-11 14:55:34] 监听线程已启动，等待串口数据...
[2025-07-11 14:55:36] 执行命令: adb shell "echo -e 'AT+CCID\r' > /dev/ttyUSB0"
[2025-07-11 14:55:36] AT+CCID指令已发送，等待串口返回...
[2025-07-11 14:55:36] 串口输出: AT+CCID
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: +CME ERROR: 13
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: AT+C
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: +CME ERROR: 58
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: AT+C
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: +CME ERROR: 58
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: AT+C
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:36] 串口输出: +CME ERROR: 58
[2025-07-11 14:55:36] 串口输出: 
[2025-07-11 14:55:48] ❌ 未检测到CCID，请检查4G模组或手动测试
[2025-07-11 14:55:49] 
开始执行: 按键测试
[2025-07-11 14:55:49] 开始按键测试...
[2025-07-11 14:55:49] 执行命令: adb shell evtest /dev/input/event5
[2025-07-11 14:55:51] 原始事件: Event: time 1751299767.360700, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:55:51] 解析结果: key_code=662, value=1
[2025-07-11 14:55:51] ✓ 检测到档位减(662)按下
[2025-07-11 14:55:51] 原始事件: Event: time 1751299767.534017, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:55:51] 解析结果: key_code=662, value=0
[2025-07-11 14:55:51] ✓ 档位减(662)测试通过
[2025-07-11 14:55:51] 原始事件: Event: time 1751299768.384021, type 1 (EV_KEY), code 662 (?), value 1
[2025-07-11 14:55:51] 解析结果: key_code=662, value=1
[2025-07-11 14:55:51] ✓ 检测到档位减(662)按下
[2025-07-11 14:55:52] 原始事件: Event: time 1751299768.580771, type 1 (EV_KEY), code 662 (?), value 0
[2025-07-11 14:55:52] 解析结果: key_code=662, value=0
[2025-07-11 14:55:52] ✓ 档位减(662)测试通过
[2025-07-11 14:55:52] 原始事件: Event: time 1751299769.007373, type 1 (EV_KEY), code 661 (?), value 1
[2025-07-11 14:55:52] 解析结果: key_code=661, value=1
[2025-07-11 14:55:52] ✓ 检测到静音键(661)按下
[2025-07-11 14:55:52] 原始事件: Event: time 1751299769.230660, type 1 (EV_KEY), code 661 (?), value 0
[2025-07-11 14:55:52] 解析结果: key_code=661, value=0
[2025-07-11 14:55:52] ✓ 静音键(661)测试通过
[2025-07-11 14:55:52] 原始事件: Event: time 1751299769.580786, type 1 (EV_KEY), code 659 (?), value 1
[2025-07-11 14:55:52] 解析结果: key_code=659, value=1
[2025-07-11 14:55:53] ✓ 检测到档位加(659)按下
[2025-07-11 14:55:53] 原始事件: Event: time 1751299769.757378, type 1 (EV_KEY), code 659 (?), value 0
[2025-07-11 14:55:53] 解析结果: key_code=659, value=0
[2025-07-11 14:55:53] ✓ 档位加(659)测试通过
[2025-07-11 14:55:53] 原始事件: Event: time 1751299770.030671, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:55:53] 解析结果: key_code=660, value=1
[2025-07-11 14:55:53] ✓ 检测到智驾键(660)按下
[2025-07-11 14:55:53] 原始事件: Event: time 1751299770.257382, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:55:53] 解析结果: key_code=660, value=0
[2025-07-11 14:55:53] ✓ 智驾键(660)测试通过
[2025-07-11 14:55:54] 原始事件: Event: time 1751299770.630667, type 1 (EV_KEY), code 663 (?), value 1
[2025-07-11 14:55:54] 解析结果: key_code=663, value=1
[2025-07-11 14:55:54] ✓ 检测到语音键(663)按下
[2025-07-11 14:55:54] 原始事件: Event: time 1751299770.807362, type 1 (EV_KEY), code 663 (?), value 0
[2025-07-11 14:55:54] 解析结果: key_code=663, value=0
[2025-07-11 14:55:54] ✓ 语音键(663)测试通过
[2025-07-11 14:55:55] 原始事件: Event: time 1751299771.730748, type 1 (EV_KEY), code 657 (?), value 1
[2025-07-11 14:55:55] 解析结果: key_code=657, value=1
[2025-07-11 14:55:55] ✓ 检测到SOS键(657)按下
[2025-07-11 14:55:55] 原始事件: Event: time 1751299771.907332, type 1 (EV_KEY), code 657 (?), value 0
[2025-07-11 14:55:55] 解析结果: key_code=657, value=0
[2025-07-11 14:55:55] ✓ SOS键(657)测试通过
[2025-07-11 14:55:56] 原始事件: Event: time 1751299772.760676, type 1 (EV_KEY), code 656 (?), value 1
[2025-07-11 14:55:56] 解析结果: key_code=656, value=1
[2025-07-11 14:55:56] ✓ 检测到锁定键(656)按下
[2025-07-11 14:55:56] 原始事件: Event: time 1751299772.981795, type 1 (EV_KEY), code 656 (?), value 0
[2025-07-11 14:55:56] 解析结果: key_code=656, value=0
[2025-07-11 14:55:56] ✓ 锁定键(656)测试通过
[2025-07-11 14:55:57] 原始事件: Event: time 1751299773.507360, type 1 (EV_KEY), code 660 (?), value 1
[2025-07-11 14:55:57] 解析结果: key_code=660, value=1
[2025-07-11 14:55:57] ✓ 检测到智驾键(660)按下
[2025-07-11 14:55:57] 原始事件: Event: time 1751299773.730674, type 1 (EV_KEY), code 660 (?), value 0
[2025-07-11 14:55:57] 解析结果: key_code=660, value=0
[2025-07-11 14:55:57] ✓ 智驾键(660)测试通过
[2025-07-11 14:55:58] 原始事件: Event: time 1751299775.130649, type 1 (EV_KEY), code 658 (?), value 1
[2025-07-11 14:55:58] 解析结果: key_code=658, value=1
[2025-07-11 14:55:58] ✓ 检测到喇叭键(658)按下
[2025-07-11 14:55:58] 原始事件: Event: time 1751299775.357501, type 1 (EV_KEY), code 658 (?), value 0
[2025-07-11 14:55:58] 解析结果: key_code=658, value=0
[2025-07-11 14:55:58] ✓ 喇叭键(658)测试通过
[2025-07-11 14:55:59] 按键测试完成 - 检测到8个按键
[2025-07-11 14:56:01] 
开始执行: 按键灯测试
[2025-07-11 14:56:01] 开始LED背光灯测试...
[2025-07-11 14:56:01] 执行命令: adb shell echo 255 > /sys/class/leds/lock_led/brightness
[2025-07-11 14:56:01] LED控制命令执行成功
[2025-07-11 14:56:03] 执行命令: adb shell echo 0 > /sys/class/leds/lock_led/brightness
[2025-07-11 14:56:03] LED灯已关闭
[2025-07-11 14:56:03] LED测试通过 - 背光灯正常点亮
[2025-07-11 14:56:03] 
开始执行: 手电筒测试
[2025-07-11 14:56:03] 开始手电筒LED测试...
[2025-07-11 14:56:03] 执行命令: adb shell echo 255 > /sys/class/leds/torch/brightness
[2025-07-11 14:56:03] 手电筒控制命令执行成功
[2025-07-11 14:56:06] 执行命令: adb shell echo 0 > /sys/class/leds/torch/brightness
[2025-07-11 14:56:06] 手电筒已关闭
[2025-07-11 14:56:06] 手电筒测试通过 - 手电筒正常点亮
[2025-07-11 14:56:06] 
开始执行: 摇杆使能测试
[2025-07-11 14:56:06] 执行摇杆测试...
[2025-07-11 14:56:06] 执行命令: adb shell cat /sys/class/leds/joystick/brightness
[2025-07-11 14:56:06] 命令执行成功
[2025-07-11 14:56:06] 返回数据: 255
[2025-07-11 14:56:06] 摇杆测试通过，值: 255
[2025-07-11 14:56:07] 
开始执行: 前摄像头测试
[2025-07-11 14:56:07] 开始执行前摄像头测试...
[2025-07-11 14:56:07] 执行命令 1: adb shell mkdir -p /data/camera/
[2025-07-11 14:56:07] 命令执行成功，返回: 无输出
[2025-07-11 14:56:07] 执行命令 2: adb shell gst-launch-1.0 -e v4l2src device=/dev/video0 num-buffers=1 ! video/x-raw,format=NV12,width=3840,height=2160 ! mppjpegenc ! filesink location=/data/camera/cam0_3840x2160.jpg
[2025-07-11 14:56:08] 命令执行成功，返回: Setting pipeline to PAUSED ...
Using mplane plugin for capture 
Pipeline is live and does not need PREROLL ...
Pipeline is PREROLLED ...
Setting pipeline to PLAYING ...
New clock: GstSystemClock
Redistribute latency...
Got EOS from element "pipeline0".
EOS received - stopping pipeline...
Execution ended after 0:00:00.322965324
Setting pipeline to NULL ...
Freeing pipeline ...
[2025-07-11 14:56:08] 执行拉取命令: adb pull /data/camera/cam0_3840x2160.jpg .
[2025-07-11 14:56:08] 拉取命令执行成功
[2025-07-11 14:56:08] 返回数据: [ 34%] /data/camera/cam0_3840x2160.jpg
[ 69%] /data/camera/cam0_3840x2160.jpg
[100%] /data/camera/cam0_3840x2160.jpg
/data/camera/cam0_3840x2160.jpg: 1 file pulled. 17.5 MB/s (189186 bytes in 0.010s)
[2025-07-11 14:56:08] 图片已保存: cam0_3840x2160.jpg
[2025-07-11 14:56:10] 用户确认结果: 通过
[2025-07-11 14:56:10] 
开始执行: 光感测试
[2025-07-11 14:56:10] 执行光感测试...
[2025-07-11 14:56:10] 执行命令: adb shell evtest /dev/input/event1
[2025-07-11 14:56:14] 光感测试完成 - 检测到数值变化
[2025-07-11 14:56:14] 数值从 0 变化到 1
[2025-07-11 14:56:14] 
开始执行: 回充摄像头测试
[2025-07-11 14:56:14] 开始执行回充摄像头测试...
[2025-07-11 14:56:14] 执行命令 1: adb shell v4l2-ctl --device=/dev/video20 --set-fmt-video=width=640,height=480,pixelformat=MJPG
[2025-07-11 14:56:15] 命令执行成功，返回: 无输出
[2025-07-11 14:56:15] 执行命令 2: adb shell v4l2-ctl --device=/dev/video20 --stream-mmap=3 --stream-to=/data/output.jpg --stream-count=1
[2025-07-11 14:56:15] 命令执行成功，返回: 无输出
[2025-07-11 14:56:15] 执行拉取命令: adb pull /data/output.jpg .
[2025-07-11 14:56:16] 拉取命令执行成功
[2025-07-11 14:56:16] 返回数据: [100%] /data/output.jpg
/data/output.jpg: 1 file pulled. 7.5 MB/s (18616 bytes in 0.002s)
[2025-07-11 14:56:16] 图片已保存: output.jpg
[2025-07-11 14:56:18] 用户确认结果: 通过
[2025-07-11 14:56:18] 
开始执行: 喇叭测试
[2025-07-11 14:56:18] 执行喇叭测试...
[2025-07-11 14:56:18] 执行命令: adb shell tinyplay /usr/data/test.wav
[2025-07-11 14:56:32] 命令执行成功
[2025-07-11 14:56:32] 返回数据: playing '/usr/data/test.wav': 2 ch, 48000 hz, 16 bit
[2025-07-11 14:56:32] 音频播放完成
[2025-07-11 14:56:32] 
开始执行: 蓝牙测试
[2025-07-11 14:56:32] 执行蓝牙测试...
[2025-07-11 14:56:32] 启动蓝牙服务...
[2025-07-11 14:56:32] 启动蓝牙服务失败: /bin/bash: line 1: systemctl: command not found

[2025-07-11 14:56:32] 开启蓝牙...
[2025-07-11 14:56:33] 执行命令: adb shell bluetoothctl devices
[2025-07-11 14:56:33] 命令执行成功
[2025-07-11 14:56:33] 无输出数据
[2025-07-11 14:56:33] 
开始执行: WiFi测试
[2025-07-11 14:56:33] 执行WiFi测试...
[2025-07-11 14:56:33] 执行命令 1: adb shell killall wpa_supplicant 2>/dev/null
[2025-07-11 14:56:33] 命令执行成功，无输出
[2025-07-11 14:56:33] 执行命令 2: adb shell rm -f /var/run/wpa_supplicant/wlan0
[2025-07-11 14:56:33] 命令执行成功，无输出
[2025-07-11 14:56:33] 执行命令 3: adb shell ip link set wlan0 down
[2025-07-11 14:56:33] 命令执行成功，无输出
[2025-07-11 14:56:33] 执行命令 4: adb shell wpa_supplicant -B -Dnl80211 -i wlan0 -c /etc/wpa_supplicant.conf && wpa_cli -i wlan0 add_network && wpa_cli -i wlan0 set_network 0 ssid '"Orion_SZ_5G"' && wpa_cli -i wlan0 set_network 0 psk '"Orion@2025"' && wpa_cli -i wlan0 enable_network 0 && udhcpc -i wlan0 && iw wlan0 link && ip addr show wlan0
[2025-07-11 14:56:37] 命令执行成功，返回数据:
[2025-07-11 14:56:38]   Successfully initialized wpa_supplicant
[2025-07-11 14:56:38]   nl80211: kernel reports: Registration to specific type not supported
[2025-07-11 14:56:38]   1
[2025-07-11 14:56:38]   OK
[2025-07-11 14:56:38]   OK
[2025-07-11 14:56:38]   OK
[2025-07-11 14:56:38]   deleting routers
[2025-07-11 14:56:38]   adding dns ************
[2025-07-11 14:56:38]   adding dns ***********
[2025-07-11 14:56:38]   Connected to 6c:c4:9f:2a:5a:b0 (on wlan0)
[2025-07-11 14:56:38]   SSID: Orion_SZ_5G
[2025-07-11 14:56:38]   freq: 5300
[2025-07-11 14:56:38]   RX: 3553 bytes (14 packets)
[2025-07-11 14:56:38]   TX: 2761 bytes (23 packets)
[2025-07-11 14:56:38]   signal: -46 dBm
[2025-07-11 14:56:38]   rx bitrate: 270.8 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 1 HE-DCM 0
[2025-07-11 14:56:38]   tx bitrate: 286.7 MBit/s 40MHz HE-MCS 11 HE-NSS 1 HE-GI 0 HE-DCM 0
[2025-07-11 14:56:38]   4: wlan0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc mq state UP group default qlen 1000
[2025-07-11 14:56:38]   link/ether 24:21:5e:c0:3d:52 brd ff:ff:ff:ff:ff:ff
[2025-07-11 14:56:38]   inet 192.168.20.176/24 brd 192.168.20.255 scope global noprefixroute wlan0
[2025-07-11 14:56:38]   valid_lft forever preferred_lft forever
[2025-07-11 14:56:38]   inet6 fe80::464f:efff:8111:4e96/64 scope link
[2025-07-11 14:56:38]   valid_lft forever preferred_lft forever
[2025-07-11 14:56:38] 执行命令 5: adb shell ping -c 3 www.baidu.com
[2025-07-11 14:56:40] 命令执行成功，返回数据:
[2025-07-11 14:56:40]   PING www.a.shifen.com (157.148.69.186) 56(84) bytes of data.
[2025-07-11 14:56:40]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=1 ttl=54 time=10.8 ms
[2025-07-11 14:56:40]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=2 ttl=54 time=11.3 ms
[2025-07-11 14:56:40]   64 bytes from 157.148.69.186 (157.148.69.186): icmp_seq=3 ttl=54 time=10.6 ms
[2025-07-11 14:56:40]   --- www.a.shifen.com ping statistics ---
[2025-07-11 14:56:40]   3 packets transmitted, 3 received, 0% packet loss, time 2003ms
[2025-07-11 14:56:40]   rtt min/avg/max/mdev = 10.640/10.934/11.314/0.281 ms
[2025-07-11 14:56:40] ✅ ping测试成功，网络连通性正常
[2025-07-11 14:56:40] WiFi连接成功
[2025-07-11 14:56:40] 
测试完成 - 通过率: 12/15
[2025-07-11 14:56:40] ❌ 存在测试失败项！
[2025-07-11 14:56:40] 测试记录已保存: records/1111_20250711_145640.json

