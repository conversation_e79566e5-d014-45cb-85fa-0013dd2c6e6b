{"sn": "0000", "timestamp": "2025-07-05T22:36:09.439430", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "失败", "message": "未连接设备", "timestamp": "2025-07-05T22:36:03.651206"}, "usb_test": {"test_name": "USB关键器件检测", "status": "失败", "message": "未知的测试类型", "timestamp": "2025-07-05T22:36:03.852682"}, "can_test": {"test_name": "CAN0测试", "status": "失败", "message": "CAN0 down失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T22:36:04.006928"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "无GPS信号", "timestamp": "2025-07-05T22:36:04.178279"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "AT指令发送失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T22:36:04.400955"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成", "timestamp": "2025-07-05T22:36:06.688227"}, "led_test": {"test_name": "按键灯测试", "status": "失败", "message": "LED控制失败", "timestamp": "2025-07-05T22:36:06.861536"}, "torch_test": {"test_name": "手电筒测试", "status": "失败", "message": "手电筒控制失败", "timestamp": "2025-07-05T22:36:07.054658"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T22:36:07.258614"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-05T22:36:07.505221"}, "light_sensor_test": {"test_name": "光感测试", "status": "失败", "message": "光感异常", "timestamp": "2025-07-05T22:36:07.897824"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-05T22:36:08.209355"}, "speaker_test": {"test_name": "喇叭测试", "status": "失败", "message": "播放失败", "timestamp": "2025-07-05T22:36:08.573288"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T22:36:08.792729"}, "wifi_test": {"test_name": "WiFi测试", "status": "失败", "message": "WiFi连接失败", "timestamp": "2025-07-05T22:36:09.109926"}}}