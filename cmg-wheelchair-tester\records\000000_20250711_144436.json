{"sn": "000000", "timestamp": "2025-07-11T14:44:36.547984", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-11T14:42:01.954588"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-11T14:42:02.184742"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-11T14:42:02.415933"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-11T14:42:06.002521"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-11T14:42:16.594592"}, "key_test": {"test_name": "按键测试", "status": "失败", "message": "按键测试失败，只检测到5/8个按键", "timestamp": "2025-07-11T14:42:31.462429"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-11T14:43:36.377254"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-11T14:43:57.352481"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-11T14:43:59.020339"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-11T14:43:59.348108"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-11T14:44:03.259348"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-11T14:44:11.304869"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-11T14:44:14.661572"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "无设备数据", "timestamp": "2025-07-11T14:44:29.238744"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "WiFi连接正常", "timestamp": "2025-07-11T14:44:29.959402"}}}