#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试手动确认功能
"""

import tkinter as tk
from tkinter import messagebox

def test_manual_confirmation():
    """测试手动确认对话框"""
    root = tk.Tk()
    root.withdraw()  # 隐藏主窗口
    
    # 模拟测试项目
    test_projects = [
        {
            "id": "led_test",
            "name": "按键灯测试",
            "manual_check": True
        },
        {
            "id": "front_camera_test", 
            "name": "前摄像头测试",
            "output_file": "cam0_3840x2160.jpg",
            "manual_check": True
        },
        {
            "id": "back_camera_test",
            "name": "回充摄像头测试", 
            "output_file": "output.jpg",
            "manual_check": True
        },
        {
            "id": "speaker_test",
            "name": "喇叭测试",
            "manual_check": True
        },
        {
            "id": "key_test",
            "name": "按键测试",
            "manual_check": True
        },
        {
            "id": "light_sensor_test",
            "name": "光感测试",
            "manual_check": True
        }
    ]
    
    def ask_manual_confirmation(test_project):
        """询问手动确认结果"""
        test_name = test_project["name"]
        
        # 根据测试类型显示不同的确认消息
        if test_project["id"] == "led_test":
            message = f"按键灯测试完成\n\n请观察LED灯是否正常点亮和熄灭\n\n测试结果是否正常？"
        elif test_project["id"] == "front_camera_test":
            message = f"前摄像头测试完成\n\n图片已保存为: {test_project.get('output_file', '')}\n\n请检查图片质量是否正常？"
        elif test_project["id"] == "back_camera_test":
            message = f"回充摄像头测试完成\n\n图片已保存为: {test_project.get('output_file', '')}\n\n请检查图片质量是否正常？"
        elif test_project["id"] == "speaker_test":
            message = f"喇叭测试完成\n\n音频播放已完成\n\n请确认声音质量是否正常？"
        elif test_project["id"] == "key_test":
            message = f"按键测试完成\n\n请确认所有按键是否正常工作？"
        elif test_project["id"] == "light_sensor_test":
            message = f"光感测试完成\n\n请确认光线传感器是否正常响应？"
        else:
            message = f"{test_name}测试完成\n\n请确认测试结果是否正常？"
        
        # 显示确认对话框
        response = messagebox.askyesno(
            f"手动确认 - {test_name}",
            message,
            icon='question'
        )
        
        if response:
            print(f"✅ {test_name} - 手动确认通过")
            return True
        else:
            print(f"❌ {test_name} - 手动确认失败")
            return False
    
    print("开始测试手动确认功能...")
    
    for project in test_projects:
        if project.get("manual_check", False):
            result = ask_manual_confirmation(project)
            print(f"测试项目: {project['name']}, 结果: {'PASS' if result else 'FAIL'}")
    
    print("手动确认功能测试完成")
    root.destroy()

if __name__ == "__main__":
    test_manual_confirmation() 