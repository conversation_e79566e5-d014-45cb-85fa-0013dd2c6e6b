{"sn": "1111111111111", "timestamp": "2025-07-16T21:10:41.506013", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-16T21:09:18.574837"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-16T21:09:19.198544"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-16T21:09:20.346857"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-16T21:09:24.163337"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "未检测到CCID", "timestamp": "2025-07-16T21:09:34.745036"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-16T21:09:49.564923"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-16T21:10:05.742003"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-16T21:10:10.930642"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-16T21:10:13.864542"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-16T21:10:14.478600"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-16T21:10:18.945464"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-16T21:10:22.472031"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-16T21:10:26.545940"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 24:21:5E:C0:30:F3", "timestamp": "2025-07-16T21:10:40.892287"}, "wifi_test": {"test_name": "WiFi测试", "status": "失败", "message": "网络连接失败", "timestamp": "2025-07-16T21:10:41.299488"}}}