{"sn": "11111", "timestamp": "2025-07-05T14:18:31.112383", "results": {"usb_test": {"test_name": "USB关键器件测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T14:18:13.134618"}, "can_test": {"test_name": "CAN0测试", "status": "失败", "message": "CAN0 down失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T14:18:14.220743"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "无GPS信号", "timestamp": "2025-07-05T14:18:15.395422"}, "4g_test": {"test_name": "4G模组测试", "status": "失败", "message": "AT指令发送失败: error: no devices/emulators found\n", "timestamp": "2025-07-05T14:18:18.530738"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成", "timestamp": "2025-07-05T14:18:19.581394"}, "led_test": {"test_name": "按键灯测试", "status": "失败", "message": "LED控制失败", "timestamp": "2025-07-05T14:18:20.663179"}, "torch_test": {"test_name": "手电筒测试", "status": "失败", "message": "手电筒控制失败", "timestamp": "2025-07-05T14:18:21.770413"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T14:18:22.866076"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-05T14:18:24.150456"}, "light_sensor_test": {"test_name": "光感测试", "status": "失败", "message": "光感异常", "timestamp": "2025-07-05T14:18:25.371581"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "失败", "message": "拍照失败", "timestamp": "2025-07-05T14:18:26.666608"}, "speaker_test": {"test_name": "喇叭测试", "status": "失败", "message": "播放失败", "timestamp": "2025-07-05T14:18:27.783221"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "失败", "message": "命令执行失败", "timestamp": "2025-07-05T14:18:28.966957"}, "wifi_test": {"test_name": "WiFi测试", "status": "失败", "message": "WiFi连接失败", "timestamp": "2025-07-05T14:18:30.079443"}}}