#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工序选择功能
"""

import tkinter as tk
from tkinter import ttk
import json
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_work_process_config():
    """测试工序配置"""
    print("=== 测试工序配置 ===")
    
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return False
    
    work_processes = config.get("work_processes", {})
    test_projects = config.get("test_projects", [])
    
    print(f"找到 {len(work_processes)} 个工序配置")
    print(f"找到 {len(test_projects)} 个测试项目")
    
    # 验证工序配置
    for process_name, process_config in work_processes.items():
        print(f"\n工序: {process_name}")
        print(f"  描述: {process_config['description']}")
        test_ids = process_config['test_ids']
        print(f"  包含测试项目: {len(test_ids)} 个")
        
        # 验证测试项目ID是否存在
        valid_test_ids = [project['id'] for project in test_projects]
        invalid_ids = []
        
        for test_id in test_ids:
            if test_id not in valid_test_ids:
                invalid_ids.append(test_id)
            else:
                # 找到对应的测试项目名称
                for project in test_projects:
                    if project['id'] == test_id:
                        print(f"    - {project['name']} ({test_id})")
                        break
        
        if invalid_ids:
            print(f"  ❌ 无效的测试项目ID: {invalid_ids}")
        else:
            print(f"  ✅ 所有测试项目ID有效")
    
    return True

def test_work_process_ui():
    """测试工序选择UI"""
    print("\n=== 测试工序选择UI ===")
    
    # 加载配置
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return
    
    work_processes = config.get("work_processes", {})
    test_projects = config.get("test_projects", [])
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("工序选择功能测试")
    root.geometry("800x600")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="工序选择功能测试", font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 工序选择区域
    process_frame = ttk.LabelFrame(main_frame, text="工序选择", padding="10")
    process_frame.pack(fill=tk.X, pady=(0, 10))
    process_frame.columnconfigure(1, weight=1)
    
    # 工序选择标签
    ttk.Label(process_frame, text="选择工序:").grid(row=0, column=0, padx=(0, 10), sticky=tk.W)
    
    # 工序选择下拉框
    selected_process = tk.StringVar(value="完整测试")
    process_combo = ttk.Combobox(
        process_frame, 
        textvariable=selected_process,
        values=list(work_processes.keys()),
        state="readonly",
        width=20
    )
    process_combo.grid(row=0, column=1, padx=(0, 10), sticky=tk.W)
    
    # 工序描述标签
    process_desc_var = tk.StringVar()
    desc_label = ttk.Label(process_frame, textvariable=process_desc_var, foreground="gray")
    desc_label.grid(row=0, column=2, sticky=tk.W)
    
    # 测试项目显示区域
    project_frame = ttk.LabelFrame(main_frame, text="测试项目", padding="10")
    project_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    # 创建测试项目列表
    project_tree = ttk.Treeview(
        project_frame,
        columns=("description", "type"),
        show="tree headings",
        height=15
    )
    
    project_tree.heading("#0", text="测试项目")
    project_tree.heading("description", text="描述")
    project_tree.heading("type", text="类型")
    project_tree.column("#0", width=200)
    project_tree.column("description", width=300)
    project_tree.column("type", width=150)
    project_tree.pack(fill=tk.BOTH, expand=True)
    
    # 统计信息显示
    stats_var = tk.StringVar()
    stats_label = ttk.Label(main_frame, textvariable=stats_var, font=("Arial", 12))
    stats_label.pack(pady=10)
    
    def update_process_description():
        """更新工序描述"""
        process_name = selected_process.get()
        if process_name in work_processes:
            desc = work_processes[process_name]["description"]
            process_desc_var.set(f"({desc})")
        else:
            process_desc_var.set("")
    
    def update_project_list():
        """更新测试项目列表"""
        # 清空现有项目
        for item in project_tree.get_children():
            project_tree.delete(item)
        
        process_name = selected_process.get()
        if process_name not in work_processes:
            return
        
        selected_test_ids = work_processes[process_name]["test_ids"]
        filtered_projects = []
        
        # 过滤测试项目
        for project in test_projects:
            if project["id"] in selected_test_ids:
                filtered_projects.append(project)
        
        # 添加到树中
        for project in filtered_projects:
            project_tree.insert("", "end", 
                              text=project["name"],
                              values=(project["description"], project["type"]))
        
        # 更新统计信息
        stats_var.set(f"当前工序包含 {len(filtered_projects)} 个测试项目")
    
    def on_process_changed(event=None):
        """工序选择改变事件"""
        update_process_description()
        update_project_list()
        print(f"切换工序: {selected_process.get()}")
    
    # 绑定事件
    process_combo.bind("<<ComboboxSelected>>", on_process_changed)
    
    # 初始化显示
    update_process_description()
    update_project_list()
    
    # 控制按钮
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=10)
    
    def show_process_details():
        """显示工序详情"""
        process_name = selected_process.get()
        if process_name not in work_processes:
            return
        
        details_window = tk.Toplevel(root)
        details_window.title(f"工序详情 - {process_name}")
        details_window.geometry("600x400")
        
        details_frame = ttk.Frame(details_window, padding="20")
        details_frame.pack(fill=tk.BOTH, expand=True)
        
        # 工序信息
        info_text = f"""工序名称: {process_name}
工序描述: {work_processes[process_name]['description']}
测试项目数量: {len(work_processes[process_name]['test_ids'])}

包含的测试项目:"""
        
        ttk.Label(details_frame, text=info_text, justify=tk.LEFT).pack(anchor=tk.W, pady=(0, 10))
        
        # 测试项目列表
        for test_id in work_processes[process_name]['test_ids']:
            for project in test_projects:
                if project['id'] == test_id:
                    project_info = f"• {project['name']} ({project['type']})"
                    ttk.Label(details_frame, text=project_info).pack(anchor=tk.W, padx=(20, 0))
                    break
    
    details_button = ttk.Button(
        button_frame,
        text="查看工序详情",
        command=show_process_details
    )
    details_button.pack(side=tk.LEFT, padx=(0, 10))
    
    # 说明文本
    info_frame = ttk.LabelFrame(main_frame, text="功能说明", padding="10")
    info_frame.pack(fill=tk.X, pady=10)
    
    info_text = """工序选择功能特点：
• 根据不同工序匹配对应的测试项目
• 支持基础连接测试、通信模块测试、人机交互测试、传感器测试等工序
• 可以选择完整测试运行所有项目
• 每个工序都有详细的描述和包含的测试项目列表
• 切换工序时会自动更新测试项目列表和统计信息"""
    
    info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
    info_label.pack(fill=tk.X)
    
    print("工序选择UI测试窗口已创建")
    root.mainloop()

if __name__ == "__main__":
    print("工序选择功能测试")
    print("=" * 50)
    
    # 测试工序配置
    if not test_work_process_config():
        print("❌ 工序配置测试失败")
        exit(1)
    
    print("\n✅ 工序配置测试成功")
    
    # 测试工序选择UI
    test_work_process_ui()
    
    print("\n测试完成")
