#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单项测试功能和数据保留功能
验证：
1. 测试项目状态在回到序列号输入时不清除
2. 测试项目状态在下一次开始测试后清除
3. 单项测试功能正常工作
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import threading
import time
import random

def load_config():
    """加载配置文件"""
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置失败: {e}")
        return {"work_processes": {}}

def test_single_test_and_data_preservation():
    """测试单项测试功能和数据保留"""
    print("=== 测试单项测试功能和数据保留 ===")
    
    config = load_config()
    work_processes = config.get("work_processes", {})
    test_projects = config.get("test_projects", [])
    
    if not work_processes:
        print("❌ 没有找到工序配置")
        return
    
    # 创建主窗口
    root = tk.Tk()
    root.title("单项测试功能和数据保留测试")
    root.geometry("1200x800")
    
    # 将主窗口移动到屏幕中心
    root.update_idletasks()
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    x = (screen_width - 1200) // 2
    y = (screen_height - 800) // 2
    root.geometry(f"1200x800+{x}+{y}")
    
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # 标题
    title_label = ttk.Label(main_frame, text="单项测试功能和数据保留测试", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 20))
    
    # 修改说明
    change_frame = ttk.LabelFrame(main_frame, text="功能修改说明", padding="15")
    change_frame.pack(fill=tk.X, pady=(0, 20))
    
    change_text = """🔧 数据保留逻辑修改:

修改前:
  • 测试完成回到序列号输入时清除测试项目状态
  • 无法查看上一次测试的具体项目结果

修改后:
  ✅ 测试完成回到序列号输入时不清除测试项目状态
  ✅ 测试项目状态在下一次开始测试后才清除
  ✅ 可以查看上一次测试的具体项目pass/fail状态
  ✅ 单项测试功能正常工作（双击或右键菜单）

🎯 使用场景:
  • 测试完成后可以查看每个测试项目的详细状态
  • 在输入下一个序列号前可以确认哪些项目通过/失败
  • 支持单独测试某个项目
  • 便于问题定位和结果分析"""
    
    ttk.Label(change_frame, text=change_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 10)).pack(fill=tk.X)
    
    # 创建左右分栏
    content_frame = ttk.Frame(main_frame)
    content_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
    
    # 左侧：测试项目列表
    left_frame = ttk.LabelFrame(content_frame, text="测试项目列表", padding="10")
    left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
    
    # 创建测试项目树
    columns = ("data", "result")
    test_tree = ttk.Treeview(left_frame, columns=columns, show="tree headings", height=15)
    
    # 设置列标题
    test_tree.heading("#0", text="测试项目", anchor=tk.W)
    test_tree.heading("data", text="测试数据", anchor=tk.W)
    test_tree.heading("result", text="测试结果", anchor=tk.CENTER)
    
    # 设置列宽
    test_tree.column("#0", width=200, minwidth=150)
    test_tree.column("data", width=200, minwidth=150)
    test_tree.column("result", width=100, minwidth=80)
    
    # 设置样式
    test_tree.tag_configure("pass_result", foreground="#28a745", font=("Microsoft YaHei UI", 10, "bold"))
    test_tree.tag_configure("fail_result", foreground="#dc3545", font=("Microsoft YaHei UI", 10, "bold"))
    test_tree.tag_configure("testing_result", foreground="#ffc107", font=("Microsoft YaHei UI", 10, "bold"))
    
    # 添加滚动条
    tree_scrollbar = ttk.Scrollbar(left_frame, orient=tk.VERTICAL, command=test_tree.yview)
    test_tree.configure(yscrollcommand=tree_scrollbar.set)
    
    test_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 右侧：日志和控制
    right_frame = ttk.Frame(content_frame)
    right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
    
    # 状态显示
    status_frame = ttk.Frame(right_frame)
    status_frame.pack(fill=tk.X, pady=(0, 10))
    
    status_var = tk.StringVar(value="准备开始测试...")
    status_label = ttk.Label(status_frame, textvariable=status_var, 
                            font=("Microsoft YaHei UI", 12, "bold"), 
                            foreground="blue")
    status_label.pack()
    
    # 日志显示
    log_frame = ttk.LabelFrame(right_frame, text="测试日志", padding="10")
    log_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
    
    log_text = tk.Text(log_frame, height=12, font=("Consolas", 9))
    log_scrollbar = ttk.Scrollbar(log_frame, orient=tk.VERTICAL, command=log_text.yview)
    log_text.configure(yscrollcommand=log_scrollbar.set)
    
    log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def log_message(message, color=None):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        if color:
            start_pos = log_text.index(tk.END + "-1c")
            log_text.insert(tk.END, log_entry)
            end_pos = log_text.index(tk.END + "-1c")
            log_text.tag_add(color, start_pos, end_pos)
            log_text.tag_config(color, foreground=color)
        else:
            log_text.insert(tk.END, log_entry)
        
        log_text.see(tk.END)
        root.update_idletasks()
    
    # 测试状态
    test_state = {
        "selected_process": "整机半成品功能测试",
        "test_count": 0,
        "running": False,
        "test_results": {}
    }
    
    # 初始化测试项目
    def init_test_projects():
        """初始化测试项目"""
        # 清空现有项目
        for item in test_tree.get_children():
            test_tree.delete(item)
        
        # 获取当前工序的测试项目
        if test_state["selected_process"] in work_processes:
            test_ids = work_processes[test_state["selected_process"]]["test_ids"]
            
            for test_id in test_ids:
                # 查找项目名称
                project_name = test_id
                for project in test_projects:
                    if project["id"] == test_id:
                        project_name = project["name"]
                        break
                
                # 创建测试项目
                item = test_tree.insert("", "end", text=project_name)
                test_tree.set(item, "data", "")
                test_tree.set(item, "result", "")
                
                # 初始化测试结果
                test_state["test_results"][test_id] = {
                    "name": project_name,
                    "status": "未测试",
                    "message": "",
                    "item": item
                }
    
    def update_test_item(test_id, status, message=""):
        """更新测试项目状态"""
        if test_id in test_state["test_results"]:
            result = test_state["test_results"][test_id]
            item = result["item"]
            
            result["status"] = status
            result["message"] = message
            
            test_tree.set(item, "data", message)
            
            if status == "通过":
                test_tree.set(item, "result", "PASS")
                test_tree.item(item, tags=("pass_result",))
            elif status == "失败":
                test_tree.set(item, "result", "FAIL")
                test_tree.item(item, tags=("fail_result",))
            elif status == "测试中":
                test_tree.set(item, "result", "测试中")
                test_tree.item(item, tags=("testing_result",))
            else:
                test_tree.set(item, "result", "")
                test_tree.item(item, tags=())
    
    def clear_test_results():
        """清空测试结果（模拟开始新测试时的清空）"""
        log_message("清空测试项目状态，开始新测试", "blue")
        for test_id in test_state["test_results"]:
            update_test_item(test_id, "未测试", "")
    
    def run_single_test_simulation(test_id):
        """模拟单项测试"""
        if test_id not in test_state["test_results"]:
            return
        
        result = test_state["test_results"][test_id]
        test_name = result["name"]
        
        log_message(f"开始单项测试: {test_name}")
        update_test_item(test_id, "测试中", "正在测试...")
        
        def simulate_test():
            time.sleep(1.5)  # 模拟测试时间
            
            # 随机生成测试结果
            is_pass = random.random() > 0.2  # 80%通过率
            
            if is_pass:
                update_test_item(test_id, "通过", "测试通过")
                log_message(f"{test_name} - 通过 ✅", "green")
            else:
                update_test_item(test_id, "失败", "测试失败")
                log_message(f"{test_name} - 失败 ❌", "red")
        
        threading.Thread(target=simulate_test, daemon=True).start()
    
    def run_all_tests_simulation():
        """模拟全部测试"""
        log_message("=== 开始全部测试 ===", "blue")
        status_var.set("正在执行全部测试...")
        test_state["running"] = True
        
        def simulate_all_tests():
            test_ids = list(test_state["test_results"].keys())
            
            for i, test_id in enumerate(test_ids):
                if not test_state["running"]:
                    break
                
                result = test_state["test_results"][test_id]
                test_name = result["name"]
                
                log_message(f"[{i+1}/{len(test_ids)}] 测试 {test_name}")
                update_test_item(test_id, "测试中", "正在测试...")
                
                time.sleep(0.5)  # 模拟测试时间
                
                # 随机生成测试结果
                is_pass = random.random() > 0.15  # 85%通过率
                
                if is_pass:
                    update_test_item(test_id, "通过", "测试通过")
                    log_message(f"{test_name} - 通过 ✅", "green")
                else:
                    update_test_item(test_id, "失败", "测试失败")
                    log_message(f"{test_name} - 失败 ❌", "red")
            
            if test_state["running"]:
                # 统计结果
                passed = sum(1 for r in test_state["test_results"].values() if r["status"] == "通过")
                failed = sum(1 for r in test_state["test_results"].values() if r["status"] == "失败")
                total = len(test_state["test_results"])
                
                log_message(f"全部测试完成 - 通过: {passed}, 失败: {failed}, 总计: {total}")
                status_var.set(f"测试完成 - 通过率: {passed/total*100:.1f}%")
                
                test_state["running"] = False
        
        threading.Thread(target=simulate_all_tests, daemon=True).start()
    
    # 绑定双击事件
    def on_double_click(event):
        """双击测试项目"""
        item = test_tree.selection()
        if item:
            test_name = test_tree.item(item[0], "text")
            # 查找对应的test_id
            for test_id, result in test_state["test_results"].items():
                if result["name"] == test_name:
                    run_single_test_simulation(test_id)
                    break
    
    test_tree.bind("<Double-Button-1>", on_double_click)
    
    # 创建右键菜单
    context_menu = tk.Menu(root, tearoff=0)
    context_menu.add_command(label="运行测试", command=lambda: on_double_click(None))
    
    def show_context_menu(event):
        """显示右键菜单"""
        item = test_tree.selection()
        if item:
            context_menu.post(event.x_root, event.y_root)
    
    test_tree.bind("<Button-3>", show_context_menu)
    
    # 控制按钮
    control_frame = ttk.Frame(right_frame)
    control_frame.pack(fill=tk.X)
    
    def stop_test():
        """停止测试"""
        test_state["running"] = False
        log_message("测试已停止")
        status_var.set("测试已停止")
    
    def reset_demo():
        """重置演示"""
        test_state["running"] = False
        test_state["test_count"] = 0
        log_text.delete(1.0, tk.END)
        status_var.set("准备开始测试...")
        init_test_projects()
        log_message("演示已重置")
    
    ttk.Button(control_frame, text="开始全部测试", 
              command=run_all_tests_simulation, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="清空测试结果", 
              command=clear_test_results, width=15).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="停止测试", 
              command=stop_test, width=12).pack(side=tk.LEFT, padx=5)
    ttk.Button(control_frame, text="重置演示", 
              command=reset_demo, width=12).pack(side=tk.RIGHT, padx=5)
    
    # 说明文档
    instruction_frame = ttk.LabelFrame(main_frame, text="操作说明", padding="10")
    instruction_frame.pack(fill=tk.X)
    
    instruction_text = """📋 操作说明:

1. 单项测试:
   • 双击测试项目可以单独运行该项目
   • 右键点击测试项目也可以运行单项测试

2. 全部测试:
   • 点击"开始全部测试"运行所有测试项目
   • 测试过程中可以点击"停止测试"中断

3. 数据保留验证:
   • 运行测试后，测试项目状态会保留
   • 点击"清空测试结果"模拟开始新测试时的清空行为
   • 在实际程序中，只有开始新测试时才会清空项目状态

4. 结果查看:
   • 绿色PASS表示测试通过
   • 红色FAIL表示测试失败
   • 黄色"测试中"表示正在测试"""
    
    ttk.Label(instruction_frame, text=instruction_text, justify=tk.LEFT, 
             font=("Microsoft YaHei UI", 9)).pack(fill=tk.X)
    
    # 初始化
    init_test_projects()
    log_message("单项测试功能和数据保留测试已准备就绪")
    log_message("双击测试项目可以进行单项测试")
    log_message("测试完成后项目状态会保留，便于查看结果")
    
    print("单项测试功能和数据保留测试界面已创建")
    root.mainloop()

if __name__ == "__main__":
    print("单项测试功能和数据保留测试")
    print("=" * 50)
    
    print("🔧 验证内容:")
    print("  1. 测试项目状态在回到序列号输入时不清除")
    print("  2. 测试项目状态在下一次开始测试后清除")
    print("  3. 单项测试功能正常工作（双击或右键）")
    print("  4. 可以查看上一次测试的具体项目结果")
    
    # 创建测试界面
    test_single_test_and_data_preservation()
    
    print("\n测试完成")
