{"sn": "999999999999999999", "timestamp": "2025-07-18T10:49:20.542615", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-18T10:47:55.491267"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-18T10:47:55.781052"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-18T10:47:56.495023"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-18T10:47:57.009750"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-18T10:48:00.469128"}, "4g_test": {"test_name": "4G模组测试", "status": "通过", "message": "CCID: 89860114851012535241", "timestamp": "2025-07-18T10:48:10.899859"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-18T10:48:15.487636"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-18T10:48:32.667616"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-18T10:48:34.962766"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-18T10:48:36.464952"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T10:48:36.559849"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-18T10:48:39.437115"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T10:48:46.494210"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-18T10:48:49.566542"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 40:55:48:A4:B8:E8", "timestamp": "2025-07-18T10:49:03.879255"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "平均延时: 51.29ms (良好)", "timestamp": "2025-07-18T10:49:04.434640"}}}