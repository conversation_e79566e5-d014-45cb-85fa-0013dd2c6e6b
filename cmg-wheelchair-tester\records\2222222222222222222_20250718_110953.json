{"sn": "2222222222222222222", "timestamp": "2025-07-18T11:09:53.830794", "results": {"device_connection": {"test_name": "设备连接状态检测", "status": "通过", "message": "已连接 1 个设备", "timestamp": "2025-07-18T11:07:41.111926"}, "rom_version_test": {"test_name": "3568版本测试", "status": "通过", "message": "RK3568 - Jul 9 12:01:12", "timestamp": "2025-07-18T11:07:41.653888"}, "usb_test": {"test_name": "USB关键器件检测", "status": "通过", "message": "检测到9个USB设备", "timestamp": "2025-07-18T11:07:42.410275"}, "can_test": {"test_name": "CAN0测试", "status": "通过", "message": "接收到: ID: 1234ABCD DATA: AA 00 CC AA 55 66 AA FF", "timestamp": "2025-07-18T11:07:43.037366"}, "gps_test": {"test_name": "GPS测试", "status": "失败", "message": "错误: Command '['adb', 'shell', 'cat /dev/ttyUSB4 |grep GPGSV']' timed out after 10 seconds", "timestamp": "2025-07-18T11:07:46.755596"}, "4g_test": {"test_name": "4G模组测试", "status": "通过", "message": "CCID: 89860114851012535241", "timestamp": "2025-07-18T11:07:57.450198"}, "key_test": {"test_name": "按键测试", "status": "通过", "message": "按键测试完成，检测到8个按键", "timestamp": "2025-07-18T11:08:02.275765"}, "led_test": {"test_name": "按键灯测试", "status": "通过", "message": "有背光", "timestamp": "2025-07-18T11:08:50.209591"}, "torch_test": {"test_name": "手电筒测试", "status": "通过", "message": "手电筒正常", "timestamp": "2025-07-18T11:08:52.419064"}, "joystick_test": {"test_name": "摇杆使能测试", "status": "通过", "message": "值: 255", "timestamp": "2025-07-18T11:08:54.061863"}, "front_camera_test": {"test_name": "前摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T11:08:54.677919"}, "light_sensor_test": {"test_name": "光感测试", "status": "通过", "message": "光感正常", "timestamp": "2025-07-18T11:08:57.973985"}, "back_camera_test": {"test_name": "回充摄像头测试", "status": "通过", "message": "图片质量正常", "timestamp": "2025-07-18T11:09:04.020399"}, "speaker_test": {"test_name": "喇叭测试", "status": "通过", "message": "音频播放完成", "timestamp": "2025-07-18T11:09:06.919983"}, "bluetooth_test": {"test_name": "蓝牙测试", "status": "通过", "message": "MAC: 24:21:5E:C0:30:4A", "timestamp": "2025-07-18T11:09:21.482167"}, "4g_network_test": {"test_name": "4G网络测试", "status": "通过", "message": "平均延时: 148.6ms", "timestamp": "2025-07-18T11:09:22.030662"}, "wifi_test": {"test_name": "WiFi测试", "status": "通过", "message": "平均延时: 84.89ms (良好)", "timestamp": "2025-07-18T11:09:37.149406"}}}